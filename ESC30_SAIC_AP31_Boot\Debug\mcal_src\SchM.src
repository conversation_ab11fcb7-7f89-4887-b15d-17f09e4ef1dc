	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc28072a -c99 --dep-file=mcal_src\\.SchM.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\SchM.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\SchM.src ..\\mcal_src\\SchM.c"
	.compiler_name		"ctc"
	.name	"SchM"

	
$TC16X
	
	.sdecl	'.text.SchM.SchM_Enter_Adc_StartGroup',code,cluster('SchM_Enter_Adc_StartGroup')
	.sect	'.text.SchM.SchM_Enter_Adc_StartGroup'
	.align	2
	
	.global	SchM_Enter_Adc_StartGroup

; ..\mcal_src\SchM.c	     1  /*******************************************************************************
; ..\mcal_src\SchM.c	     2  **                                                                            **
; ..\mcal_src\SchM.c	     3  ** Copyright (C) Infineon Technologies (2013)                                 **
; ..\mcal_src\SchM.c	     4  **                                                                            **
; ..\mcal_src\SchM.c	     5  ** All rights reserved.                                                       **
; ..\mcal_src\SchM.c	     6  **                                                                            **
; ..\mcal_src\SchM.c	     7  ** This document contains proprietary information belonging to Infineon       **
; ..\mcal_src\SchM.c	     8  ** Technologies. Passing on and copying of this document, and communication   **
; ..\mcal_src\SchM.c	     9  ** of its contents is not permitted without prior written authorization.      **
; ..\mcal_src\SchM.c	    10  **                                                                            **
; ..\mcal_src\SchM.c	    11  ********************************************************************************
; ..\mcal_src\SchM.c	    12  **                                                                            **
; ..\mcal_src\SchM.c	    13  **   $FILENAME   : SchM.c $                                                   **
; ..\mcal_src\SchM.c	    14  **                                                                            **
; ..\mcal_src\SchM.c	    15  **   $CC VERSION : \main\42 $                                                 **
; ..\mcal_src\SchM.c	    16  **                                                                            **
; ..\mcal_src\SchM.c	    17  **   $DATE       : 2018-05-09 $                                               **
; ..\mcal_src\SchM.c	    18  **                                                                            **
; ..\mcal_src\SchM.c	    19  **   AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\SchM.c	    20  **                                                                            **
; ..\mcal_src\SchM.c	    21  **   VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\SchM.c	    22  **                                                                            **
; ..\mcal_src\SchM.c	    23  **   DESCRIPTION : This file contains                                         **
; ..\mcal_src\SchM.c	    24  **                 - stub for OS functionality.                               **
; ..\mcal_src\SchM.c	    25  **                                                                            **
; ..\mcal_src\SchM.c	    26  **                                                                            **
; ..\mcal_src\SchM.c	    27  **   MAY BE CHANGED BY USER [yes/no]: yes                                     **
; ..\mcal_src\SchM.c	    28  **                                                                            **
; ..\mcal_src\SchM.c	    29  *******************************************************************************/
; ..\mcal_src\SchM.c	    30  
; ..\mcal_src\SchM.c	    31  /*******************************************************************************
; ..\mcal_src\SchM.c	    32  **                      Includes                                              **
; ..\mcal_src\SchM.c	    33  *******************************************************************************/
; ..\mcal_src\SchM.c	    34  
; ..\mcal_src\SchM.c	    35  /* Inclusion of Platform_Types.h and Compiler.h */
; ..\mcal_src\SchM.c	    36  #include "Std_Types.h"
; ..\mcal_src\SchM.c	    37  #include "McalOsConfig.h"
; ..\mcal_src\SchM.c	    38  #include MCAL_OS_HEADER
; ..\mcal_src\SchM.c	    39  
; ..\mcal_src\SchM.c	    40  /*******************************************************************************
; ..\mcal_src\SchM.c	    41  **                      Imported Compiler Switch Checks                       **
; ..\mcal_src\SchM.c	    42  *******************************************************************************/
; ..\mcal_src\SchM.c	    43  
; ..\mcal_src\SchM.c	    44  /*******************************************************************************
; ..\mcal_src\SchM.c	    45  **                      Private Macro Definitions                             **
; ..\mcal_src\SchM.c	    46  *******************************************************************************/
; ..\mcal_src\SchM.c	    47  
; ..\mcal_src\SchM.c	    48  /*******************************************************************************
; ..\mcal_src\SchM.c	    49  **                      Private Type Definitions                              **
; ..\mcal_src\SchM.c	    50  *******************************************************************************/
; ..\mcal_src\SchM.c	    51  
; ..\mcal_src\SchM.c	    52  
; ..\mcal_src\SchM.c	    53  /*******************************************************************************
; ..\mcal_src\SchM.c	    54  **                      Private Function Declarations                         **
; ..\mcal_src\SchM.c	    55  *******************************************************************************/
; ..\mcal_src\SchM.c	    56  
; ..\mcal_src\SchM.c	    57  
; ..\mcal_src\SchM.c	    58  /*******************************************************************************
; ..\mcal_src\SchM.c	    59  **                      Global Constant Definitions                           **
; ..\mcal_src\SchM.c	    60  *******************************************************************************/
; ..\mcal_src\SchM.c	    61  
; ..\mcal_src\SchM.c	    62  
; ..\mcal_src\SchM.c	    63  /*******************************************************************************
; ..\mcal_src\SchM.c	    64  **                      Global Variable Definitions                           **
; ..\mcal_src\SchM.c	    65  *******************************************************************************/
; ..\mcal_src\SchM.c	    66  
; ..\mcal_src\SchM.c	    67  
; ..\mcal_src\SchM.c	    68  /*******************************************************************************
; ..\mcal_src\SchM.c	    69  **                      Private Constant Definitions                          **
; ..\mcal_src\SchM.c	    70  *******************************************************************************/
; ..\mcal_src\SchM.c	    71  
; ..\mcal_src\SchM.c	    72  
; ..\mcal_src\SchM.c	    73  /*******************************************************************************
; ..\mcal_src\SchM.c	    74  **                      Private Variable Definitions                          **
; ..\mcal_src\SchM.c	    75  *******************************************************************************/
; ..\mcal_src\SchM.c	    76  
; ..\mcal_src\SchM.c	    77  /*******************************************************************************
; ..\mcal_src\SchM.c	    78  **                      Private Function Definitions                          **
; ..\mcal_src\SchM.c	    79  *******************************************************************************/
; ..\mcal_src\SchM.c	    80  
; ..\mcal_src\SchM.c	    81  
; ..\mcal_src\SchM.c	    82  /*******************************************************************************
; ..\mcal_src\SchM.c	    83  **                      Global Function Definitions                           **
; ..\mcal_src\SchM.c	    84  *******************************************************************************/
; ..\mcal_src\SchM.c	    85  
; ..\mcal_src\SchM.c	    86  /********************************ADC*******************************************/
; ..\mcal_src\SchM.c	    87  
; ..\mcal_src\SchM.c	    88  void SchM_Enter_Adc_StartGroup(void)
; Function SchM_Enter_Adc_StartGroup
.L3:
SchM_Enter_Adc_StartGroup:	.type	func

; ..\mcal_src\SchM.c	    89  {
; ..\mcal_src\SchM.c	    90    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L706:
	
__SchM_Enter_Adc_StartGroup_function_end:
	.size	SchM_Enter_Adc_StartGroup,__SchM_Enter_Adc_StartGroup_function_end-SchM_Enter_Adc_StartGroup
.L210:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Adc_StopGroup',code,cluster('SchM_Enter_Adc_StopGroup')
	.sect	'.text.SchM.SchM_Enter_Adc_StopGroup'
	.align	2
	
	.global	SchM_Enter_Adc_StopGroup

; ..\mcal_src\SchM.c	    91  }
; ..\mcal_src\SchM.c	    92  
; ..\mcal_src\SchM.c	    93  void SchM_Enter_Adc_StopGroup(void)
; Function SchM_Enter_Adc_StopGroup
.L5:
SchM_Enter_Adc_StopGroup:	.type	func

; ..\mcal_src\SchM.c	    94  {
; ..\mcal_src\SchM.c	    95    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L707:
	
__SchM_Enter_Adc_StopGroup_function_end:
	.size	SchM_Enter_Adc_StopGroup,__SchM_Enter_Adc_StopGroup_function_end-SchM_Enter_Adc_StopGroup
.L215:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Adc_ReadGroup',code,cluster('SchM_Enter_Adc_ReadGroup')
	.sect	'.text.SchM.SchM_Enter_Adc_ReadGroup'
	.align	2
	
	.global	SchM_Enter_Adc_ReadGroup

; ..\mcal_src\SchM.c	    96  }
; ..\mcal_src\SchM.c	    97  
; ..\mcal_src\SchM.c	    98  void SchM_Enter_Adc_ReadGroup(void)
; Function SchM_Enter_Adc_ReadGroup
.L7:
SchM_Enter_Adc_ReadGroup:	.type	func

; ..\mcal_src\SchM.c	    99  {
; ..\mcal_src\SchM.c	   100    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L708:
	
__SchM_Enter_Adc_ReadGroup_function_end:
	.size	SchM_Enter_Adc_ReadGroup,__SchM_Enter_Adc_ReadGroup_function_end-SchM_Enter_Adc_ReadGroup
.L220:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Adc_EnableHwTrig',code,cluster('SchM_Enter_Adc_EnableHwTrig')
	.sect	'.text.SchM.SchM_Enter_Adc_EnableHwTrig'
	.align	2
	
	.global	SchM_Enter_Adc_EnableHwTrig

; ..\mcal_src\SchM.c	   101  }
; ..\mcal_src\SchM.c	   102  
; ..\mcal_src\SchM.c	   103  void SchM_Enter_Adc_EnableHwTrig(void)
; Function SchM_Enter_Adc_EnableHwTrig
.L9:
SchM_Enter_Adc_EnableHwTrig:	.type	func

; ..\mcal_src\SchM.c	   104  {
; ..\mcal_src\SchM.c	   105    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L709:
	
__SchM_Enter_Adc_EnableHwTrig_function_end:
	.size	SchM_Enter_Adc_EnableHwTrig,__SchM_Enter_Adc_EnableHwTrig_function_end-SchM_Enter_Adc_EnableHwTrig
.L225:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Adc_DisableHwTrig',code,cluster('SchM_Enter_Adc_DisableHwTrig')
	.sect	'.text.SchM.SchM_Enter_Adc_DisableHwTrig'
	.align	2
	
	.global	SchM_Enter_Adc_DisableHwTrig

; ..\mcal_src\SchM.c	   106  }
; ..\mcal_src\SchM.c	   107  
; ..\mcal_src\SchM.c	   108  void SchM_Enter_Adc_DisableHwTrig(void)
; Function SchM_Enter_Adc_DisableHwTrig
.L11:
SchM_Enter_Adc_DisableHwTrig:	.type	func

; ..\mcal_src\SchM.c	   109  {
; ..\mcal_src\SchM.c	   110    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L710:
	
__SchM_Enter_Adc_DisableHwTrig_function_end:
	.size	SchM_Enter_Adc_DisableHwTrig,__SchM_Enter_Adc_DisableHwTrig_function_end-SchM_Enter_Adc_DisableHwTrig
.L230:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Adc_GetGrpStatus',code,cluster('SchM_Enter_Adc_GetGrpStatus')
	.sect	'.text.SchM.SchM_Enter_Adc_GetGrpStatus'
	.align	2
	
	.global	SchM_Enter_Adc_GetGrpStatus

; ..\mcal_src\SchM.c	   111  }
; ..\mcal_src\SchM.c	   112  
; ..\mcal_src\SchM.c	   113  void SchM_Enter_Adc_GetGrpStatus(void)
; Function SchM_Enter_Adc_GetGrpStatus
.L13:
SchM_Enter_Adc_GetGrpStatus:	.type	func

; ..\mcal_src\SchM.c	   114  {
; ..\mcal_src\SchM.c	   115    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L711:
	
__SchM_Enter_Adc_GetGrpStatus_function_end:
	.size	SchM_Enter_Adc_GetGrpStatus,__SchM_Enter_Adc_GetGrpStatus_function_end-SchM_Enter_Adc_GetGrpStatus
.L235:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Adc_GetStreamLastPtr',code,cluster('SchM_Enter_Adc_GetStreamLastPtr')
	.sect	'.text.SchM.SchM_Enter_Adc_GetStreamLastPtr'
	.align	2
	
	.global	SchM_Enter_Adc_GetStreamLastPtr

; ..\mcal_src\SchM.c	   116  }
; ..\mcal_src\SchM.c	   117  
; ..\mcal_src\SchM.c	   118  void SchM_Enter_Adc_GetStreamLastPtr(void)
; Function SchM_Enter_Adc_GetStreamLastPtr
.L15:
SchM_Enter_Adc_GetStreamLastPtr:	.type	func

; ..\mcal_src\SchM.c	   119  {
; ..\mcal_src\SchM.c	   120    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L712:
	
__SchM_Enter_Adc_GetStreamLastPtr_function_end:
	.size	SchM_Enter_Adc_GetStreamLastPtr,__SchM_Enter_Adc_GetStreamLastPtr_function_end-SchM_Enter_Adc_GetStreamLastPtr
.L240:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Adc_ScheduleStart',code,cluster('SchM_Enter_Adc_ScheduleStart')
	.sect	'.text.SchM.SchM_Enter_Adc_ScheduleStart'
	.align	2
	
	.global	SchM_Enter_Adc_ScheduleStart

; ..\mcal_src\SchM.c	   121  }
; ..\mcal_src\SchM.c	   122  
; ..\mcal_src\SchM.c	   123  void SchM_Enter_Adc_ScheduleStart(void)
; Function SchM_Enter_Adc_ScheduleStart
.L17:
SchM_Enter_Adc_ScheduleStart:	.type	func

; ..\mcal_src\SchM.c	   124  {
; ..\mcal_src\SchM.c	   125    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L713:
	
__SchM_Enter_Adc_ScheduleStart_function_end:
	.size	SchM_Enter_Adc_ScheduleStart,__SchM_Enter_Adc_ScheduleStart_function_end-SchM_Enter_Adc_ScheduleStart
.L245:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Adc_ScheduleStop',code,cluster('SchM_Enter_Adc_ScheduleStop')
	.sect	'.text.SchM.SchM_Enter_Adc_ScheduleStop'
	.align	2
	
	.global	SchM_Enter_Adc_ScheduleStop

; ..\mcal_src\SchM.c	   126  }
; ..\mcal_src\SchM.c	   127  
; ..\mcal_src\SchM.c	   128  void SchM_Enter_Adc_ScheduleStop(void)
; Function SchM_Enter_Adc_ScheduleStop
.L19:
SchM_Enter_Adc_ScheduleStop:	.type	func

; ..\mcal_src\SchM.c	   129  {
; ..\mcal_src\SchM.c	   130    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L714:
	
__SchM_Enter_Adc_ScheduleStop_function_end:
	.size	SchM_Enter_Adc_ScheduleStop,__SchM_Enter_Adc_ScheduleStop_function_end-SchM_Enter_Adc_ScheduleStop
.L250:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Adc_ScheduleNext',code,cluster('SchM_Enter_Adc_ScheduleNext')
	.sect	'.text.SchM.SchM_Enter_Adc_ScheduleNext'
	.align	2
	
	.global	SchM_Enter_Adc_ScheduleNext

; ..\mcal_src\SchM.c	   131  }
; ..\mcal_src\SchM.c	   132  
; ..\mcal_src\SchM.c	   133  void SchM_Enter_Adc_ScheduleNext(void)
; Function SchM_Enter_Adc_ScheduleNext
.L21:
SchM_Enter_Adc_ScheduleNext:	.type	func

; ..\mcal_src\SchM.c	   134  {
; ..\mcal_src\SchM.c	   135    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L715:
	
__SchM_Enter_Adc_ScheduleNext_function_end:
	.size	SchM_Enter_Adc_ScheduleNext,__SchM_Enter_Adc_ScheduleNext_function_end-SchM_Enter_Adc_ScheduleNext
.L255:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Adc_PushQueue',code,cluster('SchM_Enter_Adc_PushQueue')
	.sect	'.text.SchM.SchM_Enter_Adc_PushQueue'
	.align	2
	
	.global	SchM_Enter_Adc_PushQueue

; ..\mcal_src\SchM.c	   136  }
; ..\mcal_src\SchM.c	   137  
; ..\mcal_src\SchM.c	   138  void SchM_Enter_Adc_PushQueue(void)
; Function SchM_Enter_Adc_PushQueue
.L23:
SchM_Enter_Adc_PushQueue:	.type	func

; ..\mcal_src\SchM.c	   139  {
; ..\mcal_src\SchM.c	   140    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L716:
	
__SchM_Enter_Adc_PushQueue_function_end:
	.size	SchM_Enter_Adc_PushQueue,__SchM_Enter_Adc_PushQueue_function_end-SchM_Enter_Adc_PushQueue
.L260:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Adc_PopQueue',code,cluster('SchM_Enter_Adc_PopQueue')
	.sect	'.text.SchM.SchM_Enter_Adc_PopQueue'
	.align	2
	
	.global	SchM_Enter_Adc_PopQueue

; ..\mcal_src\SchM.c	   141  }
; ..\mcal_src\SchM.c	   142  
; ..\mcal_src\SchM.c	   143  void SchM_Enter_Adc_PopQueue(void)
; Function SchM_Enter_Adc_PopQueue
.L25:
SchM_Enter_Adc_PopQueue:	.type	func

; ..\mcal_src\SchM.c	   144  {
; ..\mcal_src\SchM.c	   145    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L717:
	
__SchM_Enter_Adc_PopQueue_function_end:
	.size	SchM_Enter_Adc_PopQueue,__SchM_Enter_Adc_PopQueue_function_end-SchM_Enter_Adc_PopQueue
.L265:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Adc_StartGroup',code,cluster('SchM_Exit_Adc_StartGroup')
	.sect	'.text.SchM.SchM_Exit_Adc_StartGroup'
	.align	2
	
	.global	SchM_Exit_Adc_StartGroup

; ..\mcal_src\SchM.c	   146  }
; ..\mcal_src\SchM.c	   147  
; ..\mcal_src\SchM.c	   148  void SchM_Exit_Adc_StartGroup(void)
; Function SchM_Exit_Adc_StartGroup
.L27:
SchM_Exit_Adc_StartGroup:	.type	func

; ..\mcal_src\SchM.c	   149  {
; ..\mcal_src\SchM.c	   150    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L718:
	
__SchM_Exit_Adc_StartGroup_function_end:
	.size	SchM_Exit_Adc_StartGroup,__SchM_Exit_Adc_StartGroup_function_end-SchM_Exit_Adc_StartGroup
.L270:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Adc_StopGroup',code,cluster('SchM_Exit_Adc_StopGroup')
	.sect	'.text.SchM.SchM_Exit_Adc_StopGroup'
	.align	2
	
	.global	SchM_Exit_Adc_StopGroup

; ..\mcal_src\SchM.c	   151  }
; ..\mcal_src\SchM.c	   152  
; ..\mcal_src\SchM.c	   153  void SchM_Exit_Adc_StopGroup(void)
; Function SchM_Exit_Adc_StopGroup
.L29:
SchM_Exit_Adc_StopGroup:	.type	func

; ..\mcal_src\SchM.c	   154  {
; ..\mcal_src\SchM.c	   155    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L719:
	
__SchM_Exit_Adc_StopGroup_function_end:
	.size	SchM_Exit_Adc_StopGroup,__SchM_Exit_Adc_StopGroup_function_end-SchM_Exit_Adc_StopGroup
.L275:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Adc_ReadGroup',code,cluster('SchM_Exit_Adc_ReadGroup')
	.sect	'.text.SchM.SchM_Exit_Adc_ReadGroup'
	.align	2
	
	.global	SchM_Exit_Adc_ReadGroup

; ..\mcal_src\SchM.c	   156  }
; ..\mcal_src\SchM.c	   157  
; ..\mcal_src\SchM.c	   158  void SchM_Exit_Adc_ReadGroup(void)
; Function SchM_Exit_Adc_ReadGroup
.L31:
SchM_Exit_Adc_ReadGroup:	.type	func

; ..\mcal_src\SchM.c	   159  {
; ..\mcal_src\SchM.c	   160    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L720:
	
__SchM_Exit_Adc_ReadGroup_function_end:
	.size	SchM_Exit_Adc_ReadGroup,__SchM_Exit_Adc_ReadGroup_function_end-SchM_Exit_Adc_ReadGroup
.L280:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Adc_EnableHwTrig',code,cluster('SchM_Exit_Adc_EnableHwTrig')
	.sect	'.text.SchM.SchM_Exit_Adc_EnableHwTrig'
	.align	2
	
	.global	SchM_Exit_Adc_EnableHwTrig

; ..\mcal_src\SchM.c	   161  }
; ..\mcal_src\SchM.c	   162  
; ..\mcal_src\SchM.c	   163  void SchM_Exit_Adc_EnableHwTrig(void)
; Function SchM_Exit_Adc_EnableHwTrig
.L33:
SchM_Exit_Adc_EnableHwTrig:	.type	func

; ..\mcal_src\SchM.c	   164  {
; ..\mcal_src\SchM.c	   165    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L721:
	
__SchM_Exit_Adc_EnableHwTrig_function_end:
	.size	SchM_Exit_Adc_EnableHwTrig,__SchM_Exit_Adc_EnableHwTrig_function_end-SchM_Exit_Adc_EnableHwTrig
.L285:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Adc_DisableHwTrig',code,cluster('SchM_Exit_Adc_DisableHwTrig')
	.sect	'.text.SchM.SchM_Exit_Adc_DisableHwTrig'
	.align	2
	
	.global	SchM_Exit_Adc_DisableHwTrig

; ..\mcal_src\SchM.c	   166  }
; ..\mcal_src\SchM.c	   167  
; ..\mcal_src\SchM.c	   168  void SchM_Exit_Adc_DisableHwTrig(void)
; Function SchM_Exit_Adc_DisableHwTrig
.L35:
SchM_Exit_Adc_DisableHwTrig:	.type	func

; ..\mcal_src\SchM.c	   169  {
; ..\mcal_src\SchM.c	   170    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L722:
	
__SchM_Exit_Adc_DisableHwTrig_function_end:
	.size	SchM_Exit_Adc_DisableHwTrig,__SchM_Exit_Adc_DisableHwTrig_function_end-SchM_Exit_Adc_DisableHwTrig
.L290:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Adc_GetGrpStatus',code,cluster('SchM_Exit_Adc_GetGrpStatus')
	.sect	'.text.SchM.SchM_Exit_Adc_GetGrpStatus'
	.align	2
	
	.global	SchM_Exit_Adc_GetGrpStatus

; ..\mcal_src\SchM.c	   171  }
; ..\mcal_src\SchM.c	   172  
; ..\mcal_src\SchM.c	   173  void SchM_Exit_Adc_GetGrpStatus(void)
; Function SchM_Exit_Adc_GetGrpStatus
.L37:
SchM_Exit_Adc_GetGrpStatus:	.type	func

; ..\mcal_src\SchM.c	   174  {
; ..\mcal_src\SchM.c	   175    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L723:
	
__SchM_Exit_Adc_GetGrpStatus_function_end:
	.size	SchM_Exit_Adc_GetGrpStatus,__SchM_Exit_Adc_GetGrpStatus_function_end-SchM_Exit_Adc_GetGrpStatus
.L295:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Adc_GetStreamLastPtr',code,cluster('SchM_Exit_Adc_GetStreamLastPtr')
	.sect	'.text.SchM.SchM_Exit_Adc_GetStreamLastPtr'
	.align	2
	
	.global	SchM_Exit_Adc_GetStreamLastPtr

; ..\mcal_src\SchM.c	   176  }
; ..\mcal_src\SchM.c	   177  
; ..\mcal_src\SchM.c	   178  void SchM_Exit_Adc_GetStreamLastPtr(void)
; Function SchM_Exit_Adc_GetStreamLastPtr
.L39:
SchM_Exit_Adc_GetStreamLastPtr:	.type	func

; ..\mcal_src\SchM.c	   179  {
; ..\mcal_src\SchM.c	   180    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L724:
	
__SchM_Exit_Adc_GetStreamLastPtr_function_end:
	.size	SchM_Exit_Adc_GetStreamLastPtr,__SchM_Exit_Adc_GetStreamLastPtr_function_end-SchM_Exit_Adc_GetStreamLastPtr
.L300:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Adc_ScheduleStart',code,cluster('SchM_Exit_Adc_ScheduleStart')
	.sect	'.text.SchM.SchM_Exit_Adc_ScheduleStart'
	.align	2
	
	.global	SchM_Exit_Adc_ScheduleStart

; ..\mcal_src\SchM.c	   181  }
; ..\mcal_src\SchM.c	   182  
; ..\mcal_src\SchM.c	   183  void SchM_Exit_Adc_ScheduleStart(void)
; Function SchM_Exit_Adc_ScheduleStart
.L41:
SchM_Exit_Adc_ScheduleStart:	.type	func

; ..\mcal_src\SchM.c	   184  {
; ..\mcal_src\SchM.c	   185    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L725:
	
__SchM_Exit_Adc_ScheduleStart_function_end:
	.size	SchM_Exit_Adc_ScheduleStart,__SchM_Exit_Adc_ScheduleStart_function_end-SchM_Exit_Adc_ScheduleStart
.L305:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Adc_ScheduleStop',code,cluster('SchM_Exit_Adc_ScheduleStop')
	.sect	'.text.SchM.SchM_Exit_Adc_ScheduleStop'
	.align	2
	
	.global	SchM_Exit_Adc_ScheduleStop

; ..\mcal_src\SchM.c	   186  }
; ..\mcal_src\SchM.c	   187  
; ..\mcal_src\SchM.c	   188  void SchM_Exit_Adc_ScheduleStop(void)
; Function SchM_Exit_Adc_ScheduleStop
.L43:
SchM_Exit_Adc_ScheduleStop:	.type	func

; ..\mcal_src\SchM.c	   189  {
; ..\mcal_src\SchM.c	   190    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L726:
	
__SchM_Exit_Adc_ScheduleStop_function_end:
	.size	SchM_Exit_Adc_ScheduleStop,__SchM_Exit_Adc_ScheduleStop_function_end-SchM_Exit_Adc_ScheduleStop
.L310:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Adc_ScheduleNext',code,cluster('SchM_Exit_Adc_ScheduleNext')
	.sect	'.text.SchM.SchM_Exit_Adc_ScheduleNext'
	.align	2
	
	.global	SchM_Exit_Adc_ScheduleNext

; ..\mcal_src\SchM.c	   191  }
; ..\mcal_src\SchM.c	   192  
; ..\mcal_src\SchM.c	   193  void SchM_Exit_Adc_ScheduleNext(void)
; Function SchM_Exit_Adc_ScheduleNext
.L45:
SchM_Exit_Adc_ScheduleNext:	.type	func

; ..\mcal_src\SchM.c	   194  {
; ..\mcal_src\SchM.c	   195    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L727:
	
__SchM_Exit_Adc_ScheduleNext_function_end:
	.size	SchM_Exit_Adc_ScheduleNext,__SchM_Exit_Adc_ScheduleNext_function_end-SchM_Exit_Adc_ScheduleNext
.L315:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Adc_PushQueue',code,cluster('SchM_Exit_Adc_PushQueue')
	.sect	'.text.SchM.SchM_Exit_Adc_PushQueue'
	.align	2
	
	.global	SchM_Exit_Adc_PushQueue

; ..\mcal_src\SchM.c	   196  }
; ..\mcal_src\SchM.c	   197  
; ..\mcal_src\SchM.c	   198  void SchM_Exit_Adc_PushQueue(void)
; Function SchM_Exit_Adc_PushQueue
.L47:
SchM_Exit_Adc_PushQueue:	.type	func

; ..\mcal_src\SchM.c	   199  {
; ..\mcal_src\SchM.c	   200    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L728:
	
__SchM_Exit_Adc_PushQueue_function_end:
	.size	SchM_Exit_Adc_PushQueue,__SchM_Exit_Adc_PushQueue_function_end-SchM_Exit_Adc_PushQueue
.L320:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Adc_PopQueue',code,cluster('SchM_Exit_Adc_PopQueue')
	.sect	'.text.SchM.SchM_Exit_Adc_PopQueue'
	.align	2
	
	.global	SchM_Exit_Adc_PopQueue

; ..\mcal_src\SchM.c	   201  }
; ..\mcal_src\SchM.c	   202  
; ..\mcal_src\SchM.c	   203  void SchM_Exit_Adc_PopQueue(void)
; Function SchM_Exit_Adc_PopQueue
.L49:
SchM_Exit_Adc_PopQueue:	.type	func

; ..\mcal_src\SchM.c	   204  {
; ..\mcal_src\SchM.c	   205    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L729:
	
__SchM_Exit_Adc_PopQueue_function_end:
	.size	SchM_Exit_Adc_PopQueue,__SchM_Exit_Adc_PopQueue_function_end-SchM_Exit_Adc_PopQueue
.L325:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Icu_17_GtmCcu6_EnableWakeup',code,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.text.SchM.SchM_Enter_Icu_17_GtmCcu6_EnableWakeup'
	.align	2
	
	.global	SchM_Enter_Icu_17_GtmCcu6_EnableWakeup

; ..\mcal_src\SchM.c	   206  }
; ..\mcal_src\SchM.c	   207  
; ..\mcal_src\SchM.c	   208  /********************************ICU*******************************************/
; ..\mcal_src\SchM.c	   209  void SchM_Enter_Icu_17_GtmCcu6_EnableWakeup(void)
; Function SchM_Enter_Icu_17_GtmCcu6_EnableWakeup
.L51:
SchM_Enter_Icu_17_GtmCcu6_EnableWakeup:	.type	func

; ..\mcal_src\SchM.c	   210  {
; ..\mcal_src\SchM.c	   211    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L730:
	
__SchM_Enter_Icu_17_GtmCcu6_EnableWakeup_function_end:
	.size	SchM_Enter_Icu_17_GtmCcu6_EnableWakeup,__SchM_Enter_Icu_17_GtmCcu6_EnableWakeup_function_end-SchM_Enter_Icu_17_GtmCcu6_EnableWakeup
.L330:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Icu_17_GtmCcu6_EnableWakeup',code,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.text.SchM.SchM_Exit_Icu_17_GtmCcu6_EnableWakeup'
	.align	2
	
	.global	SchM_Exit_Icu_17_GtmCcu6_EnableWakeup

; ..\mcal_src\SchM.c	   212  }
; ..\mcal_src\SchM.c	   213  
; ..\mcal_src\SchM.c	   214  void SchM_Exit_Icu_17_GtmCcu6_EnableWakeup(void)
; Function SchM_Exit_Icu_17_GtmCcu6_EnableWakeup
.L53:
SchM_Exit_Icu_17_GtmCcu6_EnableWakeup:	.type	func

; ..\mcal_src\SchM.c	   215  {
; ..\mcal_src\SchM.c	   216    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L731:
	
__SchM_Exit_Icu_17_GtmCcu6_EnableWakeup_function_end:
	.size	SchM_Exit_Icu_17_GtmCcu6_EnableWakeup,__SchM_Exit_Icu_17_GtmCcu6_EnableWakeup_function_end-SchM_Exit_Icu_17_GtmCcu6_EnableWakeup
.L335:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Icu_17_GtmCcu6_EnableNotification',code,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.text.SchM.SchM_Enter_Icu_17_GtmCcu6_EnableNotification'
	.align	2
	
	.global	SchM_Enter_Icu_17_GtmCcu6_EnableNotification

; ..\mcal_src\SchM.c	   217  }
; ..\mcal_src\SchM.c	   218  
; ..\mcal_src\SchM.c	   219  void SchM_Enter_Icu_17_GtmCcu6_EnableNotification(void)
; Function SchM_Enter_Icu_17_GtmCcu6_EnableNotification
.L55:
SchM_Enter_Icu_17_GtmCcu6_EnableNotification:	.type	func

; ..\mcal_src\SchM.c	   220  {
; ..\mcal_src\SchM.c	   221    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L732:
	
__SchM_Enter_Icu_17_GtmCcu6_EnableNotification_function_end:
	.size	SchM_Enter_Icu_17_GtmCcu6_EnableNotification,__SchM_Enter_Icu_17_GtmCcu6_EnableNotification_function_end-SchM_Enter_Icu_17_GtmCcu6_EnableNotification
.L340:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Icu_17_GtmCcu6_EnableNotification',code,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.text.SchM.SchM_Exit_Icu_17_GtmCcu6_EnableNotification'
	.align	2
	
	.global	SchM_Exit_Icu_17_GtmCcu6_EnableNotification

; ..\mcal_src\SchM.c	   222  }
; ..\mcal_src\SchM.c	   223  
; ..\mcal_src\SchM.c	   224  void SchM_Exit_Icu_17_GtmCcu6_EnableNotification(void)
; Function SchM_Exit_Icu_17_GtmCcu6_EnableNotification
.L57:
SchM_Exit_Icu_17_GtmCcu6_EnableNotification:	.type	func

; ..\mcal_src\SchM.c	   225  {
; ..\mcal_src\SchM.c	   226    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L733:
	
__SchM_Exit_Icu_17_GtmCcu6_EnableNotification_function_end:
	.size	SchM_Exit_Icu_17_GtmCcu6_EnableNotification,__SchM_Exit_Icu_17_GtmCcu6_EnableNotification_function_end-SchM_Exit_Icu_17_GtmCcu6_EnableNotification
.L345:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount',code,cluster('SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.text.SchM.SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount'
	.align	2
	
	.global	SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount

; ..\mcal_src\SchM.c	   227  }
; ..\mcal_src\SchM.c	   228  
; ..\mcal_src\SchM.c	   229  void SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount(void)
; Function SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount
.L59:
SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount:	.type	func

; ..\mcal_src\SchM.c	   230  {
; ..\mcal_src\SchM.c	   231    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L734:
	
__SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount_function_end:
	.size	SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount,__SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount_function_end-SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount
.L350:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount',code,cluster('SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.text.SchM.SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount'
	.align	2
	
	.global	SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount

; ..\mcal_src\SchM.c	   232  }
; ..\mcal_src\SchM.c	   233  
; ..\mcal_src\SchM.c	   234  void SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount(void)
; Function SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount
.L61:
SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount:	.type	func

; ..\mcal_src\SchM.c	   235  {
; ..\mcal_src\SchM.c	   236    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L735:
	
__SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount_function_end:
	.size	SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount,__SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount_function_end-SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount
.L355:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate',code,cluster('SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.text.SchM.SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate'
	.align	2
	
	.global	SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate

; ..\mcal_src\SchM.c	   237  }
; ..\mcal_src\SchM.c	   238  
; ..\mcal_src\SchM.c	   239  void SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate(void)
; Function SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate
.L63:
SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate:	.type	func

; ..\mcal_src\SchM.c	   240  {
; ..\mcal_src\SchM.c	   241    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L736:
	
__SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate_function_end:
	.size	SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate,__SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate_function_end-SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate
.L360:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate',code,cluster('SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.text.SchM.SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate'
	.align	2
	
	.global	SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate

; ..\mcal_src\SchM.c	   242  }
; ..\mcal_src\SchM.c	   243  
; ..\mcal_src\SchM.c	   244  void SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate(void)
; Function SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate
.L65:
SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate:	.type	func

; ..\mcal_src\SchM.c	   245  {
; ..\mcal_src\SchM.c	   246    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L737:
	
__SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate_function_end:
	.size	SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate,__SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate_function_end-SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate
.L365:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle',code,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.text.SchM.SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle'
	.align	2
	
	.global	SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle

; ..\mcal_src\SchM.c	   247  }
; ..\mcal_src\SchM.c	   248  
; ..\mcal_src\SchM.c	   249  void SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle(void)
; Function SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle
.L67:
SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle:	.type	func

; ..\mcal_src\SchM.c	   250  {
; ..\mcal_src\SchM.c	   251    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L738:
	
__SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle_function_end:
	.size	SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle,__SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle_function_end-SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle
.L370:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle',code,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.text.SchM.SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle'
	.align	2
	
	.global	SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle

; ..\mcal_src\SchM.c	   252  }
; ..\mcal_src\SchM.c	   253  
; ..\mcal_src\SchM.c	   254  void SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle(void)
; Function SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle
.L69:
SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle:	.type	func

; ..\mcal_src\SchM.c	   255  {
; ..\mcal_src\SchM.c	   256    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L739:
	
__SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle_function_end:
	.size	SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle,__SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle_function_end-SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle
.L375:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate',code,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.text.SchM.SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate'
	.align	2
	
	.global	SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate

; ..\mcal_src\SchM.c	   257  }
; ..\mcal_src\SchM.c	   258  
; ..\mcal_src\SchM.c	   259  void SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate(void)
; Function SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate
.L71:
SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate:	.type	func

; ..\mcal_src\SchM.c	   260  {
; ..\mcal_src\SchM.c	   261    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L740:
	
__SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate_function_end:
	.size	SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate,__SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate_function_end-SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate
.L380:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate',code,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.text.SchM.SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate'
	.align	2
	
	.global	SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate

; ..\mcal_src\SchM.c	   262  }
; ..\mcal_src\SchM.c	   263  
; ..\mcal_src\SchM.c	   264  void SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate(void)
; Function SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate
.L73:
SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate:	.type	func

; ..\mcal_src\SchM.c	   265  {
; ..\mcal_src\SchM.c	   266    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L741:
	
__SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate_function_end:
	.size	SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate,__SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate_function_end-SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate
.L385:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Can_17_MCanP_CanDisInt',code,cluster('SchM_Enter_Can_17_MCanP_CanDisInt')
	.sect	'.text.SchM.SchM_Enter_Can_17_MCanP_CanDisInt'
	.align	2
	
	.global	SchM_Enter_Can_17_MCanP_CanDisInt

; ..\mcal_src\SchM.c	   267  }
; ..\mcal_src\SchM.c	   268  
; ..\mcal_src\SchM.c	   269  /********************************CAN*******************************************/
; ..\mcal_src\SchM.c	   270  
; ..\mcal_src\SchM.c	   271  void SchM_Enter_Can_17_MCanP_CanDisInt(void)
; Function SchM_Enter_Can_17_MCanP_CanDisInt
.L75:
SchM_Enter_Can_17_MCanP_CanDisInt:	.type	func

; ..\mcal_src\SchM.c	   272  {
; ..\mcal_src\SchM.c	   273    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L742:
	
__SchM_Enter_Can_17_MCanP_CanDisInt_function_end:
	.size	SchM_Enter_Can_17_MCanP_CanDisInt,__SchM_Enter_Can_17_MCanP_CanDisInt_function_end-SchM_Enter_Can_17_MCanP_CanDisInt
.L390:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Can_17_MCanP_CanDisInt',code,cluster('SchM_Exit_Can_17_MCanP_CanDisInt')
	.sect	'.text.SchM.SchM_Exit_Can_17_MCanP_CanDisInt'
	.align	2
	
	.global	SchM_Exit_Can_17_MCanP_CanDisInt

; ..\mcal_src\SchM.c	   274  }
; ..\mcal_src\SchM.c	   275  
; ..\mcal_src\SchM.c	   276  void SchM_Exit_Can_17_MCanP_CanDisInt(void)
; Function SchM_Exit_Can_17_MCanP_CanDisInt
.L77:
SchM_Exit_Can_17_MCanP_CanDisInt:	.type	func

; ..\mcal_src\SchM.c	   277  {
; ..\mcal_src\SchM.c	   278    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L743:
	
__SchM_Exit_Can_17_MCanP_CanDisInt_function_end:
	.size	SchM_Exit_Can_17_MCanP_CanDisInt,__SchM_Exit_Can_17_MCanP_CanDisInt_function_end-SchM_Exit_Can_17_MCanP_CanDisInt
.L395:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Can_17_MCanP_CanEnInt',code,cluster('SchM_Enter_Can_17_MCanP_CanEnInt')
	.sect	'.text.SchM.SchM_Enter_Can_17_MCanP_CanEnInt'
	.align	2
	
	.global	SchM_Enter_Can_17_MCanP_CanEnInt

; ..\mcal_src\SchM.c	   279  }
; ..\mcal_src\SchM.c	   280  
; ..\mcal_src\SchM.c	   281  void SchM_Enter_Can_17_MCanP_CanEnInt(void)
; Function SchM_Enter_Can_17_MCanP_CanEnInt
.L79:
SchM_Enter_Can_17_MCanP_CanEnInt:	.type	func

; ..\mcal_src\SchM.c	   282  {
; ..\mcal_src\SchM.c	   283    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L744:
	
__SchM_Enter_Can_17_MCanP_CanEnInt_function_end:
	.size	SchM_Enter_Can_17_MCanP_CanEnInt,__SchM_Enter_Can_17_MCanP_CanEnInt_function_end-SchM_Enter_Can_17_MCanP_CanEnInt
.L400:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Can_17_MCanP_CanEnInt',code,cluster('SchM_Exit_Can_17_MCanP_CanEnInt')
	.sect	'.text.SchM.SchM_Exit_Can_17_MCanP_CanEnInt'
	.align	2
	
	.global	SchM_Exit_Can_17_MCanP_CanEnInt

; ..\mcal_src\SchM.c	   284  }
; ..\mcal_src\SchM.c	   285  
; ..\mcal_src\SchM.c	   286  void SchM_Exit_Can_17_MCanP_CanEnInt(void)
; Function SchM_Exit_Can_17_MCanP_CanEnInt
.L81:
SchM_Exit_Can_17_MCanP_CanEnInt:	.type	func

; ..\mcal_src\SchM.c	   287  {
; ..\mcal_src\SchM.c	   288    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L745:
	
__SchM_Exit_Can_17_MCanP_CanEnInt_function_end:
	.size	SchM_Exit_Can_17_MCanP_CanEnInt,__SchM_Exit_Can_17_MCanP_CanEnInt_function_end-SchM_Exit_Can_17_MCanP_CanEnInt
.L405:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Can_17_MCanP_CanWrMO',code,cluster('SchM_Enter_Can_17_MCanP_CanWrMO')
	.sect	'.text.SchM.SchM_Enter_Can_17_MCanP_CanWrMO'
	.align	2
	
	.global	SchM_Enter_Can_17_MCanP_CanWrMO

; ..\mcal_src\SchM.c	   289  }
; ..\mcal_src\SchM.c	   290  
; ..\mcal_src\SchM.c	   291  void SchM_Enter_Can_17_MCanP_CanWrMO(void)
; Function SchM_Enter_Can_17_MCanP_CanWrMO
.L83:
SchM_Enter_Can_17_MCanP_CanWrMO:	.type	func

; ..\mcal_src\SchM.c	   292  {
; ..\mcal_src\SchM.c	   293    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L746:
	
__SchM_Enter_Can_17_MCanP_CanWrMO_function_end:
	.size	SchM_Enter_Can_17_MCanP_CanWrMO,__SchM_Enter_Can_17_MCanP_CanWrMO_function_end-SchM_Enter_Can_17_MCanP_CanWrMO
.L410:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Can_17_MCanP_CanWrMO',code,cluster('SchM_Exit_Can_17_MCanP_CanWrMO')
	.sect	'.text.SchM.SchM_Exit_Can_17_MCanP_CanWrMO'
	.align	2
	
	.global	SchM_Exit_Can_17_MCanP_CanWrMO

; ..\mcal_src\SchM.c	   294  }
; ..\mcal_src\SchM.c	   295  
; ..\mcal_src\SchM.c	   296  void SchM_Exit_Can_17_MCanP_CanWrMO(void)
; Function SchM_Exit_Can_17_MCanP_CanWrMO
.L85:
SchM_Exit_Can_17_MCanP_CanWrMO:	.type	func

; ..\mcal_src\SchM.c	   297  {
; ..\mcal_src\SchM.c	   298    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L747:
	
__SchM_Exit_Can_17_MCanP_CanWrMO_function_end:
	.size	SchM_Exit_Can_17_MCanP_CanWrMO,__SchM_Exit_Can_17_MCanP_CanWrMO_function_end-SchM_Exit_Can_17_MCanP_CanWrMO
.L415:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Fr_17_Eray_ControllerInit',code,cluster('SchM_Enter_Fr_17_Eray_ControllerInit')
	.sect	'.text.SchM.SchM_Enter_Fr_17_Eray_ControllerInit'
	.align	2
	
	.global	SchM_Enter_Fr_17_Eray_ControllerInit

; ..\mcal_src\SchM.c	   299  }
; ..\mcal_src\SchM.c	   300  
; ..\mcal_src\SchM.c	   301  /********************************FlexRay***************************************/
; ..\mcal_src\SchM.c	   302  
; ..\mcal_src\SchM.c	   303  void SchM_Enter_Fr_17_Eray_ControllerInit(void)
; Function SchM_Enter_Fr_17_Eray_ControllerInit
.L87:
SchM_Enter_Fr_17_Eray_ControllerInit:	.type	func

; ..\mcal_src\SchM.c	   304  {
; ..\mcal_src\SchM.c	   305    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L748:
	
__SchM_Enter_Fr_17_Eray_ControllerInit_function_end:
	.size	SchM_Enter_Fr_17_Eray_ControllerInit,__SchM_Enter_Fr_17_Eray_ControllerInit_function_end-SchM_Enter_Fr_17_Eray_ControllerInit
.L420:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Fr_17_Eray_ControllerInit',code,cluster('SchM_Exit_Fr_17_Eray_ControllerInit')
	.sect	'.text.SchM.SchM_Exit_Fr_17_Eray_ControllerInit'
	.align	2
	
	.global	SchM_Exit_Fr_17_Eray_ControllerInit

; ..\mcal_src\SchM.c	   306  }
; ..\mcal_src\SchM.c	   307  
; ..\mcal_src\SchM.c	   308  void SchM_Exit_Fr_17_Eray_ControllerInit(void)
; Function SchM_Exit_Fr_17_Eray_ControllerInit
.L89:
SchM_Exit_Fr_17_Eray_ControllerInit:	.type	func

; ..\mcal_src\SchM.c	   309  {
; ..\mcal_src\SchM.c	   310    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L749:
	
__SchM_Exit_Fr_17_Eray_ControllerInit_function_end:
	.size	SchM_Exit_Fr_17_Eray_ControllerInit,__SchM_Exit_Fr_17_Eray_ControllerInit_function_end-SchM_Exit_Fr_17_Eray_ControllerInit
.L425:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Fr_17_Eray_SetWakeupChannel',code,cluster('SchM_Enter_Fr_17_Eray_SetWakeupChannel')
	.sect	'.text.SchM.SchM_Enter_Fr_17_Eray_SetWakeupChannel'
	.align	2
	
	.global	SchM_Enter_Fr_17_Eray_SetWakeupChannel

; ..\mcal_src\SchM.c	   311  }
; ..\mcal_src\SchM.c	   312  
; ..\mcal_src\SchM.c	   313  void SchM_Enter_Fr_17_Eray_SetWakeupChannel(void)
; Function SchM_Enter_Fr_17_Eray_SetWakeupChannel
.L91:
SchM_Enter_Fr_17_Eray_SetWakeupChannel:	.type	func

; ..\mcal_src\SchM.c	   314  {
; ..\mcal_src\SchM.c	   315    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L750:
	
__SchM_Enter_Fr_17_Eray_SetWakeupChannel_function_end:
	.size	SchM_Enter_Fr_17_Eray_SetWakeupChannel,__SchM_Enter_Fr_17_Eray_SetWakeupChannel_function_end-SchM_Enter_Fr_17_Eray_SetWakeupChannel
.L430:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Fr_17_Eray_SetWakeupChannel',code,cluster('SchM_Exit_Fr_17_Eray_SetWakeupChannel')
	.sect	'.text.SchM.SchM_Exit_Fr_17_Eray_SetWakeupChannel'
	.align	2
	
	.global	SchM_Exit_Fr_17_Eray_SetWakeupChannel

; ..\mcal_src\SchM.c	   316  }
; ..\mcal_src\SchM.c	   317  
; ..\mcal_src\SchM.c	   318  void SchM_Exit_Fr_17_Eray_SetWakeupChannel(void)
; Function SchM_Exit_Fr_17_Eray_SetWakeupChannel
.L93:
SchM_Exit_Fr_17_Eray_SetWakeupChannel:	.type	func

; ..\mcal_src\SchM.c	   319  {
; ..\mcal_src\SchM.c	   320    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L751:
	
__SchM_Exit_Fr_17_Eray_SetWakeupChannel_function_end:
	.size	SchM_Exit_Fr_17_Eray_SetWakeupChannel,__SchM_Exit_Fr_17_Eray_SetWakeupChannel_function_end-SchM_Exit_Fr_17_Eray_SetWakeupChannel
.L435:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Spi_WriteIB',code,cluster('SchM_Enter_Spi_WriteIB')
	.sect	'.text.SchM.SchM_Enter_Spi_WriteIB'
	.align	2
	
	.global	SchM_Enter_Spi_WriteIB

; ..\mcal_src\SchM.c	   321  }
; ..\mcal_src\SchM.c	   322  
; ..\mcal_src\SchM.c	   323  /********************************SPI*******************************************/
; ..\mcal_src\SchM.c	   324  
; ..\mcal_src\SchM.c	   325  void SchM_Enter_Spi_WriteIB(void)
; Function SchM_Enter_Spi_WriteIB
.L95:
SchM_Enter_Spi_WriteIB:	.type	func

; ..\mcal_src\SchM.c	   326  {
; ..\mcal_src\SchM.c	   327    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L752:
	
__SchM_Enter_Spi_WriteIB_function_end:
	.size	SchM_Enter_Spi_WriteIB,__SchM_Enter_Spi_WriteIB_function_end-SchM_Enter_Spi_WriteIB
.L440:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Spi_WriteIB',code,cluster('SchM_Exit_Spi_WriteIB')
	.sect	'.text.SchM.SchM_Exit_Spi_WriteIB'
	.align	2
	
	.global	SchM_Exit_Spi_WriteIB

; ..\mcal_src\SchM.c	   328  }
; ..\mcal_src\SchM.c	   329  void SchM_Exit_Spi_WriteIB(void)
; Function SchM_Exit_Spi_WriteIB
.L97:
SchM_Exit_Spi_WriteIB:	.type	func

; ..\mcal_src\SchM.c	   330  {
; ..\mcal_src\SchM.c	   331    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L753:
	
__SchM_Exit_Spi_WriteIB_function_end:
	.size	SchM_Exit_Spi_WriteIB,__SchM_Exit_Spi_WriteIB_function_end-SchM_Exit_Spi_WriteIB
.L445:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Spi_AsyncTransmit',code,cluster('SchM_Enter_Spi_AsyncTransmit')
	.sect	'.text.SchM.SchM_Enter_Spi_AsyncTransmit'
	.align	2
	
	.global	SchM_Enter_Spi_AsyncTransmit

; ..\mcal_src\SchM.c	   332  }
; ..\mcal_src\SchM.c	   333  
; ..\mcal_src\SchM.c	   334  void SchM_Enter_Spi_AsyncTransmit(void)
; Function SchM_Enter_Spi_AsyncTransmit
.L99:
SchM_Enter_Spi_AsyncTransmit:	.type	func

; ..\mcal_src\SchM.c	   335  {
; ..\mcal_src\SchM.c	   336    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L754:
	
__SchM_Enter_Spi_AsyncTransmit_function_end:
	.size	SchM_Enter_Spi_AsyncTransmit,__SchM_Enter_Spi_AsyncTransmit_function_end-SchM_Enter_Spi_AsyncTransmit
.L450:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Spi_AsyncTransmit',code,cluster('SchM_Exit_Spi_AsyncTransmit')
	.sect	'.text.SchM.SchM_Exit_Spi_AsyncTransmit'
	.align	2
	
	.global	SchM_Exit_Spi_AsyncTransmit

; ..\mcal_src\SchM.c	   337  }
; ..\mcal_src\SchM.c	   338  void SchM_Exit_Spi_AsyncTransmit(void)
; Function SchM_Exit_Spi_AsyncTransmit
.L101:
SchM_Exit_Spi_AsyncTransmit:	.type	func

; ..\mcal_src\SchM.c	   339  {
; ..\mcal_src\SchM.c	   340    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L755:
	
__SchM_Exit_Spi_AsyncTransmit_function_end:
	.size	SchM_Exit_Spi_AsyncTransmit,__SchM_Exit_Spi_AsyncTransmit_function_end-SchM_Exit_Spi_AsyncTransmit
.L455:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Spi_GetSequenceResult',code,cluster('SchM_Enter_Spi_GetSequenceResult')
	.sect	'.text.SchM.SchM_Enter_Spi_GetSequenceResult'
	.align	2
	
	.global	SchM_Enter_Spi_GetSequenceResult

; ..\mcal_src\SchM.c	   341  }
; ..\mcal_src\SchM.c	   342  
; ..\mcal_src\SchM.c	   343  void SchM_Enter_Spi_GetSequenceResult(void)
; Function SchM_Enter_Spi_GetSequenceResult
.L103:
SchM_Enter_Spi_GetSequenceResult:	.type	func

; ..\mcal_src\SchM.c	   344  {
; ..\mcal_src\SchM.c	   345    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L756:
	
__SchM_Enter_Spi_GetSequenceResult_function_end:
	.size	SchM_Enter_Spi_GetSequenceResult,__SchM_Enter_Spi_GetSequenceResult_function_end-SchM_Enter_Spi_GetSequenceResult
.L460:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Spi_GetSequenceResult',code,cluster('SchM_Exit_Spi_GetSequenceResult')
	.sect	'.text.SchM.SchM_Exit_Spi_GetSequenceResult'
	.align	2
	
	.global	SchM_Exit_Spi_GetSequenceResult

; ..\mcal_src\SchM.c	   346  }
; ..\mcal_src\SchM.c	   347  void SchM_Exit_Spi_GetSequenceResult(void)
; Function SchM_Exit_Spi_GetSequenceResult
.L105:
SchM_Exit_Spi_GetSequenceResult:	.type	func

; ..\mcal_src\SchM.c	   348  {
; ..\mcal_src\SchM.c	   349    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L757:
	
__SchM_Exit_Spi_GetSequenceResult_function_end:
	.size	SchM_Exit_Spi_GetSequenceResult,__SchM_Exit_Spi_GetSequenceResult_function_end-SchM_Exit_Spi_GetSequenceResult
.L465:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Spi_Cancel',code,cluster('SchM_Enter_Spi_Cancel')
	.sect	'.text.SchM.SchM_Enter_Spi_Cancel'
	.align	2
	
	.global	SchM_Enter_Spi_Cancel

; ..\mcal_src\SchM.c	   350  }
; ..\mcal_src\SchM.c	   351  void SchM_Enter_Spi_Cancel(void)
; Function SchM_Enter_Spi_Cancel
.L107:
SchM_Enter_Spi_Cancel:	.type	func

; ..\mcal_src\SchM.c	   352  {
; ..\mcal_src\SchM.c	   353    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L758:
	
__SchM_Enter_Spi_Cancel_function_end:
	.size	SchM_Enter_Spi_Cancel,__SchM_Enter_Spi_Cancel_function_end-SchM_Enter_Spi_Cancel
.L470:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Spi_Cancel',code,cluster('SchM_Exit_Spi_Cancel')
	.sect	'.text.SchM.SchM_Exit_Spi_Cancel'
	.align	2
	
	.global	SchM_Exit_Spi_Cancel

; ..\mcal_src\SchM.c	   354  }
; ..\mcal_src\SchM.c	   355  void SchM_Exit_Spi_Cancel(void)
; Function SchM_Exit_Spi_Cancel
.L109:
SchM_Exit_Spi_Cancel:	.type	func

; ..\mcal_src\SchM.c	   356  {
; ..\mcal_src\SchM.c	   357    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L759:
	
__SchM_Exit_Spi_Cancel_function_end:
	.size	SchM_Exit_Spi_Cancel,__SchM_Exit_Spi_Cancel_function_end-SchM_Exit_Spi_Cancel
.L475:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Spi_Init',code,cluster('SchM_Enter_Spi_Init')
	.sect	'.text.SchM.SchM_Enter_Spi_Init'
	.align	2
	
	.global	SchM_Enter_Spi_Init

; ..\mcal_src\SchM.c	   358  }
; ..\mcal_src\SchM.c	   359  void SchM_Enter_Spi_Init(void)
; Function SchM_Enter_Spi_Init
.L111:
SchM_Enter_Spi_Init:	.type	func

; ..\mcal_src\SchM.c	   360  {
; ..\mcal_src\SchM.c	   361    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L760:
	
__SchM_Enter_Spi_Init_function_end:
	.size	SchM_Enter_Spi_Init,__SchM_Enter_Spi_Init_function_end-SchM_Enter_Spi_Init
.L480:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Spi_Init',code,cluster('SchM_Exit_Spi_Init')
	.sect	'.text.SchM.SchM_Exit_Spi_Init'
	.align	2
	
	.global	SchM_Exit_Spi_Init

; ..\mcal_src\SchM.c	   362  }
; ..\mcal_src\SchM.c	   363  void SchM_Exit_Spi_Init(void)
; Function SchM_Exit_Spi_Init
.L113:
SchM_Exit_Spi_Init:	.type	func

; ..\mcal_src\SchM.c	   364  {
; ..\mcal_src\SchM.c	   365    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L761:
	
__SchM_Exit_Spi_Init_function_end:
	.size	SchM_Exit_Spi_Init,__SchM_Exit_Spi_Init_function_end-SchM_Exit_Spi_Init
.L485:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Spi_DeInit',code,cluster('SchM_Enter_Spi_DeInit')
	.sect	'.text.SchM.SchM_Enter_Spi_DeInit'
	.align	2
	
	.global	SchM_Enter_Spi_DeInit

; ..\mcal_src\SchM.c	   366  }
; ..\mcal_src\SchM.c	   367  void SchM_Enter_Spi_DeInit(void)
; Function SchM_Enter_Spi_DeInit
.L115:
SchM_Enter_Spi_DeInit:	.type	func

; ..\mcal_src\SchM.c	   368  {
; ..\mcal_src\SchM.c	   369    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L762:
	
__SchM_Enter_Spi_DeInit_function_end:
	.size	SchM_Enter_Spi_DeInit,__SchM_Enter_Spi_DeInit_function_end-SchM_Enter_Spi_DeInit
.L490:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Spi_DeInit',code,cluster('SchM_Exit_Spi_DeInit')
	.sect	'.text.SchM.SchM_Exit_Spi_DeInit'
	.align	2
	
	.global	SchM_Exit_Spi_DeInit

; ..\mcal_src\SchM.c	   370  }
; ..\mcal_src\SchM.c	   371  void SchM_Exit_Spi_DeInit(void)
; Function SchM_Exit_Spi_DeInit
.L117:
SchM_Exit_Spi_DeInit:	.type	func

; ..\mcal_src\SchM.c	   372  {
; ..\mcal_src\SchM.c	   373    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L763:
	
__SchM_Exit_Spi_DeInit_function_end:
	.size	SchM_Exit_Spi_DeInit,__SchM_Exit_Spi_DeInit_function_end-SchM_Exit_Spi_DeInit
.L495:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Spi_SyncTransmit',code,cluster('SchM_Enter_Spi_SyncTransmit')
	.sect	'.text.SchM.SchM_Enter_Spi_SyncTransmit'
	.align	2
	
	.global	SchM_Enter_Spi_SyncTransmit

; ..\mcal_src\SchM.c	   374  }
; ..\mcal_src\SchM.c	   375  void SchM_Enter_Spi_SyncTransmit(void)
; Function SchM_Enter_Spi_SyncTransmit
.L119:
SchM_Enter_Spi_SyncTransmit:	.type	func

; ..\mcal_src\SchM.c	   376  {
; ..\mcal_src\SchM.c	   377    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L764:
	
__SchM_Enter_Spi_SyncTransmit_function_end:
	.size	SchM_Enter_Spi_SyncTransmit,__SchM_Enter_Spi_SyncTransmit_function_end-SchM_Enter_Spi_SyncTransmit
.L500:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Spi_SyncTransmit',code,cluster('SchM_Exit_Spi_SyncTransmit')
	.sect	'.text.SchM.SchM_Exit_Spi_SyncTransmit'
	.align	2
	
	.global	SchM_Exit_Spi_SyncTransmit

; ..\mcal_src\SchM.c	   378  }
; ..\mcal_src\SchM.c	   379  void SchM_Exit_Spi_SyncTransmit(void)
; Function SchM_Exit_Spi_SyncTransmit
.L121:
SchM_Exit_Spi_SyncTransmit:	.type	func

; ..\mcal_src\SchM.c	   380  {
; ..\mcal_src\SchM.c	   381    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L765:
	
__SchM_Exit_Spi_SyncTransmit_function_end:
	.size	SchM_Exit_Spi_SyncTransmit,__SchM_Exit_Spi_SyncTransmit_function_end-SchM_Exit_Spi_SyncTransmit
.L505:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Wdg_17_Scu_TimerHandling',code,cluster('SchM_Enter_Wdg_17_Scu_TimerHandling')
	.sect	'.text.SchM.SchM_Enter_Wdg_17_Scu_TimerHandling'
	.align	2
	
	.global	SchM_Enter_Wdg_17_Scu_TimerHandling

; ..\mcal_src\SchM.c	   382  }
; ..\mcal_src\SchM.c	   383  
; ..\mcal_src\SchM.c	   384  
; ..\mcal_src\SchM.c	   385  /********************************WatchDog**************************************/
; ..\mcal_src\SchM.c	   386  
; ..\mcal_src\SchM.c	   387  
; ..\mcal_src\SchM.c	   388  void SchM_Enter_Wdg_17_Scu_TimerHandling(void)
; Function SchM_Enter_Wdg_17_Scu_TimerHandling
.L123:
SchM_Enter_Wdg_17_Scu_TimerHandling:	.type	func

; ..\mcal_src\SchM.c	   389  {
; ..\mcal_src\SchM.c	   390    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L766:
	
__SchM_Enter_Wdg_17_Scu_TimerHandling_function_end:
	.size	SchM_Enter_Wdg_17_Scu_TimerHandling,__SchM_Enter_Wdg_17_Scu_TimerHandling_function_end-SchM_Enter_Wdg_17_Scu_TimerHandling
.L510:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Wdg_17_Scu_Trigger',code,cluster('SchM_Enter_Wdg_17_Scu_Trigger')
	.sect	'.text.SchM.SchM_Enter_Wdg_17_Scu_Trigger'
	.align	2
	
	.global	SchM_Enter_Wdg_17_Scu_Trigger

; ..\mcal_src\SchM.c	   391  }
; ..\mcal_src\SchM.c	   392  void SchM_Enter_Wdg_17_Scu_Trigger(void)
; Function SchM_Enter_Wdg_17_Scu_Trigger
.L125:
SchM_Enter_Wdg_17_Scu_Trigger:	.type	func

; ..\mcal_src\SchM.c	   393  {
; ..\mcal_src\SchM.c	   394    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L767:
	
__SchM_Enter_Wdg_17_Scu_Trigger_function_end:
	.size	SchM_Enter_Wdg_17_Scu_Trigger,__SchM_Enter_Wdg_17_Scu_Trigger_function_end-SchM_Enter_Wdg_17_Scu_Trigger
.L515:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Wdg_17_Scu_ChangeMode',code,cluster('SchM_Enter_Wdg_17_Scu_ChangeMode')
	.sect	'.text.SchM.SchM_Enter_Wdg_17_Scu_ChangeMode'
	.align	2
	
	.global	SchM_Enter_Wdg_17_Scu_ChangeMode

; ..\mcal_src\SchM.c	   395  }
; ..\mcal_src\SchM.c	   396  void SchM_Enter_Wdg_17_Scu_ChangeMode(void)
; Function SchM_Enter_Wdg_17_Scu_ChangeMode
.L127:
SchM_Enter_Wdg_17_Scu_ChangeMode:	.type	func

; ..\mcal_src\SchM.c	   397  {
; ..\mcal_src\SchM.c	   398    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L768:
	
__SchM_Enter_Wdg_17_Scu_ChangeMode_function_end:
	.size	SchM_Enter_Wdg_17_Scu_ChangeMode,__SchM_Enter_Wdg_17_Scu_ChangeMode_function_end-SchM_Enter_Wdg_17_Scu_ChangeMode
.L520:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Wdg_17_Scu_SafetyInit',code,cluster('SchM_Enter_Wdg_17_Scu_SafetyInit')
	.sect	'.text.SchM.SchM_Enter_Wdg_17_Scu_SafetyInit'
	.align	2
	
	.global	SchM_Enter_Wdg_17_Scu_SafetyInit

; ..\mcal_src\SchM.c	   399  }
; ..\mcal_src\SchM.c	   400  void SchM_Enter_Wdg_17_Scu_SafetyInit(void)
; Function SchM_Enter_Wdg_17_Scu_SafetyInit
.L129:
SchM_Enter_Wdg_17_Scu_SafetyInit:	.type	func

; ..\mcal_src\SchM.c	   401  {
; ..\mcal_src\SchM.c	   402    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L769:
	
__SchM_Enter_Wdg_17_Scu_SafetyInit_function_end:
	.size	SchM_Enter_Wdg_17_Scu_SafetyInit,__SchM_Enter_Wdg_17_Scu_SafetyInit_function_end-SchM_Enter_Wdg_17_Scu_SafetyInit
.L525:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Wdg_17_Scu_SafetyTrigger',code,cluster('SchM_Enter_Wdg_17_Scu_SafetyTrigger')
	.sect	'.text.SchM.SchM_Enter_Wdg_17_Scu_SafetyTrigger'
	.align	2
	
	.global	SchM_Enter_Wdg_17_Scu_SafetyTrigger

; ..\mcal_src\SchM.c	   403  } 
; ..\mcal_src\SchM.c	   404  void SchM_Enter_Wdg_17_Scu_SafetyTrigger(void)
; Function SchM_Enter_Wdg_17_Scu_SafetyTrigger
.L131:
SchM_Enter_Wdg_17_Scu_SafetyTrigger:	.type	func

; ..\mcal_src\SchM.c	   405  {
; ..\mcal_src\SchM.c	   406    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L770:
	
__SchM_Enter_Wdg_17_Scu_SafetyTrigger_function_end:
	.size	SchM_Enter_Wdg_17_Scu_SafetyTrigger,__SchM_Enter_Wdg_17_Scu_SafetyTrigger_function_end-SchM_Enter_Wdg_17_Scu_SafetyTrigger
.L530:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Wdg_17_Scu_SafetyOffMode',code,cluster('SchM_Enter_Wdg_17_Scu_SafetyOffMode')
	.sect	'.text.SchM.SchM_Enter_Wdg_17_Scu_SafetyOffMode'
	.align	2
	
	.global	SchM_Enter_Wdg_17_Scu_SafetyOffMode

; ..\mcal_src\SchM.c	   407  }
; ..\mcal_src\SchM.c	   408  void SchM_Enter_Wdg_17_Scu_SafetyOffMode(void)
; Function SchM_Enter_Wdg_17_Scu_SafetyOffMode
.L133:
SchM_Enter_Wdg_17_Scu_SafetyOffMode:	.type	func

; ..\mcal_src\SchM.c	   409  {
; ..\mcal_src\SchM.c	   410    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L771:
	
__SchM_Enter_Wdg_17_Scu_SafetyOffMode_function_end:
	.size	SchM_Enter_Wdg_17_Scu_SafetyOffMode,__SchM_Enter_Wdg_17_Scu_SafetyOffMode_function_end-SchM_Enter_Wdg_17_Scu_SafetyOffMode
.L535:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Wdg_17_Scu_TimerHandling',code,cluster('SchM_Exit_Wdg_17_Scu_TimerHandling')
	.sect	'.text.SchM.SchM_Exit_Wdg_17_Scu_TimerHandling'
	.align	2
	
	.global	SchM_Exit_Wdg_17_Scu_TimerHandling

; ..\mcal_src\SchM.c	   411  }
; ..\mcal_src\SchM.c	   412  
; ..\mcal_src\SchM.c	   413  void SchM_Exit_Wdg_17_Scu_TimerHandling(void)
; Function SchM_Exit_Wdg_17_Scu_TimerHandling
.L135:
SchM_Exit_Wdg_17_Scu_TimerHandling:	.type	func

; ..\mcal_src\SchM.c	   414  {
; ..\mcal_src\SchM.c	   415    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L772:
	
__SchM_Exit_Wdg_17_Scu_TimerHandling_function_end:
	.size	SchM_Exit_Wdg_17_Scu_TimerHandling,__SchM_Exit_Wdg_17_Scu_TimerHandling_function_end-SchM_Exit_Wdg_17_Scu_TimerHandling
.L540:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Wdg_17_Scu_Trigger',code,cluster('SchM_Exit_Wdg_17_Scu_Trigger')
	.sect	'.text.SchM.SchM_Exit_Wdg_17_Scu_Trigger'
	.align	2
	
	.global	SchM_Exit_Wdg_17_Scu_Trigger

; ..\mcal_src\SchM.c	   416  }
; ..\mcal_src\SchM.c	   417  void SchM_Exit_Wdg_17_Scu_Trigger(void)
; Function SchM_Exit_Wdg_17_Scu_Trigger
.L137:
SchM_Exit_Wdg_17_Scu_Trigger:	.type	func

; ..\mcal_src\SchM.c	   418  {
; ..\mcal_src\SchM.c	   419    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L773:
	
__SchM_Exit_Wdg_17_Scu_Trigger_function_end:
	.size	SchM_Exit_Wdg_17_Scu_Trigger,__SchM_Exit_Wdg_17_Scu_Trigger_function_end-SchM_Exit_Wdg_17_Scu_Trigger
.L545:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Wdg_17_Scu_ChangeMode',code,cluster('SchM_Exit_Wdg_17_Scu_ChangeMode')
	.sect	'.text.SchM.SchM_Exit_Wdg_17_Scu_ChangeMode'
	.align	2
	
	.global	SchM_Exit_Wdg_17_Scu_ChangeMode

; ..\mcal_src\SchM.c	   420  }
; ..\mcal_src\SchM.c	   421  void SchM_Exit_Wdg_17_Scu_ChangeMode(void)
; Function SchM_Exit_Wdg_17_Scu_ChangeMode
.L139:
SchM_Exit_Wdg_17_Scu_ChangeMode:	.type	func

; ..\mcal_src\SchM.c	   422  {
; ..\mcal_src\SchM.c	   423    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L774:
	
__SchM_Exit_Wdg_17_Scu_ChangeMode_function_end:
	.size	SchM_Exit_Wdg_17_Scu_ChangeMode,__SchM_Exit_Wdg_17_Scu_ChangeMode_function_end-SchM_Exit_Wdg_17_Scu_ChangeMode
.L550:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Wdg_17_Scu_SafetyInit',code,cluster('SchM_Exit_Wdg_17_Scu_SafetyInit')
	.sect	'.text.SchM.SchM_Exit_Wdg_17_Scu_SafetyInit'
	.align	2
	
	.global	SchM_Exit_Wdg_17_Scu_SafetyInit

; ..\mcal_src\SchM.c	   424  }
; ..\mcal_src\SchM.c	   425  void SchM_Exit_Wdg_17_Scu_SafetyInit(void)
; Function SchM_Exit_Wdg_17_Scu_SafetyInit
.L141:
SchM_Exit_Wdg_17_Scu_SafetyInit:	.type	func

; ..\mcal_src\SchM.c	   426  {
; ..\mcal_src\SchM.c	   427    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L775:
	
__SchM_Exit_Wdg_17_Scu_SafetyInit_function_end:
	.size	SchM_Exit_Wdg_17_Scu_SafetyInit,__SchM_Exit_Wdg_17_Scu_SafetyInit_function_end-SchM_Exit_Wdg_17_Scu_SafetyInit
.L555:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Wdg_17_Scu_SafetyTrigger',code,cluster('SchM_Exit_Wdg_17_Scu_SafetyTrigger')
	.sect	'.text.SchM.SchM_Exit_Wdg_17_Scu_SafetyTrigger'
	.align	2
	
	.global	SchM_Exit_Wdg_17_Scu_SafetyTrigger

; ..\mcal_src\SchM.c	   428  } 
; ..\mcal_src\SchM.c	   429  void SchM_Exit_Wdg_17_Scu_SafetyTrigger(void)
; Function SchM_Exit_Wdg_17_Scu_SafetyTrigger
.L143:
SchM_Exit_Wdg_17_Scu_SafetyTrigger:	.type	func

; ..\mcal_src\SchM.c	   430  {
; ..\mcal_src\SchM.c	   431    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L776:
	
__SchM_Exit_Wdg_17_Scu_SafetyTrigger_function_end:
	.size	SchM_Exit_Wdg_17_Scu_SafetyTrigger,__SchM_Exit_Wdg_17_Scu_SafetyTrigger_function_end-SchM_Exit_Wdg_17_Scu_SafetyTrigger
.L560:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Wdg_17_Scu_SafetyOffMode',code,cluster('SchM_Exit_Wdg_17_Scu_SafetyOffMode')
	.sect	'.text.SchM.SchM_Exit_Wdg_17_Scu_SafetyOffMode'
	.align	2
	
	.global	SchM_Exit_Wdg_17_Scu_SafetyOffMode

; ..\mcal_src\SchM.c	   432  }
; ..\mcal_src\SchM.c	   433  void SchM_Exit_Wdg_17_Scu_SafetyOffMode(void)
; Function SchM_Exit_Wdg_17_Scu_SafetyOffMode
.L145:
SchM_Exit_Wdg_17_Scu_SafetyOffMode:	.type	func

; ..\mcal_src\SchM.c	   434  {
; ..\mcal_src\SchM.c	   435    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L777:
	
__SchM_Exit_Wdg_17_Scu_SafetyOffMode_function_end:
	.size	SchM_Exit_Wdg_17_Scu_SafetyOffMode,__SchM_Exit_Wdg_17_Scu_SafetyOffMode_function_end-SchM_Exit_Wdg_17_Scu_SafetyOffMode
.L565:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Fls_17_Pmu_Init',code,cluster('SchM_Enter_Fls_17_Pmu_Init')
	.sect	'.text.SchM.SchM_Enter_Fls_17_Pmu_Init'
	.align	2
	
	.global	SchM_Enter_Fls_17_Pmu_Init

; ..\mcal_src\SchM.c	   436  }
; ..\mcal_src\SchM.c	   437  
; ..\mcal_src\SchM.c	   438  /********************************FLS**************************************/
; ..\mcal_src\SchM.c	   439  
; ..\mcal_src\SchM.c	   440  
; ..\mcal_src\SchM.c	   441  void SchM_Enter_Fls_17_Pmu_Init(void)
; Function SchM_Enter_Fls_17_Pmu_Init
.L147:
SchM_Enter_Fls_17_Pmu_Init:	.type	func

; ..\mcal_src\SchM.c	   442  {
; ..\mcal_src\SchM.c	   443    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L778:
	
__SchM_Enter_Fls_17_Pmu_Init_function_end:
	.size	SchM_Enter_Fls_17_Pmu_Init,__SchM_Enter_Fls_17_Pmu_Init_function_end-SchM_Enter_Fls_17_Pmu_Init
.L570:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Fls_17_Pmu_Init',code,cluster('SchM_Exit_Fls_17_Pmu_Init')
	.sect	'.text.SchM.SchM_Exit_Fls_17_Pmu_Init'
	.align	2
	
	.global	SchM_Exit_Fls_17_Pmu_Init

; ..\mcal_src\SchM.c	   444  }
; ..\mcal_src\SchM.c	   445  
; ..\mcal_src\SchM.c	   446  void SchM_Exit_Fls_17_Pmu_Init(void)
; Function SchM_Exit_Fls_17_Pmu_Init
.L149:
SchM_Exit_Fls_17_Pmu_Init:	.type	func

; ..\mcal_src\SchM.c	   447  {
; ..\mcal_src\SchM.c	   448    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L779:
	
__SchM_Exit_Fls_17_Pmu_Init_function_end:
	.size	SchM_Exit_Fls_17_Pmu_Init,__SchM_Exit_Fls_17_Pmu_Init_function_end-SchM_Exit_Fls_17_Pmu_Init
.L575:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Fls_17_Pmu_Erase',code,cluster('SchM_Enter_Fls_17_Pmu_Erase')
	.sect	'.text.SchM.SchM_Enter_Fls_17_Pmu_Erase'
	.align	2
	
	.global	SchM_Enter_Fls_17_Pmu_Erase

; ..\mcal_src\SchM.c	   449  }
; ..\mcal_src\SchM.c	   450  
; ..\mcal_src\SchM.c	   451  void SchM_Enter_Fls_17_Pmu_Erase(void)
; Function SchM_Enter_Fls_17_Pmu_Erase
.L151:
SchM_Enter_Fls_17_Pmu_Erase:	.type	func

; ..\mcal_src\SchM.c	   452  {
; ..\mcal_src\SchM.c	   453    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L780:
	
__SchM_Enter_Fls_17_Pmu_Erase_function_end:
	.size	SchM_Enter_Fls_17_Pmu_Erase,__SchM_Enter_Fls_17_Pmu_Erase_function_end-SchM_Enter_Fls_17_Pmu_Erase
.L580:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Fls_17_Pmu_Erase',code,cluster('SchM_Exit_Fls_17_Pmu_Erase')
	.sect	'.text.SchM.SchM_Exit_Fls_17_Pmu_Erase'
	.align	2
	
	.global	SchM_Exit_Fls_17_Pmu_Erase

; ..\mcal_src\SchM.c	   454  }
; ..\mcal_src\SchM.c	   455  
; ..\mcal_src\SchM.c	   456  void SchM_Exit_Fls_17_Pmu_Erase(void)
; Function SchM_Exit_Fls_17_Pmu_Erase
.L153:
SchM_Exit_Fls_17_Pmu_Erase:	.type	func

; ..\mcal_src\SchM.c	   457  {
; ..\mcal_src\SchM.c	   458    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L781:
	
__SchM_Exit_Fls_17_Pmu_Erase_function_end:
	.size	SchM_Exit_Fls_17_Pmu_Erase,__SchM_Exit_Fls_17_Pmu_Erase_function_end-SchM_Exit_Fls_17_Pmu_Erase
.L585:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Fls_17_Pmu_Write',code,cluster('SchM_Enter_Fls_17_Pmu_Write')
	.sect	'.text.SchM.SchM_Enter_Fls_17_Pmu_Write'
	.align	2
	
	.global	SchM_Enter_Fls_17_Pmu_Write

; ..\mcal_src\SchM.c	   459  }
; ..\mcal_src\SchM.c	   460  
; ..\mcal_src\SchM.c	   461  void SchM_Enter_Fls_17_Pmu_Write(void)
; Function SchM_Enter_Fls_17_Pmu_Write
.L155:
SchM_Enter_Fls_17_Pmu_Write:	.type	func

; ..\mcal_src\SchM.c	   462  {
; ..\mcal_src\SchM.c	   463    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L782:
	
__SchM_Enter_Fls_17_Pmu_Write_function_end:
	.size	SchM_Enter_Fls_17_Pmu_Write,__SchM_Enter_Fls_17_Pmu_Write_function_end-SchM_Enter_Fls_17_Pmu_Write
.L590:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Fls_17_Pmu_Write',code,cluster('SchM_Exit_Fls_17_Pmu_Write')
	.sect	'.text.SchM.SchM_Exit_Fls_17_Pmu_Write'
	.align	2
	
	.global	SchM_Exit_Fls_17_Pmu_Write

; ..\mcal_src\SchM.c	   464  }
; ..\mcal_src\SchM.c	   465  
; ..\mcal_src\SchM.c	   466  void SchM_Exit_Fls_17_Pmu_Write(void)
; Function SchM_Exit_Fls_17_Pmu_Write
.L157:
SchM_Exit_Fls_17_Pmu_Write:	.type	func

; ..\mcal_src\SchM.c	   467  {
; ..\mcal_src\SchM.c	   468    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L783:
	
__SchM_Exit_Fls_17_Pmu_Write_function_end:
	.size	SchM_Exit_Fls_17_Pmu_Write,__SchM_Exit_Fls_17_Pmu_Write_function_end-SchM_Exit_Fls_17_Pmu_Write
.L595:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Fls_17_Pmu_Main',code,cluster('SchM_Enter_Fls_17_Pmu_Main')
	.sect	'.text.SchM.SchM_Enter_Fls_17_Pmu_Main'
	.align	2
	
	.global	SchM_Enter_Fls_17_Pmu_Main

; ..\mcal_src\SchM.c	   469  }
; ..\mcal_src\SchM.c	   470  
; ..\mcal_src\SchM.c	   471  void SchM_Enter_Fls_17_Pmu_Main(void)
; Function SchM_Enter_Fls_17_Pmu_Main
.L159:
SchM_Enter_Fls_17_Pmu_Main:	.type	func

; ..\mcal_src\SchM.c	   472  {
; ..\mcal_src\SchM.c	   473    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L784:
	
__SchM_Enter_Fls_17_Pmu_Main_function_end:
	.size	SchM_Enter_Fls_17_Pmu_Main,__SchM_Enter_Fls_17_Pmu_Main_function_end-SchM_Enter_Fls_17_Pmu_Main
.L600:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Fls_17_Pmu_Main',code,cluster('SchM_Exit_Fls_17_Pmu_Main')
	.sect	'.text.SchM.SchM_Exit_Fls_17_Pmu_Main'
	.align	2
	
	.global	SchM_Exit_Fls_17_Pmu_Main

; ..\mcal_src\SchM.c	   474  }
; ..\mcal_src\SchM.c	   475  
; ..\mcal_src\SchM.c	   476  void SchM_Exit_Fls_17_Pmu_Main(void)
; Function SchM_Exit_Fls_17_Pmu_Main
.L161:
SchM_Exit_Fls_17_Pmu_Main:	.type	func

; ..\mcal_src\SchM.c	   477  {
; ..\mcal_src\SchM.c	   478    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L785:
	
__SchM_Exit_Fls_17_Pmu_Main_function_end:
	.size	SchM_Exit_Fls_17_Pmu_Main,__SchM_Exit_Fls_17_Pmu_Main_function_end-SchM_Exit_Fls_17_Pmu_Main
.L605:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Fls_17_Pmu_ResumeErase',code,cluster('SchM_Enter_Fls_17_Pmu_ResumeErase')
	.sect	'.text.SchM.SchM_Enter_Fls_17_Pmu_ResumeErase'
	.align	2
	
	.global	SchM_Enter_Fls_17_Pmu_ResumeErase

; ..\mcal_src\SchM.c	   479  }
; ..\mcal_src\SchM.c	   480  
; ..\mcal_src\SchM.c	   481  void SchM_Enter_Fls_17_Pmu_ResumeErase(void)
; Function SchM_Enter_Fls_17_Pmu_ResumeErase
.L163:
SchM_Enter_Fls_17_Pmu_ResumeErase:	.type	func

; ..\mcal_src\SchM.c	   482  {
; ..\mcal_src\SchM.c	   483    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L786:
	
__SchM_Enter_Fls_17_Pmu_ResumeErase_function_end:
	.size	SchM_Enter_Fls_17_Pmu_ResumeErase,__SchM_Enter_Fls_17_Pmu_ResumeErase_function_end-SchM_Enter_Fls_17_Pmu_ResumeErase
.L610:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Fls_17_Pmu_ResumeErase',code,cluster('SchM_Exit_Fls_17_Pmu_ResumeErase')
	.sect	'.text.SchM.SchM_Exit_Fls_17_Pmu_ResumeErase'
	.align	2
	
	.global	SchM_Exit_Fls_17_Pmu_ResumeErase

; ..\mcal_src\SchM.c	   484  }
; ..\mcal_src\SchM.c	   485  
; ..\mcal_src\SchM.c	   486  void SchM_Exit_Fls_17_Pmu_ResumeErase(void)
; Function SchM_Exit_Fls_17_Pmu_ResumeErase
.L165:
SchM_Exit_Fls_17_Pmu_ResumeErase:	.type	func

; ..\mcal_src\SchM.c	   487  {
; ..\mcal_src\SchM.c	   488    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L787:
	
__SchM_Exit_Fls_17_Pmu_ResumeErase_function_end:
	.size	SchM_Exit_Fls_17_Pmu_ResumeErase,__SchM_Exit_Fls_17_Pmu_ResumeErase_function_end-SchM_Exit_Fls_17_Pmu_ResumeErase
.L615:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_FlsLoader_Erase',code,cluster('SchM_Enter_FlsLoader_Erase')
	.sect	'.text.SchM.SchM_Enter_FlsLoader_Erase'
	.align	2
	
	.global	SchM_Enter_FlsLoader_Erase

; ..\mcal_src\SchM.c	   489  }
; ..\mcal_src\SchM.c	   490  
; ..\mcal_src\SchM.c	   491  /*******************************FLSLOADER**************************************/
; ..\mcal_src\SchM.c	   492  
; ..\mcal_src\SchM.c	   493  
; ..\mcal_src\SchM.c	   494  void SchM_Enter_FlsLoader_Erase(void)
; Function SchM_Enter_FlsLoader_Erase
.L167:
SchM_Enter_FlsLoader_Erase:	.type	func

; ..\mcal_src\SchM.c	   495  {
; ..\mcal_src\SchM.c	   496    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L788:
	
__SchM_Enter_FlsLoader_Erase_function_end:
	.size	SchM_Enter_FlsLoader_Erase,__SchM_Enter_FlsLoader_Erase_function_end-SchM_Enter_FlsLoader_Erase
.L620:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_FlsLoader_Write',code,cluster('SchM_Enter_FlsLoader_Write')
	.sect	'.text.SchM.SchM_Enter_FlsLoader_Write'
	.align	2
	
	.global	SchM_Enter_FlsLoader_Write

; ..\mcal_src\SchM.c	   497  }
; ..\mcal_src\SchM.c	   498  
; ..\mcal_src\SchM.c	   499  void SchM_Enter_FlsLoader_Write(void)
; Function SchM_Enter_FlsLoader_Write
.L169:
SchM_Enter_FlsLoader_Write:	.type	func

; ..\mcal_src\SchM.c	   500  {
; ..\mcal_src\SchM.c	   501    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L789:
	
__SchM_Enter_FlsLoader_Write_function_end:
	.size	SchM_Enter_FlsLoader_Write,__SchM_Enter_FlsLoader_Write_function_end-SchM_Enter_FlsLoader_Write
.L625:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_FlsLoader_lLock',code,cluster('SchM_Enter_FlsLoader_lLock')
	.sect	'.text.SchM.SchM_Enter_FlsLoader_lLock'
	.align	2
	
	.global	SchM_Enter_FlsLoader_lLock

; ..\mcal_src\SchM.c	   502  }
; ..\mcal_src\SchM.c	   503  
; ..\mcal_src\SchM.c	   504  void SchM_Enter_FlsLoader_lLock(void)
; Function SchM_Enter_FlsLoader_lLock
.L171:
SchM_Enter_FlsLoader_lLock:	.type	func

; ..\mcal_src\SchM.c	   505  {
; ..\mcal_src\SchM.c	   506    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L790:
	
__SchM_Enter_FlsLoader_lLock_function_end:
	.size	SchM_Enter_FlsLoader_lLock,__SchM_Enter_FlsLoader_lLock_function_end-SchM_Enter_FlsLoader_lLock
.L630:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_FlsLoader_lLock',code,cluster('SchM_Exit_FlsLoader_lLock')
	.sect	'.text.SchM.SchM_Exit_FlsLoader_lLock'
	.align	2
	
	.global	SchM_Exit_FlsLoader_lLock

; ..\mcal_src\SchM.c	   507  }
; ..\mcal_src\SchM.c	   508  
; ..\mcal_src\SchM.c	   509  void SchM_Exit_FlsLoader_lLock(void)
; Function SchM_Exit_FlsLoader_lLock
.L173:
SchM_Exit_FlsLoader_lLock:	.type	func

; ..\mcal_src\SchM.c	   510  {
; ..\mcal_src\SchM.c	   511    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L791:
	
__SchM_Exit_FlsLoader_lLock_function_end:
	.size	SchM_Exit_FlsLoader_lLock,__SchM_Exit_FlsLoader_lLock_function_end-SchM_Exit_FlsLoader_lLock
.L635:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_FlsLoader_Write',code,cluster('SchM_Exit_FlsLoader_Write')
	.sect	'.text.SchM.SchM_Exit_FlsLoader_Write'
	.align	2
	
	.global	SchM_Exit_FlsLoader_Write

; ..\mcal_src\SchM.c	   512  }
; ..\mcal_src\SchM.c	   513  
; ..\mcal_src\SchM.c	   514  void SchM_Exit_FlsLoader_Write(void)
; Function SchM_Exit_FlsLoader_Write
.L175:
SchM_Exit_FlsLoader_Write:	.type	func

; ..\mcal_src\SchM.c	   515  {
; ..\mcal_src\SchM.c	   516    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L792:
	
__SchM_Exit_FlsLoader_Write_function_end:
	.size	SchM_Exit_FlsLoader_Write,__SchM_Exit_FlsLoader_Write_function_end-SchM_Exit_FlsLoader_Write
.L640:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_FlsLoader_Erase',code,cluster('SchM_Exit_FlsLoader_Erase')
	.sect	'.text.SchM.SchM_Exit_FlsLoader_Erase'
	.align	2
	
	.global	SchM_Exit_FlsLoader_Erase

; ..\mcal_src\SchM.c	   517  }
; ..\mcal_src\SchM.c	   518  
; ..\mcal_src\SchM.c	   519  void SchM_Exit_FlsLoader_Erase(void)
; Function SchM_Exit_FlsLoader_Erase
.L177:
SchM_Exit_FlsLoader_Erase:	.type	func

; ..\mcal_src\SchM.c	   520  {
; ..\mcal_src\SchM.c	   521    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L793:
	
__SchM_Exit_FlsLoader_Erase_function_end:
	.size	SchM_Exit_FlsLoader_Erase,__SchM_Exit_FlsLoader_Erase_function_end-SchM_Exit_FlsLoader_Erase
.L645:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Eth_17_EthMac_Transmit',code,cluster('SchM_Enter_Eth_17_EthMac_Transmit')
	.sect	'.text.SchM.SchM_Enter_Eth_17_EthMac_Transmit'
	.align	2
	
	.global	SchM_Enter_Eth_17_EthMac_Transmit

; ..\mcal_src\SchM.c	   522  }
; ..\mcal_src\SchM.c	   523  
; ..\mcal_src\SchM.c	   524  /********************************ETH*******************************************/
; ..\mcal_src\SchM.c	   525  
; ..\mcal_src\SchM.c	   526  
; ..\mcal_src\SchM.c	   527  void SchM_Enter_Eth_17_EthMac_Transmit(void)
; Function SchM_Enter_Eth_17_EthMac_Transmit
.L179:
SchM_Enter_Eth_17_EthMac_Transmit:	.type	func

; ..\mcal_src\SchM.c	   528  {
; ..\mcal_src\SchM.c	   529    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L794:
	
__SchM_Enter_Eth_17_EthMac_Transmit_function_end:
	.size	SchM_Enter_Eth_17_EthMac_Transmit,__SchM_Enter_Eth_17_EthMac_Transmit_function_end-SchM_Enter_Eth_17_EthMac_Transmit
.L650:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Eth_17_EthMac_Transmit',code,cluster('SchM_Exit_Eth_17_EthMac_Transmit')
	.sect	'.text.SchM.SchM_Exit_Eth_17_EthMac_Transmit'
	.align	2
	
	.global	SchM_Exit_Eth_17_EthMac_Transmit

; ..\mcal_src\SchM.c	   530  }
; ..\mcal_src\SchM.c	   531  
; ..\mcal_src\SchM.c	   532  void SchM_Exit_Eth_17_EthMac_Transmit(void)
; Function SchM_Exit_Eth_17_EthMac_Transmit
.L181:
SchM_Exit_Eth_17_EthMac_Transmit:	.type	func

; ..\mcal_src\SchM.c	   533  {
; ..\mcal_src\SchM.c	   534    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L795:
	
__SchM_Exit_Eth_17_EthMac_Transmit_function_end:
	.size	SchM_Exit_Eth_17_EthMac_Transmit,__SchM_Exit_Eth_17_EthMac_Transmit_function_end-SchM_Exit_Eth_17_EthMac_Transmit
.L655:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Eth_17_EthMac_ProvideTxBuffer',code,cluster('SchM_Enter_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.text.SchM.SchM_Enter_Eth_17_EthMac_ProvideTxBuffer'
	.align	2
	
	.global	SchM_Enter_Eth_17_EthMac_ProvideTxBuffer

; ..\mcal_src\SchM.c	   535  }
; ..\mcal_src\SchM.c	   536  
; ..\mcal_src\SchM.c	   537  void SchM_Enter_Eth_17_EthMac_ProvideTxBuffer(void)
; Function SchM_Enter_Eth_17_EthMac_ProvideTxBuffer
.L183:
SchM_Enter_Eth_17_EthMac_ProvideTxBuffer:	.type	func

; ..\mcal_src\SchM.c	   538  {
; ..\mcal_src\SchM.c	   539    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L796:
	
__SchM_Enter_Eth_17_EthMac_ProvideTxBuffer_function_end:
	.size	SchM_Enter_Eth_17_EthMac_ProvideTxBuffer,__SchM_Enter_Eth_17_EthMac_ProvideTxBuffer_function_end-SchM_Enter_Eth_17_EthMac_ProvideTxBuffer
.L660:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Eth_17_EthMac_ProvideTxBuffer',code,cluster('SchM_Exit_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.text.SchM.SchM_Exit_Eth_17_EthMac_ProvideTxBuffer'
	.align	2
	
	.global	SchM_Exit_Eth_17_EthMac_ProvideTxBuffer

; ..\mcal_src\SchM.c	   540  }
; ..\mcal_src\SchM.c	   541  
; ..\mcal_src\SchM.c	   542  void SchM_Exit_Eth_17_EthMac_ProvideTxBuffer(void)
; Function SchM_Exit_Eth_17_EthMac_ProvideTxBuffer
.L185:
SchM_Exit_Eth_17_EthMac_ProvideTxBuffer:	.type	func

; ..\mcal_src\SchM.c	   543  {
; ..\mcal_src\SchM.c	   544    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L797:
	
__SchM_Exit_Eth_17_EthMac_ProvideTxBuffer_function_end:
	.size	SchM_Exit_Eth_17_EthMac_ProvideTxBuffer,__SchM_Exit_Eth_17_EthMac_ProvideTxBuffer_function_end-SchM_Exit_Eth_17_EthMac_ProvideTxBuffer
.L665:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Eth_17_EthMac_SetControllerMode',code,cluster('SchM_Enter_Eth_17_EthMac_SetControllerMode')
	.sect	'.text.SchM.SchM_Enter_Eth_17_EthMac_SetControllerMode'
	.align	2
	
	.global	SchM_Enter_Eth_17_EthMac_SetControllerMode

; ..\mcal_src\SchM.c	   545  }
; ..\mcal_src\SchM.c	   546  
; ..\mcal_src\SchM.c	   547  void SchM_Enter_Eth_17_EthMac_SetControllerMode(void)
; Function SchM_Enter_Eth_17_EthMac_SetControllerMode
.L187:
SchM_Enter_Eth_17_EthMac_SetControllerMode:	.type	func

; ..\mcal_src\SchM.c	   548  {
; ..\mcal_src\SchM.c	   549    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L798:
	
__SchM_Enter_Eth_17_EthMac_SetControllerMode_function_end:
	.size	SchM_Enter_Eth_17_EthMac_SetControllerMode,__SchM_Enter_Eth_17_EthMac_SetControllerMode_function_end-SchM_Enter_Eth_17_EthMac_SetControllerMode
.L670:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Eth_17_EthMac_SetControllerMode',code,cluster('SchM_Exit_Eth_17_EthMac_SetControllerMode')
	.sect	'.text.SchM.SchM_Exit_Eth_17_EthMac_SetControllerMode'
	.align	2
	
	.global	SchM_Exit_Eth_17_EthMac_SetControllerMode

; ..\mcal_src\SchM.c	   550  }
; ..\mcal_src\SchM.c	   551  
; ..\mcal_src\SchM.c	   552  void SchM_Exit_Eth_17_EthMac_SetControllerMode(void)
; Function SchM_Exit_Eth_17_EthMac_SetControllerMode
.L189:
SchM_Exit_Eth_17_EthMac_SetControllerMode:	.type	func

; ..\mcal_src\SchM.c	   553  {
; ..\mcal_src\SchM.c	   554    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L799:
	
__SchM_Exit_Eth_17_EthMac_SetControllerMode_function_end:
	.size	SchM_Exit_Eth_17_EthMac_SetControllerMode,__SchM_Exit_Eth_17_EthMac_SetControllerMode_function_end-SchM_Exit_Eth_17_EthMac_SetControllerMode
.L675:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Eth_17_EthMac_TxRxIrqHandler',code,cluster('SchM_Enter_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.text.SchM.SchM_Enter_Eth_17_EthMac_TxRxIrqHandler'
	.align	2
	
	.global	SchM_Enter_Eth_17_EthMac_TxRxIrqHandler

; ..\mcal_src\SchM.c	   555  }
; ..\mcal_src\SchM.c	   556  
; ..\mcal_src\SchM.c	   557  void SchM_Enter_Eth_17_EthMac_TxRxIrqHandler(void)
; Function SchM_Enter_Eth_17_EthMac_TxRxIrqHandler
.L191:
SchM_Enter_Eth_17_EthMac_TxRxIrqHandler:	.type	func

; ..\mcal_src\SchM.c	   558  {
; ..\mcal_src\SchM.c	   559    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L800:
	
__SchM_Enter_Eth_17_EthMac_TxRxIrqHandler_function_end:
	.size	SchM_Enter_Eth_17_EthMac_TxRxIrqHandler,__SchM_Enter_Eth_17_EthMac_TxRxIrqHandler_function_end-SchM_Enter_Eth_17_EthMac_TxRxIrqHandler
.L680:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Eth_17_EthMac_TxRxIrqHandler',code,cluster('SchM_Exit_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.text.SchM.SchM_Exit_Eth_17_EthMac_TxRxIrqHandler'
	.align	2
	
	.global	SchM_Exit_Eth_17_EthMac_TxRxIrqHandler

; ..\mcal_src\SchM.c	   560  }
; ..\mcal_src\SchM.c	   561  
; ..\mcal_src\SchM.c	   562  void SchM_Exit_Eth_17_EthMac_TxRxIrqHandler(void)
; Function SchM_Exit_Eth_17_EthMac_TxRxIrqHandler
.L193:
SchM_Exit_Eth_17_EthMac_TxRxIrqHandler:	.type	func

; ..\mcal_src\SchM.c	   563  {
; ..\mcal_src\SchM.c	   564    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L801:
	
__SchM_Exit_Eth_17_EthMac_TxRxIrqHandler_function_end:
	.size	SchM_Exit_Eth_17_EthMac_TxRxIrqHandler,__SchM_Exit_Eth_17_EthMac_TxRxIrqHandler_function_end-SchM_Exit_Eth_17_EthMac_TxRxIrqHandler
.L685:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Pwm_17_Gtm_StartChannel',code,cluster('SchM_Enter_Pwm_17_Gtm_StartChannel')
	.sect	'.text.SchM.SchM_Enter_Pwm_17_Gtm_StartChannel'
	.align	2
	
	.global	SchM_Enter_Pwm_17_Gtm_StartChannel

; ..\mcal_src\SchM.c	   565  }
; ..\mcal_src\SchM.c	   566  
; ..\mcal_src\SchM.c	   567  /********************************PWM*******************************************/
; ..\mcal_src\SchM.c	   568  void SchM_Enter_Pwm_17_Gtm_StartChannel(void)
; Function SchM_Enter_Pwm_17_Gtm_StartChannel
.L195:
SchM_Enter_Pwm_17_Gtm_StartChannel:	.type	func

; ..\mcal_src\SchM.c	   569  {
; ..\mcal_src\SchM.c	   570    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L802:
	
__SchM_Enter_Pwm_17_Gtm_StartChannel_function_end:
	.size	SchM_Enter_Pwm_17_Gtm_StartChannel,__SchM_Enter_Pwm_17_Gtm_StartChannel_function_end-SchM_Enter_Pwm_17_Gtm_StartChannel
.L690:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Pwm_17_Gtm_StartChannel',code,cluster('SchM_Exit_Pwm_17_Gtm_StartChannel')
	.sect	'.text.SchM.SchM_Exit_Pwm_17_Gtm_StartChannel'
	.align	2
	
	.global	SchM_Exit_Pwm_17_Gtm_StartChannel

; ..\mcal_src\SchM.c	   571  }
; ..\mcal_src\SchM.c	   572  
; ..\mcal_src\SchM.c	   573  void SchM_Exit_Pwm_17_Gtm_StartChannel(void)
; Function SchM_Exit_Pwm_17_Gtm_StartChannel
.L197:
SchM_Exit_Pwm_17_Gtm_StartChannel:	.type	func

; ..\mcal_src\SchM.c	   574  {
; ..\mcal_src\SchM.c	   575    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L803:
	
__SchM_Exit_Pwm_17_Gtm_StartChannel_function_end:
	.size	SchM_Exit_Pwm_17_Gtm_StartChannel,__SchM_Exit_Pwm_17_Gtm_StartChannel_function_end-SchM_Exit_Pwm_17_Gtm_StartChannel
.L695:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Enter_Pwm_17_Gtm_SyncDuty',code,cluster('SchM_Enter_Pwm_17_Gtm_SyncDuty')
	.sect	'.text.SchM.SchM_Enter_Pwm_17_Gtm_SyncDuty'
	.align	2
	
	.global	SchM_Enter_Pwm_17_Gtm_SyncDuty

; ..\mcal_src\SchM.c	   576  }
; ..\mcal_src\SchM.c	   577  
; ..\mcal_src\SchM.c	   578  void SchM_Enter_Pwm_17_Gtm_SyncDuty(void)
; Function SchM_Enter_Pwm_17_Gtm_SyncDuty
.L199:
SchM_Enter_Pwm_17_Gtm_SyncDuty:	.type	func

; ..\mcal_src\SchM.c	   579  {
; ..\mcal_src\SchM.c	   580    SuspendAllInterrupts();
	j	OSEKMP_UserSuspendAllInterrupts
.L804:
	
__SchM_Enter_Pwm_17_Gtm_SyncDuty_function_end:
	.size	SchM_Enter_Pwm_17_Gtm_SyncDuty,__SchM_Enter_Pwm_17_Gtm_SyncDuty_function_end-SchM_Enter_Pwm_17_Gtm_SyncDuty
.L700:
	; End of function
	
	.sdecl	'.text.SchM.SchM_Exit_Pwm_17_Gtm_SyncDuty',code,cluster('SchM_Exit_Pwm_17_Gtm_SyncDuty')
	.sect	'.text.SchM.SchM_Exit_Pwm_17_Gtm_SyncDuty'
	.align	2
	
	.global	SchM_Exit_Pwm_17_Gtm_SyncDuty

; ..\mcal_src\SchM.c	   581  }
; ..\mcal_src\SchM.c	   582  
; ..\mcal_src\SchM.c	   583  void SchM_Exit_Pwm_17_Gtm_SyncDuty(void)
; Function SchM_Exit_Pwm_17_Gtm_SyncDuty
.L201:
SchM_Exit_Pwm_17_Gtm_SyncDuty:	.type	func

; ..\mcal_src\SchM.c	   584  {
; ..\mcal_src\SchM.c	   585    ResumeAllInterrupts();
	j	OSEKMP_UserResumeAllInterrupts
.L805:
	
__SchM_Exit_Pwm_17_Gtm_SyncDuty_function_end:
	.size	SchM_Exit_Pwm_17_Gtm_SyncDuty,__SchM_Exit_Pwm_17_Gtm_SyncDuty_function_end-SchM_Exit_Pwm_17_Gtm_SyncDuty
.L705:
	; End of function
	
	.calls	'SchM_Enter_Adc_StartGroup','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Adc_StopGroup','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Adc_ReadGroup','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Adc_EnableHwTrig','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Adc_DisableHwTrig','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Adc_GetGrpStatus','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Adc_GetStreamLastPtr','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Adc_ScheduleStart','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Adc_ScheduleStop','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Adc_ScheduleNext','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Adc_PushQueue','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Adc_PopQueue','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Adc_StartGroup','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Adc_StopGroup','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Adc_ReadGroup','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Adc_EnableHwTrig','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Adc_DisableHwTrig','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Adc_GetGrpStatus','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Adc_GetStreamLastPtr','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Adc_ScheduleStart','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Adc_ScheduleStop','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Adc_ScheduleNext','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Adc_PushQueue','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Adc_PopQueue','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Icu_17_GtmCcu6_EnableWakeup','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Icu_17_GtmCcu6_EnableWakeup','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Icu_17_GtmCcu6_EnableNotification','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Icu_17_GtmCcu6_EnableNotification','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Can_17_MCanP_CanDisInt','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Can_17_MCanP_CanDisInt','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Can_17_MCanP_CanEnInt','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Can_17_MCanP_CanEnInt','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Can_17_MCanP_CanWrMO','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Can_17_MCanP_CanWrMO','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Fr_17_Eray_ControllerInit','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Fr_17_Eray_ControllerInit','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Fr_17_Eray_SetWakeupChannel','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Fr_17_Eray_SetWakeupChannel','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Spi_WriteIB','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Spi_WriteIB','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Spi_AsyncTransmit','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Spi_AsyncTransmit','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Spi_GetSequenceResult','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Spi_GetSequenceResult','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Spi_Cancel','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Spi_Cancel','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Spi_Init','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Spi_Init','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Spi_DeInit','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Spi_DeInit','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Spi_SyncTransmit','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Spi_SyncTransmit','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Wdg_17_Scu_TimerHandling','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Wdg_17_Scu_Trigger','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Wdg_17_Scu_ChangeMode','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Wdg_17_Scu_SafetyInit','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Wdg_17_Scu_SafetyTrigger','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_Wdg_17_Scu_SafetyOffMode','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Wdg_17_Scu_TimerHandling','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Wdg_17_Scu_Trigger','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Wdg_17_Scu_ChangeMode','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Wdg_17_Scu_SafetyInit','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Wdg_17_Scu_SafetyTrigger','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_Wdg_17_Scu_SafetyOffMode','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Fls_17_Pmu_Init','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Fls_17_Pmu_Init','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Fls_17_Pmu_Erase','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Fls_17_Pmu_Erase','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Fls_17_Pmu_Write','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Fls_17_Pmu_Write','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Fls_17_Pmu_Main','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Fls_17_Pmu_Main','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Fls_17_Pmu_ResumeErase','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Fls_17_Pmu_ResumeErase','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_FlsLoader_Erase','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_FlsLoader_Write','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Enter_FlsLoader_lLock','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_FlsLoader_lLock','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_FlsLoader_Write','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Exit_FlsLoader_Erase','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Eth_17_EthMac_Transmit','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Eth_17_EthMac_Transmit','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Eth_17_EthMac_ProvideTxBuffer','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Eth_17_EthMac_ProvideTxBuffer','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Eth_17_EthMac_SetControllerMode','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Eth_17_EthMac_SetControllerMode','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Eth_17_EthMac_TxRxIrqHandler','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Eth_17_EthMac_TxRxIrqHandler','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Pwm_17_Gtm_StartChannel','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Pwm_17_Gtm_StartChannel','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Pwm_17_Gtm_SyncDuty','OSEKMP_UserSuspendAllInterrupts'
	.calls	'SchM_Exit_Pwm_17_Gtm_SyncDuty','OSEKMP_UserResumeAllInterrupts'
	.calls	'SchM_Enter_Adc_StartGroup','',0
	.calls	'SchM_Enter_Adc_StopGroup','',0
	.calls	'SchM_Enter_Adc_ReadGroup','',0
	.calls	'SchM_Enter_Adc_EnableHwTrig','',0
	.calls	'SchM_Enter_Adc_DisableHwTrig','',0
	.calls	'SchM_Enter_Adc_GetGrpStatus','',0
	.calls	'SchM_Enter_Adc_GetStreamLastPtr','',0
	.calls	'SchM_Enter_Adc_ScheduleStart','',0
	.calls	'SchM_Enter_Adc_ScheduleStop','',0
	.calls	'SchM_Enter_Adc_ScheduleNext','',0
	.calls	'SchM_Enter_Adc_PushQueue','',0
	.calls	'SchM_Enter_Adc_PopQueue','',0
	.calls	'SchM_Exit_Adc_StartGroup','',0
	.calls	'SchM_Exit_Adc_StopGroup','',0
	.calls	'SchM_Exit_Adc_ReadGroup','',0
	.calls	'SchM_Exit_Adc_EnableHwTrig','',0
	.calls	'SchM_Exit_Adc_DisableHwTrig','',0
	.calls	'SchM_Exit_Adc_GetGrpStatus','',0
	.calls	'SchM_Exit_Adc_GetStreamLastPtr','',0
	.calls	'SchM_Exit_Adc_ScheduleStart','',0
	.calls	'SchM_Exit_Adc_ScheduleStop','',0
	.calls	'SchM_Exit_Adc_ScheduleNext','',0
	.calls	'SchM_Exit_Adc_PushQueue','',0
	.calls	'SchM_Exit_Adc_PopQueue','',0
	.calls	'SchM_Enter_Icu_17_GtmCcu6_EnableWakeup','',0
	.calls	'SchM_Exit_Icu_17_GtmCcu6_EnableWakeup','',0
	.calls	'SchM_Enter_Icu_17_GtmCcu6_EnableNotification','',0
	.calls	'SchM_Exit_Icu_17_GtmCcu6_EnableNotification','',0
	.calls	'SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount','',0
	.calls	'SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount','',0
	.calls	'SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate','',0
	.calls	'SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate','',0
	.calls	'SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle','',0
	.calls	'SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle','',0
	.calls	'SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate','',0
	.calls	'SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate','',0
	.calls	'SchM_Enter_Can_17_MCanP_CanDisInt','',0
	.calls	'SchM_Exit_Can_17_MCanP_CanDisInt','',0
	.calls	'SchM_Enter_Can_17_MCanP_CanEnInt','',0
	.calls	'SchM_Exit_Can_17_MCanP_CanEnInt','',0
	.calls	'SchM_Enter_Can_17_MCanP_CanWrMO','',0
	.calls	'SchM_Exit_Can_17_MCanP_CanWrMO','',0
	.calls	'SchM_Enter_Fr_17_Eray_ControllerInit','',0
	.calls	'SchM_Exit_Fr_17_Eray_ControllerInit','',0
	.calls	'SchM_Enter_Fr_17_Eray_SetWakeupChannel','',0
	.calls	'SchM_Exit_Fr_17_Eray_SetWakeupChannel','',0
	.calls	'SchM_Enter_Spi_WriteIB','',0
	.calls	'SchM_Exit_Spi_WriteIB','',0
	.calls	'SchM_Enter_Spi_AsyncTransmit','',0
	.calls	'SchM_Exit_Spi_AsyncTransmit','',0
	.calls	'SchM_Enter_Spi_GetSequenceResult','',0
	.calls	'SchM_Exit_Spi_GetSequenceResult','',0
	.calls	'SchM_Enter_Spi_Cancel','',0
	.calls	'SchM_Exit_Spi_Cancel','',0
	.calls	'SchM_Enter_Spi_Init','',0
	.calls	'SchM_Exit_Spi_Init','',0
	.calls	'SchM_Enter_Spi_DeInit','',0
	.calls	'SchM_Exit_Spi_DeInit','',0
	.calls	'SchM_Enter_Spi_SyncTransmit','',0
	.calls	'SchM_Exit_Spi_SyncTransmit','',0
	.calls	'SchM_Enter_Wdg_17_Scu_TimerHandling','',0
	.calls	'SchM_Enter_Wdg_17_Scu_Trigger','',0
	.calls	'SchM_Enter_Wdg_17_Scu_ChangeMode','',0
	.calls	'SchM_Enter_Wdg_17_Scu_SafetyInit','',0
	.calls	'SchM_Enter_Wdg_17_Scu_SafetyTrigger','',0
	.calls	'SchM_Enter_Wdg_17_Scu_SafetyOffMode','',0
	.calls	'SchM_Exit_Wdg_17_Scu_TimerHandling','',0
	.calls	'SchM_Exit_Wdg_17_Scu_Trigger','',0
	.calls	'SchM_Exit_Wdg_17_Scu_ChangeMode','',0
	.calls	'SchM_Exit_Wdg_17_Scu_SafetyInit','',0
	.calls	'SchM_Exit_Wdg_17_Scu_SafetyTrigger','',0
	.calls	'SchM_Exit_Wdg_17_Scu_SafetyOffMode','',0
	.calls	'SchM_Enter_Fls_17_Pmu_Init','',0
	.calls	'SchM_Exit_Fls_17_Pmu_Init','',0
	.calls	'SchM_Enter_Fls_17_Pmu_Erase','',0
	.calls	'SchM_Exit_Fls_17_Pmu_Erase','',0
	.calls	'SchM_Enter_Fls_17_Pmu_Write','',0
	.calls	'SchM_Exit_Fls_17_Pmu_Write','',0
	.calls	'SchM_Enter_Fls_17_Pmu_Main','',0
	.calls	'SchM_Exit_Fls_17_Pmu_Main','',0
	.calls	'SchM_Enter_Fls_17_Pmu_ResumeErase','',0
	.calls	'SchM_Exit_Fls_17_Pmu_ResumeErase','',0
	.calls	'SchM_Enter_FlsLoader_Erase','',0
	.calls	'SchM_Enter_FlsLoader_Write','',0
	.calls	'SchM_Enter_FlsLoader_lLock','',0
	.calls	'SchM_Exit_FlsLoader_lLock','',0
	.calls	'SchM_Exit_FlsLoader_Write','',0
	.calls	'SchM_Exit_FlsLoader_Erase','',0
	.calls	'SchM_Enter_Eth_17_EthMac_Transmit','',0
	.calls	'SchM_Exit_Eth_17_EthMac_Transmit','',0
	.calls	'SchM_Enter_Eth_17_EthMac_ProvideTxBuffer','',0
	.calls	'SchM_Exit_Eth_17_EthMac_ProvideTxBuffer','',0
	.calls	'SchM_Enter_Eth_17_EthMac_SetControllerMode','',0
	.calls	'SchM_Exit_Eth_17_EthMac_SetControllerMode','',0
	.calls	'SchM_Enter_Eth_17_EthMac_TxRxIrqHandler','',0
	.calls	'SchM_Exit_Eth_17_EthMac_TxRxIrqHandler','',0
	.calls	'SchM_Enter_Pwm_17_Gtm_StartChannel','',0
	.calls	'SchM_Exit_Pwm_17_Gtm_StartChannel','',0
	.calls	'SchM_Enter_Pwm_17_Gtm_SyncDuty','',0
	.extern	OSEKMP_UserSuspendAllInterrupts
	.extern	OSEKMP_UserResumeAllInterrupts
	.calls	'SchM_Exit_Pwm_17_Gtm_SyncDuty','',0
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L203:
	.word	56621
	.half	3
	.word	.L204
	.byte	4
.L202:
	.byte	1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L205
	.byte	2
	.byte	'OSEKMP_UserSuspendAllInterrupts',0,1,142,1,6,1,1,1,1,2
	.byte	'OSEKMP_UserResumeAllInterrupts',0,1,166,1,6,1,1,1,1,3
	.byte	'void',0,4
	.word	254
	.byte	5
	.byte	'__prof_adm',0,2,1,1
	.word	260
	.byte	6,1,4
	.word	284
	.byte	5
	.byte	'__codeptr',0,2,1,1
	.word	286
	.byte	7
	.byte	'unsigned char',0,1,8,5
	.byte	'uint8',0,3,90,29
	.word	309
	.byte	7
	.byte	'unsigned short int',0,2,7,5
	.byte	'uint16',0,3,92,29
	.word	340
	.byte	7
	.byte	'unsigned long int',0,4,7,5
	.byte	'uint32',0,3,94,29
	.word	377
	.byte	8
	.byte	'_Ifx_DMA_ACCEN00_Bits',0,4,45,16,4,9
	.byte	'EN0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	309
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	309
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	309
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	309
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	309
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	309
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	309
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	309
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_DMA_ACCEN00_Bits',0,4,79,3
	.word	413
	.byte	8
	.byte	'_Ifx_DMA_ACCEN01_Bits',0,4,82,16,4,7
	.byte	'unsigned int',0,4,7,9
	.byte	'reserved_0',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_ACCEN01_Bits',0,4,85,3
	.word	972
	.byte	8
	.byte	'_Ifx_DMA_ACCEN10_Bits',0,4,88,16,4,9
	.byte	'EN0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	309
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	309
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	309
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	309
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	309
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	309
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	309
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	309
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_DMA_ACCEN10_Bits',0,4,122,3
	.word	1067
	.byte	8
	.byte	'_Ifx_DMA_ACCEN11_Bits',0,4,125,16,4,9
	.byte	'reserved_0',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_ACCEN11_Bits',0,4,128,1,3
	.word	1626
	.byte	8
	.byte	'_Ifx_DMA_ACCEN20_Bits',0,4,131,1,16,4,9
	.byte	'EN0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	309
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	309
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	309
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	309
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	309
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	309
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	309
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	309
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_DMA_ACCEN20_Bits',0,4,165,1,3
	.word	1706
	.byte	8
	.byte	'_Ifx_DMA_ACCEN21_Bits',0,4,168,1,16,4,9
	.byte	'reserved_0',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_ACCEN21_Bits',0,4,171,1,3
	.word	2267
	.byte	8
	.byte	'_Ifx_DMA_ACCEN30_Bits',0,4,174,1,16,4,9
	.byte	'EN0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	309
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	309
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	309
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	309
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	309
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	309
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	309
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	309
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_DMA_ACCEN30_Bits',0,4,208,1,3
	.word	2348
	.byte	8
	.byte	'_Ifx_DMA_ACCEN31_Bits',0,4,211,1,16,4,9
	.byte	'reserved_0',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_ACCEN31_Bits',0,4,214,1,3
	.word	2909
	.byte	8
	.byte	'_Ifx_DMA_BLK_CLRE_Bits',0,4,217,1,16,4,9
	.byte	'reserved_0',0,2
	.word	340
	.byte	16,0,2,35,0,9
	.byte	'CSER',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'CDER',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	309
	.byte	2,4,2,35,2,9
	.byte	'CSPBER',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'CSRIER',0,1
	.word	309
	.byte	1,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	309
	.byte	2,0,2,35,2,9
	.byte	'CRAMER',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'CSLLER',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'CDLLER',0,1
	.word	309
	.byte	1,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	309
	.byte	5,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_CLRE_Bits',0,4,230,1,3
	.word	2990
	.byte	8
	.byte	'_Ifx_DMA_BLK_EER_Bits',0,4,233,1,16,4,9
	.byte	'reserved_0',0,2
	.word	340
	.byte	16,0,2,35,0,9
	.byte	'ESER',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'EDER',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	309
	.byte	6,0,2,35,2,9
	.byte	'ERER',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'reserved_25',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'ELER',0,1
	.word	309
	.byte	1,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	309
	.byte	5,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_EER_Bits',0,4,243,1,3
	.word	3264
	.byte	8
	.byte	'_Ifx_DMA_BLK_ERRSR_Bits',0,4,246,1,16,4,9
	.byte	'LEC',0,1
	.word	309
	.byte	7,1,2,35,0,9
	.byte	'reserved_7',0,2
	.word	340
	.byte	9,0,2,35,0,9
	.byte	'SER',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'DER',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	309
	.byte	2,4,2,35,2,9
	.byte	'SPBER',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'SRIER',0,1
	.word	309
	.byte	1,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	309
	.byte	2,0,2,35,2,9
	.byte	'RAMER',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'SLLER',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'DLLER',0,1
	.word	309
	.byte	1,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	309
	.byte	5,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_ERRSR_Bits',0,4,132,2,3
	.word	3478
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_ADICR_Bits',0,4,135,2,16,4,9
	.byte	'SMF',0,1
	.word	309
	.byte	3,5,2,35,0,9
	.byte	'INCS',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'DMF',0,1
	.word	309
	.byte	3,1,2,35,0,9
	.byte	'INCD',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'CBLS',0,1
	.word	309
	.byte	4,4,2,35,1,9
	.byte	'CBLD',0,1
	.word	309
	.byte	4,0,2,35,1,9
	.byte	'SHCT',0,1
	.word	309
	.byte	4,4,2,35,2,9
	.byte	'SCBE',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'DCBE',0,1
	.word	309
	.byte	1,2,2,35,2,9
	.byte	'STAMP',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'ETRL',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'WRPSE',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'WRPDE',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'INTCT',0,1
	.word	309
	.byte	2,4,2,35,3,9
	.byte	'IRDV',0,1
	.word	309
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_ME_ADICR_Bits',0,4,152,2,3
	.word	3762
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_CHCR_Bits',0,4,155,2,16,4,9
	.byte	'TREL',0,2
	.word	340
	.byte	14,2,2,35,0,9
	.byte	'reserved_14',0,1
	.word	309
	.byte	2,0,2,35,1,9
	.byte	'BLKM',0,1
	.word	309
	.byte	3,5,2,35,2,9
	.byte	'RROAT',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'CHMODE',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'CHDW',0,1
	.word	309
	.byte	3,0,2,35,2,9
	.byte	'PATSEL',0,1
	.word	309
	.byte	3,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'PRSEL',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'DMAPRIO',0,1
	.word	309
	.byte	2,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_ME_CHCR_Bits',0,4,168,2,3
	.word	4073
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_CHSR_Bits',0,4,171,2,16,4,9
	.byte	'TCOUNT',0,2
	.word	340
	.byte	14,2,2,35,0,9
	.byte	'reserved_14',0,1
	.word	309
	.byte	1,1,2,35,1,9
	.byte	'LXO',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'WRPS',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'WRPD',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'ICH',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'IPM',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'reserved_20',0,1
	.word	309
	.byte	2,2,2,35,2,9
	.byte	'BUFFER',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'FROZEN',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_ME_CHSR_Bits',0,4,184,2,3
	.word	4346
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_DADR_Bits',0,4,187,2,16,4,9
	.byte	'DADR',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_BLK_ME_DADR_Bits',0,4,190,2,3
	.word	4613
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R0_Bits',0,4,193,2,16,4,9
	.byte	'RD00',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'RD01',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'RD02',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'RD03',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_ME_R0_Bits',0,4,199,2,3
	.word	4696
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R1_Bits',0,4,202,2,16,4,9
	.byte	'RD10',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'RD11',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'RD12',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'RD13',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_ME_R1_Bits',0,4,208,2,3
	.word	4823
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R2_Bits',0,4,211,2,16,4,9
	.byte	'RD20',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'RD21',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'RD22',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'RD23',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_ME_R2_Bits',0,4,217,2,3
	.word	4950
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R3_Bits',0,4,220,2,16,4,9
	.byte	'RD30',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'RD31',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'RD32',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'RD33',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_ME_R3_Bits',0,4,226,2,3
	.word	5077
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R4_Bits',0,4,229,2,16,4,9
	.byte	'RD40',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'RD41',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'RD42',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'RD43',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_ME_R4_Bits',0,4,235,2,3
	.word	5204
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R5_Bits',0,4,238,2,16,4,9
	.byte	'RD50',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'RD51',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'RD52',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'RD53',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_ME_R5_Bits',0,4,244,2,3
	.word	5331
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R6_Bits',0,4,247,2,16,4,9
	.byte	'RD60',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'RD61',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'RD62',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'RD63',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_ME_R6_Bits',0,4,253,2,3
	.word	5458
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_R7_Bits',0,4,128,3,16,4,9
	.byte	'RD70',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'RD71',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'RD72',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'RD73',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_DMA_BLK_ME_R7_Bits',0,4,134,3,3
	.word	5585
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_RDCRC_Bits',0,4,137,3,16,4,9
	.byte	'RDCRC',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_BLK_ME_RDCRC_Bits',0,4,140,3,3
	.word	5712
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_SADR_Bits',0,4,143,3,16,4,9
	.byte	'SADR',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_BLK_ME_SADR_Bits',0,4,146,3,3
	.word	5798
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_SDCRC_Bits',0,4,149,3,16,4,9
	.byte	'SDCRC',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_BLK_ME_SDCRC_Bits',0,4,152,3,3
	.word	5881
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_SHADR_Bits',0,4,155,3,16,4,9
	.byte	'SHADR',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_BLK_ME_SHADR_Bits',0,4,158,3,3
	.word	5967
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME_SR_Bits',0,4,161,3,16,4,9
	.byte	'RS',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	309
	.byte	3,4,2,35,0,9
	.byte	'WS',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'reserved_5',0,2
	.word	340
	.byte	11,0,2,35,0,9
	.byte	'CH',0,1
	.word	309
	.byte	7,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	340
	.byte	9,0,2,35,2,0,5
	.byte	'Ifx_DMA_BLK_ME_SR_Bits',0,4,169,3,3
	.word	6053
	.byte	8
	.byte	'_Ifx_DMA_CH_ADICR_Bits',0,4,172,3,16,4,9
	.byte	'SMF',0,1
	.word	309
	.byte	3,5,2,35,0,9
	.byte	'INCS',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'DMF',0,1
	.word	309
	.byte	3,1,2,35,0,9
	.byte	'INCD',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'CBLS',0,1
	.word	309
	.byte	4,4,2,35,1,9
	.byte	'CBLD',0,1
	.word	309
	.byte	4,0,2,35,1,9
	.byte	'SHCT',0,1
	.word	309
	.byte	4,4,2,35,2,9
	.byte	'SCBE',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'DCBE',0,1
	.word	309
	.byte	1,2,2,35,2,9
	.byte	'STAMP',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'ETRL',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'WRPSE',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'WRPDE',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'INTCT',0,1
	.word	309
	.byte	2,4,2,35,3,9
	.byte	'IRDV',0,1
	.word	309
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_DMA_CH_ADICR_Bits',0,4,189,3,3
	.word	6225
	.byte	8
	.byte	'_Ifx_DMA_CH_CHCFGR_Bits',0,4,192,3,16,4,9
	.byte	'TREL',0,2
	.word	340
	.byte	14,2,2,35,0,9
	.byte	'reserved_14',0,1
	.word	309
	.byte	2,0,2,35,1,9
	.byte	'BLKM',0,1
	.word	309
	.byte	3,5,2,35,2,9
	.byte	'RROAT',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'CHMODE',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'CHDW',0,1
	.word	309
	.byte	3,0,2,35,2,9
	.byte	'PATSEL',0,1
	.word	309
	.byte	3,5,2,35,3,9
	.byte	'reserved_27',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'PRSEL',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'DMAPRIO',0,1
	.word	309
	.byte	2,0,2,35,3,0,5
	.byte	'Ifx_DMA_CH_CHCFGR_Bits',0,4,205,3,3
	.word	6528
	.byte	8
	.byte	'_Ifx_DMA_CH_CHCSR_Bits',0,4,208,3,16,4,9
	.byte	'TCOUNT',0,2
	.word	340
	.byte	14,2,2,35,0,9
	.byte	'reserved_14',0,1
	.word	309
	.byte	1,1,2,35,1,9
	.byte	'LXO',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'WRPS',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'WRPD',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'ICH',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'IPM',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'reserved_20',0,1
	.word	309
	.byte	2,2,2,35,2,9
	.byte	'BUFFER',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'FROZEN',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'SWB',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'CWRP',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'CICH',0,1
	.word	309
	.byte	1,5,2,35,3,9
	.byte	'SIT',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	309
	.byte	3,1,2,35,3,9
	.byte	'SCH',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_DMA_CH_CHCSR_Bits',0,4,226,3,3
	.word	6797
	.byte	8
	.byte	'_Ifx_DMA_CH_DADR_Bits',0,4,229,3,16,4,9
	.byte	'DADR',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_CH_DADR_Bits',0,4,232,3,3
	.word	7135
	.byte	8
	.byte	'_Ifx_DMA_CH_RDCRCR_Bits',0,4,235,3,16,4,9
	.byte	'RDCRC',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_CH_RDCRCR_Bits',0,4,238,3,3
	.word	7210
	.byte	8
	.byte	'_Ifx_DMA_CH_SADR_Bits',0,4,241,3,16,4,9
	.byte	'SADR',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_CH_SADR_Bits',0,4,244,3,3
	.word	7290
	.byte	8
	.byte	'_Ifx_DMA_CH_SDCRCR_Bits',0,4,247,3,16,4,9
	.byte	'SDCRC',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_CH_SDCRCR_Bits',0,4,250,3,3
	.word	7365
	.byte	8
	.byte	'_Ifx_DMA_CH_SHADR_Bits',0,4,253,3,16,4,9
	.byte	'SHADR',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_CH_SHADR_Bits',0,4,128,4,3
	.word	7445
	.byte	8
	.byte	'_Ifx_DMA_CLC_Bits',0,4,131,4,16,4,9
	.byte	'DISR',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'DISS',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'EDIS',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	999
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_DMA_CLC_Bits',0,4,138,4,3
	.word	7523
	.byte	8
	.byte	'_Ifx_DMA_ERRINTR_Bits',0,4,141,4,16,4,9
	.byte	'SIT',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	999
	.byte	31,0,2,35,2,0,5
	.byte	'Ifx_DMA_ERRINTR_Bits',0,4,145,4,3
	.word	7666
	.byte	8
	.byte	'_Ifx_DMA_HRR_Bits',0,4,148,4,16,4,9
	.byte	'HRP',0,1
	.word	309
	.byte	2,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	999
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_DMA_HRR_Bits',0,4,152,4,3
	.word	7762
	.byte	8
	.byte	'_Ifx_DMA_ID_Bits',0,4,155,4,16,4,9
	.byte	'MODREV',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'MODTYPE',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'MODNUMBER',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_DMA_ID_Bits',0,4,160,4,3
	.word	7850
	.byte	8
	.byte	'_Ifx_DMA_MEMCON_Bits',0,4,163,4,16,4,7
	.byte	'unsigned int',0,4,7,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	2,30,2,35,0,9
	.byte	'INTERR',0,4
	.word	7984
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	7984
	.byte	1,28,2,35,0,9
	.byte	'RMWERR',0,4
	.word	7984
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	7984
	.byte	1,26,2,35,0,9
	.byte	'DATAERR',0,4
	.word	7984
	.byte	1,25,2,35,0,9
	.byte	'reserved_7',0,4
	.word	7984
	.byte	1,24,2,35,0,9
	.byte	'PMIC',0,4
	.word	7984
	.byte	1,23,2,35,0,9
	.byte	'ERRDIS',0,4
	.word	7984
	.byte	1,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	7984
	.byte	22,0,2,35,0,0,5
	.byte	'Ifx_DMA_MEMCON_Bits',0,4,175,4,3
	.word	7957
	.byte	8
	.byte	'_Ifx_DMA_MODE_Bits',0,4,178,4,16,4,9
	.byte	'MODE',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	999
	.byte	31,0,2,35,2,0,5
	.byte	'Ifx_DMA_MODE_Bits',0,4,182,4,3
	.word	8230
	.byte	8
	.byte	'_Ifx_DMA_OTSS_Bits',0,4,185,4,16,4,9
	.byte	'TGS',0,1
	.word	309
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	309
	.byte	3,1,2,35,0,9
	.byte	'BS',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	999
	.byte	24,0,2,35,2,0,5
	.byte	'Ifx_DMA_OTSS_Bits',0,4,191,4,3
	.word	8321
	.byte	8
	.byte	'_Ifx_DMA_PRR0_Bits',0,4,194,4,16,4,9
	.byte	'PAT00',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'PAT01',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'PAT02',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'PAT03',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_DMA_PRR0_Bits',0,4,200,4,3
	.word	8447
	.byte	8
	.byte	'_Ifx_DMA_PRR1_Bits',0,4,203,4,16,4,9
	.byte	'PAT10',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'PAT11',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'PAT12',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'PAT13',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_DMA_PRR1_Bits',0,4,209,4,3
	.word	8568
	.byte	8
	.byte	'_Ifx_DMA_SUSACR_Bits',0,4,212,4,16,4,9
	.byte	'SUSAC',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	999
	.byte	31,0,2,35,2,0,5
	.byte	'Ifx_DMA_SUSACR_Bits',0,4,216,4,3
	.word	8689
	.byte	8
	.byte	'_Ifx_DMA_SUSENR_Bits',0,4,219,4,16,4,9
	.byte	'SUSEN',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	999
	.byte	31,0,2,35,2,0,5
	.byte	'Ifx_DMA_SUSENR_Bits',0,4,223,4,3
	.word	8785
	.byte	8
	.byte	'_Ifx_DMA_TIME_Bits',0,4,226,4,16,4,9
	.byte	'COUNT',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_DMA_TIME_Bits',0,4,229,4,3
	.word	8881
	.byte	8
	.byte	'_Ifx_DMA_TSR_Bits',0,4,232,4,16,4,9
	.byte	'RST',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'HTRE',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'TRL',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'CH',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	309
	.byte	4,0,2,35,0,9
	.byte	'HLTREQ',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'HLTACK',0,1
	.word	309
	.byte	1,6,2,35,1,9
	.byte	'reserved_10',0,1
	.word	309
	.byte	6,0,2,35,1,9
	.byte	'ECH',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'DCH',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'CTL',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	309
	.byte	5,0,2,35,2,9
	.byte	'HLTCLR',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'reserved_25',0,1
	.word	309
	.byte	7,0,2,35,3,0,5
	.byte	'Ifx_DMA_TSR_Bits',0,4,248,4,3
	.word	8951
	.byte	10,4,128,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,7
	.byte	'int',0,4,5,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	413
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_ACCEN00',0,4,133,5,3
	.word	9252
	.byte	10,4,136,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	972
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_ACCEN01',0,4,141,5,3
	.word	9324
	.byte	10,4,144,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1067
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_ACCEN10',0,4,149,5,3
	.word	9389
	.byte	10,4,152,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1626
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_ACCEN11',0,4,157,5,3
	.word	9454
	.byte	10,4,160,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	1706
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_ACCEN20',0,4,165,5,3
	.word	9519
	.byte	10,4,168,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2267
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_ACCEN21',0,4,173,5,3
	.word	9584
	.byte	10,4,176,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2348
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_ACCEN30',0,4,181,5,3
	.word	9649
	.byte	10,4,184,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2909
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_ACCEN31',0,4,189,5,3
	.word	9714
	.byte	10,4,192,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	2990
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_CLRE',0,4,197,5,3
	.word	9779
	.byte	10,4,200,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3264
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_EER',0,4,205,5,3
	.word	9845
	.byte	10,4,208,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3478
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ERRSR',0,4,213,5,3
	.word	9910
	.byte	10,4,216,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	3762
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_ADICR',0,4,221,5,3
	.word	9977
	.byte	10,4,224,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4073
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_CHCR',0,4,229,5,3
	.word	10047
	.byte	10,4,232,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4346
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_CHSR',0,4,237,5,3
	.word	10116
	.byte	10,4,240,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4613
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_DADR',0,4,245,5,3
	.word	10185
	.byte	10,4,248,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4696
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_R0',0,4,253,5,3
	.word	10254
	.byte	10,4,128,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4823
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_R1',0,4,133,6,3
	.word	10321
	.byte	10,4,136,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	4950
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_R2',0,4,141,6,3
	.word	10388
	.byte	10,4,144,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5077
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_R3',0,4,149,6,3
	.word	10455
	.byte	10,4,152,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5204
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_R4',0,4,157,6,3
	.word	10522
	.byte	10,4,160,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5331
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_R5',0,4,165,6,3
	.word	10589
	.byte	10,4,168,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5458
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_R6',0,4,173,6,3
	.word	10656
	.byte	10,4,176,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5585
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_R7',0,4,181,6,3
	.word	10723
	.byte	10,4,184,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5712
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_RDCRC',0,4,189,6,3
	.word	10790
	.byte	10,4,192,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5798
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_SADR',0,4,197,6,3
	.word	10860
	.byte	10,4,200,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5881
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_SDCRC',0,4,205,6,3
	.word	10929
	.byte	10,4,208,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	5967
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_SHADR',0,4,213,6,3
	.word	10999
	.byte	10,4,216,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6053
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_BLK_ME_SR',0,4,221,6,3
	.word	11069
	.byte	10,4,224,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6225
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_CH_ADICR',0,4,229,6,3
	.word	11136
	.byte	10,4,232,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6528
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_CH_CHCFGR',0,4,237,6,3
	.word	11202
	.byte	10,4,240,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	6797
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_CH_CHCSR',0,4,245,6,3
	.word	11269
	.byte	10,4,248,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7135
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_CH_DADR',0,4,253,6,3
	.word	11335
	.byte	10,4,128,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7210
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_CH_RDCRCR',0,4,133,7,3
	.word	11400
	.byte	10,4,136,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7290
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_CH_SADR',0,4,141,7,3
	.word	11467
	.byte	10,4,144,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7365
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_CH_SDCRCR',0,4,149,7,3
	.word	11532
	.byte	10,4,152,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7445
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_CH_SHADR',0,4,157,7,3
	.word	11599
	.byte	10,4,160,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7523
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_CLC',0,4,165,7,3
	.word	11665
	.byte	10,4,168,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7666
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_ERRINTR',0,4,173,7,3
	.word	11726
	.byte	10,4,176,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7762
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_HRR',0,4,181,7,3
	.word	11791
	.byte	10,4,184,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7850
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_ID',0,4,189,7,3
	.word	11852
	.byte	10,4,192,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	7957
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_MEMCON',0,4,197,7,3
	.word	11912
	.byte	10,4,200,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8230
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_MODE',0,4,205,7,3
	.word	11976
	.byte	10,4,208,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8321
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_OTSS',0,4,213,7,3
	.word	12038
	.byte	10,4,216,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8447
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_PRR0',0,4,221,7,3
	.word	12100
	.byte	10,4,224,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8568
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_PRR1',0,4,229,7,3
	.word	12162
	.byte	10,4,232,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8689
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_SUSACR',0,4,237,7,3
	.word	12224
	.byte	10,4,240,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8785
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_SUSENR',0,4,245,7,3
	.word	12288
	.byte	10,4,248,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8881
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_TIME',0,4,253,7,3
	.word	12352
	.byte	10,4,128,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	8951
	.byte	2,35,0,0,5
	.byte	'Ifx_DMA_TSR',0,4,133,8,3
	.word	12414
	.byte	8
	.byte	'_Ifx_DMA_BLK_ME',0,4,144,8,25,112,11
	.byte	'SR',0,4
	.word	11069
	.byte	2,35,0,12,12
	.word	309
	.byte	13,11,0,11
	.byte	'reserved_4',0,12
	.word	12509
	.byte	2,35,4,11
	.byte	'R0',0,4
	.word	10254
	.byte	2,35,16,11
	.byte	'R1',0,4
	.word	10321
	.byte	2,35,20,11
	.byte	'R2',0,4
	.word	10388
	.byte	2,35,24,11
	.byte	'R3',0,4
	.word	10455
	.byte	2,35,28,11
	.byte	'R4',0,4
	.word	10522
	.byte	2,35,32,11
	.byte	'R5',0,4
	.word	10589
	.byte	2,35,36,11
	.byte	'R6',0,4
	.word	10656
	.byte	2,35,40,11
	.byte	'R7',0,4
	.word	10723
	.byte	2,35,44,12,32
	.word	309
	.byte	13,31,0,11
	.byte	'reserved_30',0,32
	.word	12634
	.byte	2,35,48,11
	.byte	'RDCRC',0,4
	.word	10790
	.byte	2,35,80,11
	.byte	'SDCRC',0,4
	.word	10929
	.byte	2,35,84,11
	.byte	'SADR',0,4
	.word	10860
	.byte	2,35,88,11
	.byte	'DADR',0,4
	.word	10185
	.byte	2,35,92,11
	.byte	'ADICR',0,4
	.word	9977
	.byte	2,35,96,11
	.byte	'CHCR',0,4
	.word	10047
	.byte	2,35,100,11
	.byte	'SHADR',0,4
	.word	10999
	.byte	2,35,104,11
	.byte	'CHSR',0,4
	.word	10116
	.byte	2,35,108,0,14
	.word	12475
	.byte	5
	.byte	'Ifx_DMA_BLK_ME',0,4,165,8,3
	.word	12781
	.byte	8
	.byte	'_Ifx_DMA_BLK',0,4,178,8,25,128,1,11
	.byte	'EER',0,4
	.word	9845
	.byte	2,35,0,11
	.byte	'ERRSR',0,4
	.word	9910
	.byte	2,35,4,11
	.byte	'CLRE',0,4
	.word	9779
	.byte	2,35,8,12,4
	.word	309
	.byte	13,3,0,11
	.byte	'reserved_C',0,4
	.word	12872
	.byte	2,35,12,14
	.word	12475
	.byte	11
	.byte	'ME',0,112
	.word	12901
	.byte	2,35,16,0,14
	.word	12810
	.byte	5
	.byte	'Ifx_DMA_BLK',0,4,185,8,3
	.word	12919
	.byte	8
	.byte	'_Ifx_DMA_CH',0,4,188,8,25,32,11
	.byte	'RDCRCR',0,4
	.word	11400
	.byte	2,35,0,11
	.byte	'SDCRCR',0,4
	.word	11532
	.byte	2,35,4,11
	.byte	'SADR',0,4
	.word	11467
	.byte	2,35,8,11
	.byte	'DADR',0,4
	.word	11335
	.byte	2,35,12,11
	.byte	'ADICR',0,4
	.word	11136
	.byte	2,35,16,11
	.byte	'CHCFGR',0,4
	.word	11202
	.byte	2,35,20,11
	.byte	'SHADR',0,4
	.word	11599
	.byte	2,35,24,11
	.byte	'CHCSR',0,4
	.word	11269
	.byte	2,35,28,0,14
	.word	12945
	.byte	5
	.byte	'Ifx_DMA_CH',0,4,198,8,3
	.word	13085
	.byte	8
	.byte	'_Ifx_SCU_ACCEN0_Bits',0,5,45,16,4,9
	.byte	'EN0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'EN1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'EN2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'EN3',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'EN4',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'EN5',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'EN6',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'EN7',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'EN8',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'EN9',0,1
	.word	309
	.byte	1,6,2,35,1,9
	.byte	'EN10',0,1
	.word	309
	.byte	1,5,2,35,1,9
	.byte	'EN11',0,1
	.word	309
	.byte	1,4,2,35,1,9
	.byte	'EN12',0,1
	.word	309
	.byte	1,3,2,35,1,9
	.byte	'EN13',0,1
	.word	309
	.byte	1,2,2,35,1,9
	.byte	'EN14',0,1
	.word	309
	.byte	1,1,2,35,1,9
	.byte	'EN15',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'EN16',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'EN17',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'EN18',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'EN19',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'EN20',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'EN21',0,1
	.word	309
	.byte	1,2,2,35,2,9
	.byte	'EN22',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'EN23',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'EN24',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'EN25',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'EN26',0,1
	.word	309
	.byte	1,5,2,35,3,9
	.byte	'EN27',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'EN28',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'EN29',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'EN30',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'EN31',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_ACCEN0_Bits',0,5,79,3
	.word	13110
	.byte	8
	.byte	'_Ifx_SCU_ACCEN1_Bits',0,5,82,16,4,9
	.byte	'reserved_0',0,4
	.word	999
	.byte	32,0,2,35,2,0,5
	.byte	'Ifx_SCU_ACCEN1_Bits',0,5,85,3
	.word	13667
	.byte	8
	.byte	'_Ifx_SCU_ARSTDIS_Bits',0,5,88,16,4,9
	.byte	'STM0DIS',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'STM1DIS',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'STM2DIS',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,4
	.word	999
	.byte	29,0,2,35,2,0,5
	.byte	'Ifx_SCU_ARSTDIS_Bits',0,5,94,3
	.word	13744
	.byte	8
	.byte	'_Ifx_SCU_CCUCON0_Bits',0,5,97,16,4,9
	.byte	'reserved_0',0,1
	.word	309
	.byte	4,4,2,35,0,9
	.byte	'BAUD2DIV',0,1
	.word	309
	.byte	4,0,2,35,0,9
	.byte	'SRIDIV',0,1
	.word	309
	.byte	4,4,2,35,1,9
	.byte	'LPDIV',0,1
	.word	309
	.byte	4,0,2,35,1,9
	.byte	'SPBDIV',0,1
	.word	309
	.byte	4,4,2,35,2,9
	.byte	'FSI2DIV',0,1
	.word	309
	.byte	2,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	309
	.byte	2,0,2,35,2,9
	.byte	'FSIDIV',0,1
	.word	309
	.byte	2,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	309
	.byte	2,4,2,35,3,9
	.byte	'CLKSEL',0,1
	.word	309
	.byte	2,2,2,35,3,9
	.byte	'UP',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CCUCON0_Bits',0,5,111,3
	.word	13880
	.byte	8
	.byte	'_Ifx_SCU_CCUCON1_Bits',0,5,114,16,4,9
	.byte	'CANDIV',0,1
	.word	309
	.byte	4,4,2,35,0,9
	.byte	'ERAYDIV',0,1
	.word	309
	.byte	4,0,2,35,0,9
	.byte	'STMDIV',0,1
	.word	309
	.byte	4,4,2,35,1,9
	.byte	'GTMDIV',0,1
	.word	309
	.byte	4,0,2,35,1,9
	.byte	'ETHDIV',0,1
	.word	309
	.byte	4,4,2,35,2,9
	.byte	'ASCLINFDIV',0,1
	.word	309
	.byte	4,0,2,35,2,9
	.byte	'ASCLINSDIV',0,1
	.word	309
	.byte	4,4,2,35,3,9
	.byte	'INSEL',0,1
	.word	309
	.byte	2,2,2,35,3,9
	.byte	'UP',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CCUCON1_Bits',0,5,126,3
	.word	14162
	.byte	8
	.byte	'_Ifx_SCU_CCUCON2_Bits',0,5,129,1,16,4,9
	.byte	'BBBDIV',0,1
	.word	309
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	999
	.byte	26,2,2,35,2,9
	.byte	'UP',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CCUCON2_Bits',0,5,135,1,3
	.word	14400
	.byte	8
	.byte	'_Ifx_SCU_CCUCON3_Bits',0,5,138,1,16,4,9
	.byte	'PLLDIV',0,1
	.word	309
	.byte	6,2,2,35,0,9
	.byte	'PLLSEL',0,1
	.word	309
	.byte	2,0,2,35,0,9
	.byte	'PLLERAYDIV',0,1
	.word	309
	.byte	6,2,2,35,1,9
	.byte	'PLLERAYSEL',0,1
	.word	309
	.byte	2,0,2,35,1,9
	.byte	'SRIDIV',0,1
	.word	309
	.byte	6,2,2,35,2,9
	.byte	'SRISEL',0,1
	.word	309
	.byte	2,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	6,2,2,35,3,9
	.byte	'UP',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CCUCON3_Bits',0,5,149,1,3
	.word	14528
	.byte	8
	.byte	'_Ifx_SCU_CCUCON4_Bits',0,5,152,1,16,4,9
	.byte	'SPBDIV',0,1
	.word	309
	.byte	6,2,2,35,0,9
	.byte	'SPBSEL',0,1
	.word	309
	.byte	2,0,2,35,0,9
	.byte	'GTMDIV',0,1
	.word	309
	.byte	6,2,2,35,1,9
	.byte	'GTMSEL',0,1
	.word	309
	.byte	2,0,2,35,1,9
	.byte	'STMDIV',0,1
	.word	309
	.byte	6,2,2,35,2,9
	.byte	'STMSEL',0,1
	.word	309
	.byte	2,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	6,2,2,35,3,9
	.byte	'UP',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CCUCON4_Bits',0,5,163,1,3
	.word	14755
	.byte	8
	.byte	'_Ifx_SCU_CCUCON5_Bits',0,5,166,1,16,4,9
	.byte	'MAXDIV',0,1
	.word	309
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	999
	.byte	26,2,2,35,2,9
	.byte	'UP',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CCUCON5_Bits',0,5,172,1,3
	.word	14974
	.byte	8
	.byte	'_Ifx_SCU_CCUCON6_Bits',0,5,175,1,16,4,9
	.byte	'CPU0DIV',0,1
	.word	309
	.byte	6,2,2,35,0,9
	.byte	'reserved_6',0,4
	.word	999
	.byte	26,0,2,35,2,0,5
	.byte	'Ifx_SCU_CCUCON6_Bits',0,5,179,1,3
	.word	15102
	.byte	8
	.byte	'_Ifx_SCU_CHIPID_Bits',0,5,182,1,16,4,9
	.byte	'CHREV',0,1
	.word	309
	.byte	6,2,2,35,0,9
	.byte	'CHTEC',0,1
	.word	309
	.byte	2,0,2,35,0,9
	.byte	'CHID',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'EEA',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'UCODE',0,1
	.word	309
	.byte	7,0,2,35,2,9
	.byte	'FSIZE',0,1
	.word	309
	.byte	4,4,2,35,3,9
	.byte	'SP',0,1
	.word	309
	.byte	2,2,2,35,3,9
	.byte	'SEC',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_CHIPID_Bits',0,5,193,1,3
	.word	15202
	.byte	8
	.byte	'_Ifx_SCU_DTSCON_Bits',0,5,196,1,16,4,9
	.byte	'PWD',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'START',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	309
	.byte	2,4,2,35,0,9
	.byte	'CAL',0,4
	.word	999
	.byte	22,6,2,35,2,9
	.byte	'reserved_26',0,1
	.word	309
	.byte	5,1,2,35,3,9
	.byte	'SLCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_DTSCON_Bits',0,5,204,1,3
	.word	15410
	.byte	8
	.byte	'_Ifx_SCU_DTSLIM_Bits',0,5,207,1,16,4,9
	.byte	'LOWER',0,2
	.word	340
	.byte	10,6,2,35,0,9
	.byte	'reserved_10',0,1
	.word	309
	.byte	5,1,2,35,1,9
	.byte	'LLU',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'UPPER',0,2
	.word	340
	.byte	10,6,2,35,2,9
	.byte	'reserved_26',0,1
	.word	309
	.byte	4,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'UOF',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_DTSLIM_Bits',0,5,216,1,3
	.word	15575
	.byte	8
	.byte	'_Ifx_SCU_DTSSTAT_Bits',0,5,219,1,16,4,9
	.byte	'RESULT',0,2
	.word	340
	.byte	10,6,2,35,0,9
	.byte	'reserved_10',0,1
	.word	309
	.byte	4,2,2,35,1,9
	.byte	'RDY',0,1
	.word	309
	.byte	1,1,2,35,1,9
	.byte	'BUSY',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_DTSSTAT_Bits',0,5,226,1,3
	.word	15758
	.byte	8
	.byte	'_Ifx_SCU_EICR_Bits',0,5,229,1,16,4,9
	.byte	'reserved_0',0,1
	.word	309
	.byte	4,4,2,35,0,9
	.byte	'EXIS0',0,1
	.word	309
	.byte	3,1,2,35,0,9
	.byte	'reserved_7',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'FEN0',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'REN0',0,1
	.word	309
	.byte	1,6,2,35,1,9
	.byte	'LDEN0',0,1
	.word	309
	.byte	1,5,2,35,1,9
	.byte	'EIEN0',0,1
	.word	309
	.byte	1,4,2,35,1,9
	.byte	'INP0',0,1
	.word	309
	.byte	3,1,2,35,1,9
	.byte	'reserved_15',0,4
	.word	999
	.byte	5,12,2,35,2,9
	.byte	'EXIS1',0,1
	.word	309
	.byte	3,1,2,35,2,9
	.byte	'reserved_23',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'FEN1',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'REN1',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'LDEN1',0,1
	.word	309
	.byte	1,5,2,35,3,9
	.byte	'EIEN1',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'INP1',0,1
	.word	309
	.byte	3,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EICR_Bits',0,5,248,1,3
	.word	15912
	.byte	8
	.byte	'_Ifx_SCU_EIFR_Bits',0,5,251,1,16,4,9
	.byte	'INTF0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'INTF1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'INTF2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'INTF3',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'INTF4',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'INTF5',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'INTF6',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'INTF7',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	999
	.byte	24,0,2,35,2,0,5
	.byte	'Ifx_SCU_EIFR_Bits',0,5,134,2,3
	.word	16276
	.byte	8
	.byte	'_Ifx_SCU_EMSR_Bits',0,5,137,2,16,4,9
	.byte	'POL',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'MODE',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'ENON',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'PSEL',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,2
	.word	340
	.byte	12,0,2,35,0,9
	.byte	'EMSF',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'SEMSF',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	309
	.byte	6,0,2,35,2,9
	.byte	'EMSFM',0,1
	.word	309
	.byte	2,6,2,35,3,9
	.byte	'SEMSFM',0,1
	.word	309
	.byte	2,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	309
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_SCU_EMSR_Bits',0,5,150,2,3
	.word	16487
	.byte	8
	.byte	'_Ifx_SCU_ESRCFG_Bits',0,5,153,2,16,4,9
	.byte	'reserved_0',0,1
	.word	309
	.byte	7,1,2,35,0,9
	.byte	'EDCON',0,2
	.word	340
	.byte	2,7,2,35,0,9
	.byte	'reserved_9',0,4
	.word	999
	.byte	23,0,2,35,2,0,5
	.byte	'Ifx_SCU_ESRCFG_Bits',0,5,158,2,3
	.word	16739
	.byte	8
	.byte	'_Ifx_SCU_ESROCFG_Bits',0,5,161,2,16,4,9
	.byte	'ARI',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'ARC',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	999
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_SCU_ESROCFG_Bits',0,5,166,2,3
	.word	16857
	.byte	8
	.byte	'_Ifx_SCU_EVR13CON_Bits',0,5,169,2,16,4,9
	.byte	'reserved_0',0,4
	.word	999
	.byte	28,4,2,35,2,9
	.byte	'EVR13OFF',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'BPEVR13OFF',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVR13CON_Bits',0,5,176,2,3
	.word	16968
	.byte	8
	.byte	'_Ifx_SCU_EVRADCSTAT_Bits',0,5,179,2,16,4,9
	.byte	'ADC13V',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'ADCSWDV',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	7,1,2,35,3,9
	.byte	'VAL',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRADCSTAT_Bits',0,5,186,2,3
	.word	17131
	.byte	8
	.byte	'_Ifx_SCU_EVRMONCTRL_Bits',0,5,189,2,16,4,9
	.byte	'EVR13OVMOD',0,1
	.word	309
	.byte	2,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	309
	.byte	2,4,2,35,0,9
	.byte	'EVR13UVMOD',0,1
	.word	309
	.byte	2,2,2,35,0,9
	.byte	'reserved_6',0,2
	.word	340
	.byte	10,0,2,35,0,9
	.byte	'SWDOVMOD',0,1
	.word	309
	.byte	2,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	309
	.byte	2,4,2,35,2,9
	.byte	'SWDUVMOD',0,1
	.word	309
	.byte	2,2,2,35,2,9
	.byte	'reserved_22',0,2
	.word	340
	.byte	8,2,2,35,2,9
	.byte	'SLCK',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRMONCTRL_Bits',0,5,201,2,3
	.word	17293
	.byte	8
	.byte	'_Ifx_SCU_EVROVMON_Bits',0,5,204,2,16,4,9
	.byte	'EVR13OVVAL',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'SWDOVVAL',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	6,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVROVMON_Bits',0,5,212,2,3
	.word	17571
	.byte	8
	.byte	'_Ifx_SCU_EVRRSTCON_Bits',0,5,215,2,16,4,9
	.byte	'reserved_0',0,4
	.word	999
	.byte	28,4,2,35,2,9
	.byte	'RSTSWDOFF',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'BPRSTSWDOFF',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRRSTCON_Bits',0,5,222,2,3
	.word	17750
	.byte	8
	.byte	'_Ifx_SCU_EVRSDCOEFF2_Bits',0,5,225,2,16,4,9
	.byte	'SD33P',0,1
	.word	309
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	309
	.byte	4,0,2,35,0,9
	.byte	'SD33I',0,1
	.word	309
	.byte	4,4,2,35,1,9
	.byte	'reserved_12',0,4
	.word	999
	.byte	19,1,2,35,2,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRSDCOEFF2_Bits',0,5,232,2,3
	.word	17910
	.byte	8
	.byte	'_Ifx_SCU_EVRSDCTRL1_Bits',0,5,235,2,16,4,9
	.byte	'SDFREQSPRD',0,1
	.word	309
	.byte	4,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	309
	.byte	4,0,2,35,0,9
	.byte	'TON',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'TOFF',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'SDSTEP',0,1
	.word	309
	.byte	4,4,2,35,3,9
	.byte	'SYNCDIV',0,1
	.word	309
	.byte	3,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRSDCTRL1_Bits',0,5,244,2,3
	.word	18071
	.byte	8
	.byte	'_Ifx_SCU_EVRSDCTRL2_Bits',0,5,247,2,16,4,9
	.byte	'reserved_0',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'STBS',0,1
	.word	309
	.byte	2,6,2,35,1,9
	.byte	'STSP',0,1
	.word	309
	.byte	2,4,2,35,1,9
	.byte	'NS',0,1
	.word	309
	.byte	2,2,2,35,1,9
	.byte	'OL',0,1
	.word	309
	.byte	1,1,2,35,1,9
	.byte	'PIAD',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'ADCMODE',0,1
	.word	309
	.byte	4,4,2,35,2,9
	.byte	'ADCLPF',0,1
	.word	309
	.byte	2,2,2,35,2,9
	.byte	'ADCLSB',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'reserved_23',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'SDLUT',0,1
	.word	309
	.byte	6,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRSDCTRL2_Bits',0,5,134,3,3
	.word	18263
	.byte	8
	.byte	'_Ifx_SCU_EVRSDCTRL3_Bits',0,5,137,3,16,4,9
	.byte	'SDOLCON',0,1
	.word	309
	.byte	7,1,2,35,0,9
	.byte	'MODSEL',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'MODLOW',0,1
	.word	309
	.byte	7,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'SDVOKLVL',0,1
	.word	309
	.byte	6,2,2,35,2,9
	.byte	'MODMAN',0,1
	.word	309
	.byte	2,0,2,35,2,9
	.byte	'MODHIGH',0,1
	.word	309
	.byte	7,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRSDCTRL3_Bits',0,5,147,3,3
	.word	18559
	.byte	8
	.byte	'_Ifx_SCU_EVRSTAT_Bits',0,5,150,3,16,4,9
	.byte	'EVR13',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'OV13',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	309
	.byte	2,4,2,35,0,9
	.byte	'OVSWD',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'UV13',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'UVSWD',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	309
	.byte	2,6,2,35,1,9
	.byte	'BGPROK',0,1
	.word	309
	.byte	1,5,2,35,1,9
	.byte	'reserved_11',0,1
	.word	309
	.byte	1,4,2,35,1,9
	.byte	'SCMOD',0,1
	.word	309
	.byte	2,2,2,35,1,9
	.byte	'reserved_14',0,4
	.word	999
	.byte	18,0,2,35,2,0,5
	.byte	'Ifx_SCU_EVRSTAT_Bits',0,5,164,3,3
	.word	18774
	.byte	8
	.byte	'_Ifx_SCU_EVRUVMON_Bits',0,5,167,3,16,4,9
	.byte	'EVR13UVVAL',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'SWDUVVAL',0,1
	.word	309
	.byte	8,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	6,2,2,35,3,9
	.byte	'SLCK',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_EVRUVMON_Bits',0,5,175,3,3
	.word	19063
	.byte	8
	.byte	'_Ifx_SCU_EXTCON_Bits',0,5,178,3,16,4,9
	.byte	'EN0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'SEL0',0,1
	.word	309
	.byte	4,2,2,35,0,9
	.byte	'reserved_6',0,2
	.word	340
	.byte	10,0,2,35,0,9
	.byte	'EN1',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'NSEL',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'SEL1',0,1
	.word	309
	.byte	4,2,2,35,2,9
	.byte	'reserved_22',0,1
	.word	309
	.byte	2,0,2,35,2,9
	.byte	'DIV1',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_SCU_EXTCON_Bits',0,5,189,3,3
	.word	19242
	.byte	8
	.byte	'_Ifx_SCU_FDR_Bits',0,5,192,3,16,4,9
	.byte	'STEP',0,2
	.word	340
	.byte	10,6,2,35,0,9
	.byte	'reserved_10',0,1
	.word	309
	.byte	4,2,2,35,1,9
	.byte	'DM',0,1
	.word	309
	.byte	2,0,2,35,1,9
	.byte	'RESULT',0,2
	.word	340
	.byte	10,6,2,35,2,9
	.byte	'reserved_26',0,1
	.word	309
	.byte	5,1,2,35,3,9
	.byte	'DISCLK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_FDR_Bits',0,5,200,3,3
	.word	19460
	.byte	8
	.byte	'_Ifx_SCU_FMR_Bits',0,5,203,3,16,4,9
	.byte	'FS0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'FS1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'FS2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'FS3',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'FS4',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'FS5',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'FS6',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'FS7',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'FC0',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'FC1',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'FC2',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'FC3',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'FC4',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'FC5',0,1
	.word	309
	.byte	1,2,2,35,2,9
	.byte	'FC6',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'FC7',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_SCU_FMR_Bits',0,5,223,3,3
	.word	19623
	.byte	8
	.byte	'_Ifx_SCU_ID_Bits',0,5,226,3,16,4,9
	.byte	'MODREV',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'MODTYPE',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'MODNUMBER',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_ID_Bits',0,5,231,3,3
	.word	19959
	.byte	8
	.byte	'_Ifx_SCU_IGCR_Bits',0,5,234,3,16,4,9
	.byte	'IPEN00',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'IPEN01',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'IPEN02',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'IPEN03',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'IPEN04',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'IPEN05',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'IPEN06',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'IPEN07',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	309
	.byte	5,3,2,35,1,9
	.byte	'GEEN0',0,1
	.word	309
	.byte	1,2,2,35,1,9
	.byte	'IGP0',0,1
	.word	309
	.byte	2,0,2,35,1,9
	.byte	'IPEN10',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'IPEN11',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'IPEN12',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'IPEN13',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'IPEN14',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'IPEN15',0,1
	.word	309
	.byte	1,2,2,35,2,9
	.byte	'IPEN16',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'IPEN17',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	5,3,2,35,3,9
	.byte	'GEEN1',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'IGP1',0,1
	.word	309
	.byte	2,0,2,35,3,0,5
	.byte	'Ifx_SCU_IGCR_Bits',0,5,130,4,3
	.word	20066
	.byte	8
	.byte	'_Ifx_SCU_IN_Bits',0,5,133,4,16,4,9
	.byte	'P0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'P1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	999
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_SCU_IN_Bits',0,5,138,4,3
	.word	20518
	.byte	8
	.byte	'_Ifx_SCU_IOCR_Bits',0,5,141,4,16,4,9
	.byte	'reserved_0',0,1
	.word	309
	.byte	4,4,2,35,0,9
	.byte	'PC0',0,1
	.word	309
	.byte	4,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	309
	.byte	4,4,2,35,1,9
	.byte	'PC1',0,1
	.word	309
	.byte	4,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_IOCR_Bits',0,5,148,4,3
	.word	20617
	.byte	8
	.byte	'_Ifx_SCU_LBISTCTRL0_Bits',0,5,151,4,16,4,9
	.byte	'LBISTREQ',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'LBISTREQP',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'PATTERNS',0,2
	.word	340
	.byte	14,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_LBISTCTRL0_Bits',0,5,157,4,3
	.word	20767
	.byte	8
	.byte	'_Ifx_SCU_LBISTCTRL1_Bits',0,5,160,4,16,4,9
	.byte	'SEED',0,4
	.word	999
	.byte	23,9,2,35,2,9
	.byte	'reserved_23',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'SPLITSH',0,1
	.word	309
	.byte	3,5,2,35,3,9
	.byte	'BODY',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'LBISTFREQU',0,1
	.word	309
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_SCU_LBISTCTRL1_Bits',0,5,167,4,3
	.word	20916
	.byte	8
	.byte	'_Ifx_SCU_LBISTCTRL2_Bits',0,5,170,4,16,4,9
	.byte	'SIGNATURE',0,4
	.word	999
	.byte	24,8,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	7,1,2,35,3,9
	.byte	'LBISTDONE',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_LBISTCTRL2_Bits',0,5,175,4,3
	.word	21077
	.byte	8
	.byte	'_Ifx_SCU_LCLCON0_Bits',0,5,178,4,16,4,9
	.byte	'reserved_0',0,2
	.word	340
	.byte	16,0,2,35,0,9
	.byte	'LS',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,2
	.word	340
	.byte	14,1,2,35,2,9
	.byte	'LSEN',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_LCLCON0_Bits',0,5,184,4,3
	.word	21207
	.byte	8
	.byte	'_Ifx_SCU_LCLTEST_Bits',0,5,187,4,16,4,9
	.byte	'LCLT0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'LCLT1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	999
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_SCU_LCLTEST_Bits',0,5,192,4,3
	.word	21341
	.byte	8
	.byte	'_Ifx_SCU_MANID_Bits',0,5,195,4,16,4,9
	.byte	'DEPT',0,1
	.word	309
	.byte	5,3,2,35,0,9
	.byte	'MANUF',0,2
	.word	340
	.byte	11,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_MANID_Bits',0,5,200,4,3
	.word	21456
	.byte	8
	.byte	'_Ifx_SCU_OMR_Bits',0,5,203,4,16,4,9
	.byte	'PS0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'PS1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,2
	.word	340
	.byte	14,0,2,35,0,9
	.byte	'PCL0',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'PCL1',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,2
	.word	340
	.byte	14,0,2,35,2,0,5
	.byte	'Ifx_SCU_OMR_Bits',0,5,211,4,3
	.word	21567
	.byte	8
	.byte	'_Ifx_SCU_OSCCON_Bits',0,5,214,4,16,4,9
	.byte	'reserved_0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'PLLLV',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'OSCRES',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'GAINSEL',0,1
	.word	309
	.byte	2,3,2,35,0,9
	.byte	'MODE',0,1
	.word	309
	.byte	2,1,2,35,0,9
	.byte	'SHBY',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'PLLHV',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,1
	.word	309
	.byte	1,6,2,35,1,9
	.byte	'X1D',0,1
	.word	309
	.byte	1,5,2,35,1,9
	.byte	'X1DEN',0,1
	.word	309
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	309
	.byte	4,0,2,35,1,9
	.byte	'OSCVAL',0,1
	.word	309
	.byte	5,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	309
	.byte	2,1,2,35,2,9
	.byte	'APREN',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	8,0,2,35,3,0,5
	.byte	'Ifx_SCU_OSCCON_Bits',0,5,231,4,3
	.word	21725
	.byte	8
	.byte	'_Ifx_SCU_OUT_Bits',0,5,234,4,16,4,9
	.byte	'P0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'P1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	999
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_SCU_OUT_Bits',0,5,239,4,3
	.word	22065
	.byte	8
	.byte	'_Ifx_SCU_OVCCON_Bits',0,5,242,4,16,4,9
	.byte	'CSEL0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'CSEL1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'CSEL2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,2
	.word	340
	.byte	13,0,2,35,0,9
	.byte	'OVSTRT',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'OVSTP',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'DCINVAL',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	309
	.byte	5,0,2,35,2,9
	.byte	'OVCONF',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'POVCONF',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	309
	.byte	6,0,2,35,3,0,5
	.byte	'Ifx_SCU_OVCCON_Bits',0,5,255,4,3
	.word	22166
	.byte	8
	.byte	'_Ifx_SCU_OVCENABLE_Bits',0,5,130,5,16,4,9
	.byte	'OVEN0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'OVEN1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'OVEN2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,4
	.word	999
	.byte	29,0,2,35,2,0,5
	.byte	'Ifx_SCU_OVCENABLE_Bits',0,5,136,5,3
	.word	22433
	.byte	8
	.byte	'_Ifx_SCU_PDISC_Bits',0,5,139,5,16,4,9
	.byte	'PDIS0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'PDIS1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	999
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_SCU_PDISC_Bits',0,5,144,5,3
	.word	22569
	.byte	8
	.byte	'_Ifx_SCU_PDR_Bits',0,5,147,5,16,4,9
	.byte	'PD0',0,1
	.word	309
	.byte	3,5,2,35,0,9
	.byte	'PL0',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'PD1',0,1
	.word	309
	.byte	3,1,2,35,0,9
	.byte	'PL1',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	999
	.byte	24,0,2,35,2,0,5
	.byte	'Ifx_SCU_PDR_Bits',0,5,154,5,3
	.word	22680
	.byte	8
	.byte	'_Ifx_SCU_PDRR_Bits',0,5,157,5,16,4,9
	.byte	'PDR0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'PDR1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'PDR2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'PDR3',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'PDR4',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'PDR5',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'PDR6',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'PDR7',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	999
	.byte	24,0,2,35,2,0,5
	.byte	'Ifx_SCU_PDRR_Bits',0,5,168,5,3
	.word	22813
	.byte	8
	.byte	'_Ifx_SCU_PLLCON0_Bits',0,5,171,5,16,4,9
	.byte	'VCOBYP',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'VCOPWD',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'MODEN',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'SETFINDIS',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'CLRFINDIS',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'OSCDISCDIS',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'reserved_7',0,2
	.word	340
	.byte	2,7,2,35,0,9
	.byte	'NDIV',0,1
	.word	309
	.byte	7,0,2,35,1,9
	.byte	'PLLPWD',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'RESLD',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	309
	.byte	5,0,2,35,2,9
	.byte	'PDIV',0,1
	.word	309
	.byte	4,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	309
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_SCU_PLLCON0_Bits',0,5,188,5,3
	.word	23016
	.byte	8
	.byte	'_Ifx_SCU_PLLCON1_Bits',0,5,191,5,16,4,9
	.byte	'K2DIV',0,1
	.word	309
	.byte	7,1,2,35,0,9
	.byte	'reserved_7',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'K3DIV',0,1
	.word	309
	.byte	7,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'K1DIV',0,1
	.word	309
	.byte	7,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	340
	.byte	9,0,2,35,2,0,5
	.byte	'Ifx_SCU_PLLCON1_Bits',0,5,199,5,3
	.word	23372
	.byte	8
	.byte	'_Ifx_SCU_PLLCON2_Bits',0,5,202,5,16,4,9
	.byte	'MODCFG',0,2
	.word	340
	.byte	16,0,2,35,0,9
	.byte	'reserved_16',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_PLLCON2_Bits',0,5,206,5,3
	.word	23550
	.byte	8
	.byte	'_Ifx_SCU_PLLERAYCON0_Bits',0,5,209,5,16,4,9
	.byte	'VCOBYP',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'VCOPWD',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	309
	.byte	2,4,2,35,0,9
	.byte	'SETFINDIS',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'CLRFINDIS',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'OSCDISCDIS',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'reserved_7',0,2
	.word	340
	.byte	2,7,2,35,0,9
	.byte	'NDIV',0,1
	.word	309
	.byte	5,2,2,35,1,9
	.byte	'reserved_14',0,1
	.word	309
	.byte	2,0,2,35,1,9
	.byte	'PLLPWD',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'RESLD',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	309
	.byte	5,0,2,35,2,9
	.byte	'PDIV',0,1
	.word	309
	.byte	4,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	309
	.byte	4,0,2,35,3,0,5
	.byte	'Ifx_SCU_PLLERAYCON0_Bits',0,5,226,5,3
	.word	23650
	.byte	8
	.byte	'_Ifx_SCU_PLLERAYCON1_Bits',0,5,229,5,16,4,9
	.byte	'K2DIV',0,1
	.word	309
	.byte	7,1,2,35,0,9
	.byte	'reserved_7',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'K3DIV',0,1
	.word	309
	.byte	4,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	309
	.byte	4,0,2,35,1,9
	.byte	'K1DIV',0,1
	.word	309
	.byte	7,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	340
	.byte	9,0,2,35,2,0,5
	.byte	'Ifx_SCU_PLLERAYCON1_Bits',0,5,237,5,3
	.word	24020
	.byte	8
	.byte	'_Ifx_SCU_PLLERAYSTAT_Bits',0,5,240,5,16,4,9
	.byte	'VCOBYST',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'PWDSTAT',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'VCOLOCK',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'FINDIS',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'K1RDY',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'K2RDY',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,4
	.word	999
	.byte	26,0,2,35,2,0,5
	.byte	'Ifx_SCU_PLLERAYSTAT_Bits',0,5,249,5,3
	.word	24206
	.byte	8
	.byte	'_Ifx_SCU_PLLSTAT_Bits',0,5,252,5,16,4,9
	.byte	'VCOBYST',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'VCOLOCK',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'FINDIS',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'K1RDY',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'K2RDY',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'reserved_6',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'MODRUN',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,4
	.word	999
	.byte	24,0,2,35,2,0,5
	.byte	'Ifx_SCU_PLLSTAT_Bits',0,5,135,6,3
	.word	24404
	.byte	8
	.byte	'_Ifx_SCU_PMCSR_Bits',0,5,138,6,16,4,9
	.byte	'REQSLP',0,1
	.word	309
	.byte	2,6,2,35,0,9
	.byte	'SMUSLP',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'reserved_3',0,1
	.word	309
	.byte	5,0,2,35,0,9
	.byte	'PMST',0,1
	.word	309
	.byte	3,5,2,35,1,9
	.byte	'reserved_11',0,4
	.word	999
	.byte	21,0,2,35,2,0,5
	.byte	'Ifx_SCU_PMCSR_Bits',0,5,145,6,3
	.word	24637
	.byte	8
	.byte	'_Ifx_SCU_PMSWCR0_Bits',0,5,148,6,16,4,9
	.byte	'reserved_0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'ESR1WKEN',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'PINAWKEN',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'PINBWKEN',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'ESR0DFEN',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'ESR0EDCON',0,1
	.word	309
	.byte	2,1,2,35,0,9
	.byte	'ESR1DFEN',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'ESR1EDCON',0,1
	.word	309
	.byte	2,6,2,35,1,9
	.byte	'PINADFEN',0,1
	.word	309
	.byte	1,5,2,35,1,9
	.byte	'PINAEDCON',0,1
	.word	309
	.byte	2,3,2,35,1,9
	.byte	'PINBDFEN',0,1
	.word	309
	.byte	1,2,2,35,1,9
	.byte	'PINBEDCON',0,1
	.word	309
	.byte	2,0,2,35,1,9
	.byte	'reserved_16',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'STBYRAMSEL',0,1
	.word	309
	.byte	2,5,2,35,2,9
	.byte	'reserved_19',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'WUTWKEN',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	309
	.byte	2,1,2,35,2,9
	.byte	'PORSTDF',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'DCDCSYNC',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	309
	.byte	3,3,2,35,3,9
	.byte	'ESR0TRIST',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'reserved_30',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_PMSWCR0_Bits',0,5,174,6,3
	.word	24789
	.byte	8
	.byte	'_Ifx_SCU_PMSWCR1_Bits',0,5,177,6,16,4,9
	.byte	'reserved_0',0,2
	.word	340
	.byte	12,4,2,35,0,9
	.byte	'IRADIS',0,1
	.word	309
	.byte	1,3,2,35,1,9
	.byte	'reserved_13',0,4
	.word	999
	.byte	14,5,2,35,2,9
	.byte	'STBYEVEN',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'STBYEV',0,1
	.word	309
	.byte	3,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_PMSWCR1_Bits',0,5,185,6,3
	.word	25348
	.byte	8
	.byte	'_Ifx_SCU_PMSWCR3_Bits',0,5,188,6,16,4,9
	.byte	'WUTREL',0,4
	.word	999
	.byte	24,8,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	4,4,2,35,3,9
	.byte	'WUTDIV',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'WUTEN',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'WUTMODE',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'LCK',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_PMSWCR3_Bits',0,5,196,6,3
	.word	25531
	.byte	8
	.byte	'_Ifx_SCU_PMSWSTAT_Bits',0,5,199,6,16,4,9
	.byte	'reserved_0',0,1
	.word	309
	.byte	2,6,2,35,0,9
	.byte	'ESR1WKP',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'ESR1OVRUN',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'PINAWKP',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'PINAOVRUN',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'PINBWKP',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'PINBOVRUN',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'PORSTDF',0,1
	.word	309
	.byte	1,6,2,35,1,9
	.byte	'HWCFGEVR',0,1
	.word	309
	.byte	3,3,2,35,1,9
	.byte	'STBYRAM',0,1
	.word	309
	.byte	2,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'WUTWKP',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'WUTOVRUN',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'WUTWKEN',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'ESR1WKEN',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'PINAWKEN',0,1
	.word	309
	.byte	1,2,2,35,2,9
	.byte	'PINBWKEN',0,1
	.word	309
	.byte	1,1,2,35,2,9
	.byte	'reserved_23',0,2
	.word	340
	.byte	4,5,2,35,2,9
	.byte	'ESR0TRIST',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'reserved_28',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'WUTEN',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'WUTMODE',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'WUTRUN',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_PMSWSTAT_Bits',0,5,226,6,3
	.word	25700
	.byte	8
	.byte	'_Ifx_SCU_PMSWSTATCLR_Bits',0,5,229,6,16,4,9
	.byte	'reserved_0',0,1
	.word	309
	.byte	2,6,2,35,0,9
	.byte	'ESR1WKPCLR',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'ESR1OVRUNCLR',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'PINAWKPCLR',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'PINAOVRUNCLR',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'PINBWKPCLR',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'PINBOVRUNCLR',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'WUTWKPCLR',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'WUTOVRUNCLR',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,2
	.word	340
	.byte	14,0,2,35,2,0,5
	.byte	'Ifx_SCU_PMSWSTATCLR_Bits',0,5,242,6,3
	.word	26267
	.byte	8
	.byte	'_Ifx_SCU_PMSWUTCNT_Bits',0,5,245,6,16,4,9
	.byte	'WUTCNT',0,4
	.word	999
	.byte	24,8,2,35,2,9
	.byte	'reserved_24',0,1
	.word	309
	.byte	7,1,2,35,3,9
	.byte	'VAL',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SCU_PMSWUTCNT_Bits',0,5,250,6,3
	.word	26583
	.byte	8
	.byte	'_Ifx_SCU_RSTCON2_Bits',0,5,253,6,16,4,9
	.byte	'reserved_0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'CLRC',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,2
	.word	340
	.byte	10,4,2,35,0,9
	.byte	'CSS0',0,1
	.word	309
	.byte	1,3,2,35,1,9
	.byte	'CSS1',0,1
	.word	309
	.byte	1,2,2,35,1,9
	.byte	'CSS2',0,1
	.word	309
	.byte	1,1,2,35,1,9
	.byte	'reserved_15',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'USRINFO',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_RSTCON2_Bits',0,5,135,7,3
	.word	26702
	.byte	8
	.byte	'_Ifx_SCU_RSTCON_Bits',0,5,138,7,16,4,9
	.byte	'ESR0',0,1
	.word	309
	.byte	2,6,2,35,0,9
	.byte	'ESR1',0,1
	.word	309
	.byte	2,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	309
	.byte	2,2,2,35,0,9
	.byte	'SMU',0,1
	.word	309
	.byte	2,0,2,35,0,9
	.byte	'SW',0,1
	.word	309
	.byte	2,6,2,35,1,9
	.byte	'STM0',0,1
	.word	309
	.byte	2,4,2,35,1,9
	.byte	'STM1',0,1
	.word	309
	.byte	2,2,2,35,1,9
	.byte	'STM2',0,1
	.word	309
	.byte	2,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_RSTCON_Bits',0,5,149,7,3
	.word	26911
	.byte	8
	.byte	'_Ifx_SCU_RSTSTAT_Bits',0,5,152,7,16,4,9
	.byte	'ESR0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'ESR1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'SMU',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'SW',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'STM0',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'STM1',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'STM2',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	309
	.byte	8,0,2,35,1,9
	.byte	'PORST',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'reserved_17',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'CB0',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'CB1',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'CB3',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	309
	.byte	2,1,2,35,2,9
	.byte	'EVR13',0,1
	.word	309
	.byte	1,0,2,35,2,9
	.byte	'EVR33',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'SWD',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'reserved_26',0,1
	.word	309
	.byte	2,4,2,35,3,9
	.byte	'STBYR',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'reserved_29',0,1
	.word	309
	.byte	3,0,2,35,3,0,5
	.byte	'Ifx_SCU_RSTSTAT_Bits',0,5,175,7,3
	.word	27122
	.byte	8
	.byte	'_Ifx_SCU_SAFECON_Bits',0,5,178,7,16,4,9
	.byte	'HBT',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,4
	.word	999
	.byte	31,0,2,35,2,0,5
	.byte	'Ifx_SCU_SAFECON_Bits',0,5,182,7,3
	.word	27554
	.byte	8
	.byte	'_Ifx_SCU_STSTAT_Bits',0,5,185,7,16,4,9
	.byte	'HWCFG',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'FTM',0,1
	.word	309
	.byte	7,1,2,35,1,9
	.byte	'MODE',0,1
	.word	309
	.byte	1,0,2,35,1,9
	.byte	'FCBAE',0,1
	.word	309
	.byte	1,7,2,35,2,9
	.byte	'LUDIS',0,1
	.word	309
	.byte	1,6,2,35,2,9
	.byte	'reserved_18',0,1
	.word	309
	.byte	1,5,2,35,2,9
	.byte	'TRSTL',0,1
	.word	309
	.byte	1,4,2,35,2,9
	.byte	'SPDEN',0,1
	.word	309
	.byte	1,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	309
	.byte	3,0,2,35,2,9
	.byte	'RAMINT',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'reserved_25',0,1
	.word	309
	.byte	7,0,2,35,3,0,5
	.byte	'Ifx_SCU_STSTAT_Bits',0,5,198,7,3
	.word	27650
	.byte	8
	.byte	'_Ifx_SCU_SWRSTCON_Bits',0,5,201,7,16,4,9
	.byte	'reserved_0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'SWRSTREQ',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,4
	.word	999
	.byte	30,0,2,35,2,0,5
	.byte	'Ifx_SCU_SWRSTCON_Bits',0,5,206,7,3
	.word	27910
	.byte	8
	.byte	'_Ifx_SCU_SYSCON_Bits',0,5,209,7,16,4,9
	.byte	'CCTRIG0',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'RAMINTM',0,1
	.word	309
	.byte	2,4,2,35,0,9
	.byte	'SETLUDIS',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'reserved_5',0,1
	.word	309
	.byte	3,0,2,35,0,9
	.byte	'DATM',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'reserved_9',0,4
	.word	999
	.byte	23,0,2,35,2,0,5
	.byte	'Ifx_SCU_SYSCON_Bits',0,5,218,7,3
	.word	28035
	.byte	8
	.byte	'_Ifx_SCU_TRAPCLR_Bits',0,5,221,7,16,4,9
	.byte	'ESR0T',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	999
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_SCU_TRAPCLR_Bits',0,5,228,7,3
	.word	28232
	.byte	8
	.byte	'_Ifx_SCU_TRAPDIS_Bits',0,5,231,7,16,4,9
	.byte	'ESR0T',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	999
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_SCU_TRAPDIS_Bits',0,5,238,7,3
	.word	28385
	.byte	8
	.byte	'_Ifx_SCU_TRAPSET_Bits',0,5,241,7,16,4,9
	.byte	'ESR0T',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	999
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_SCU_TRAPSET_Bits',0,5,248,7,3
	.word	28538
	.byte	8
	.byte	'_Ifx_SCU_TRAPSTAT_Bits',0,5,251,7,16,4,9
	.byte	'ESR0T',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'ESR1T',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'reserved_2',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'SMUT',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,4
	.word	999
	.byte	28,0,2,35,2,0,5
	.byte	'Ifx_SCU_TRAPSTAT_Bits',0,5,130,8,3
	.word	28691
	.byte	8
	.byte	'_Ifx_SCU_WDTCPU_CON0_Bits',0,5,133,8,16,4,9
	.byte	'ENDINIT',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'LCK',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'PW',0,4
	.word	7984
	.byte	14,16,2,35,0,9
	.byte	'REL',0,4
	.word	7984
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_SCU_WDTCPU_CON0_Bits',0,5,139,8,3
	.word	28846
	.byte	8
	.byte	'_Ifx_SCU_WDTCPU_CON1_Bits',0,5,142,8,16,4,9
	.byte	'reserved_0',0,1
	.word	309
	.byte	2,6,2,35,0,9
	.byte	'IR0',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'DR',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'IR1',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'UR',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'PAR',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'TCR',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'TCTR',0,1
	.word	309
	.byte	7,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_WDTCPU_CON1_Bits',0,5,154,8,3
	.word	28976
	.byte	8
	.byte	'_Ifx_SCU_WDTCPU_SR_Bits',0,5,157,8,16,4,9
	.byte	'AE',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'OE',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'IS0',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'DS',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'TO',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'IS1',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'US',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'PAS',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'TCS',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'TCT',0,1
	.word	309
	.byte	7,0,2,35,1,9
	.byte	'TIM',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_WDTCPU_SR_Bits',0,5,170,8,3
	.word	29214
	.byte	8
	.byte	'_Ifx_SCU_WDTS_CON0_Bits',0,5,173,8,16,4,9
	.byte	'ENDINIT',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'LCK',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'PW',0,4
	.word	7984
	.byte	14,16,2,35,0,9
	.byte	'REL',0,4
	.word	7984
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_SCU_WDTS_CON0_Bits',0,5,179,8,3
	.word	29437
	.byte	8
	.byte	'_Ifx_SCU_WDTS_CON1_Bits',0,5,182,8,16,4,9
	.byte	'CLRIRF',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'reserved_1',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'IR0',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'DR',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'reserved_4',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'IR1',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'UR',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'PAR',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'TCR',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'TCTR',0,1
	.word	309
	.byte	7,0,2,35,1,9
	.byte	'reserved_16',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_WDTS_CON1_Bits',0,5,195,8,3
	.word	29563
	.byte	8
	.byte	'_Ifx_SCU_WDTS_SR_Bits',0,5,198,8,16,4,9
	.byte	'AE',0,1
	.word	309
	.byte	1,7,2,35,0,9
	.byte	'OE',0,1
	.word	309
	.byte	1,6,2,35,0,9
	.byte	'IS0',0,1
	.word	309
	.byte	1,5,2,35,0,9
	.byte	'DS',0,1
	.word	309
	.byte	1,4,2,35,0,9
	.byte	'TO',0,1
	.word	309
	.byte	1,3,2,35,0,9
	.byte	'IS1',0,1
	.word	309
	.byte	1,2,2,35,0,9
	.byte	'US',0,1
	.word	309
	.byte	1,1,2,35,0,9
	.byte	'PAS',0,1
	.word	309
	.byte	1,0,2,35,0,9
	.byte	'TCS',0,1
	.word	309
	.byte	1,7,2,35,1,9
	.byte	'TCT',0,1
	.word	309
	.byte	7,0,2,35,1,9
	.byte	'TIM',0,2
	.word	340
	.byte	16,0,2,35,2,0,5
	.byte	'Ifx_SCU_WDTS_SR_Bits',0,5,211,8,3
	.word	29815
	.byte	10,5,219,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	13110
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_ACCEN0',0,5,224,8,3
	.word	30034
	.byte	10,5,227,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	13667
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_ACCEN1',0,5,232,8,3
	.word	30098
	.byte	10,5,235,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	13744
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_ARSTDIS',0,5,240,8,3
	.word	30162
	.byte	10,5,243,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	13880
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON0',0,5,248,8,3
	.word	30227
	.byte	10,5,251,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14162
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON1',0,5,128,9,3
	.word	30292
	.byte	10,5,131,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14400
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON2',0,5,136,9,3
	.word	30357
	.byte	10,5,139,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14528
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON3',0,5,144,9,3
	.word	30422
	.byte	10,5,147,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14755
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON4',0,5,152,9,3
	.word	30487
	.byte	10,5,155,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	14974
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON5',0,5,160,9,3
	.word	30552
	.byte	10,5,163,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15102
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CCUCON6',0,5,168,9,3
	.word	30617
	.byte	10,5,171,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15202
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_CHIPID',0,5,176,9,3
	.word	30682
	.byte	10,5,179,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15410
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_DTSCON',0,5,184,9,3
	.word	30746
	.byte	10,5,187,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15575
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_DTSLIM',0,5,192,9,3
	.word	30810
	.byte	10,5,195,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15758
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_DTSSTAT',0,5,200,9,3
	.word	30874
	.byte	10,5,203,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	15912
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EICR',0,5,208,9,3
	.word	30939
	.byte	10,5,211,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16276
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EIFR',0,5,216,9,3
	.word	31001
	.byte	10,5,219,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16487
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EMSR',0,5,224,9,3
	.word	31063
	.byte	10,5,227,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16739
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_ESRCFG',0,5,232,9,3
	.word	31125
	.byte	10,5,235,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16857
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_ESROCFG',0,5,240,9,3
	.word	31189
	.byte	10,5,243,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	16968
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVR13CON',0,5,248,9,3
	.word	31254
	.byte	10,5,251,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17131
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRADCSTAT',0,5,128,10,3
	.word	31320
	.byte	10,5,131,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17293
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRMONCTRL',0,5,136,10,3
	.word	31388
	.byte	10,5,139,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17571
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVROVMON',0,5,144,10,3
	.word	31456
	.byte	10,5,147,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17750
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRRSTCON',0,5,152,10,3
	.word	31522
	.byte	10,5,155,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	17910
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRSDCOEFF2',0,5,160,10,3
	.word	31589
	.byte	10,5,163,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	18071
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRSDCTRL1',0,5,168,10,3
	.word	31658
	.byte	10,5,171,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	18263
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRSDCTRL2',0,5,176,10,3
	.word	31726
	.byte	10,5,179,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	18559
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRSDCTRL3',0,5,184,10,3
	.word	31794
	.byte	10,5,187,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	18774
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRSTAT',0,5,192,10,3
	.word	31862
	.byte	10,5,195,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19063
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EVRUVMON',0,5,200,10,3
	.word	31927
	.byte	10,5,203,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19242
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_EXTCON',0,5,208,10,3
	.word	31993
	.byte	10,5,211,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19460
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_FDR',0,5,216,10,3
	.word	32057
	.byte	10,5,219,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19623
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_FMR',0,5,224,10,3
	.word	32118
	.byte	10,5,227,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	19959
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_ID',0,5,232,10,3
	.word	32179
	.byte	10,5,235,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	20066
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_IGCR',0,5,240,10,3
	.word	32239
	.byte	10,5,243,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	20518
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_IN',0,5,248,10,3
	.word	32301
	.byte	10,5,251,10,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	20617
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_IOCR',0,5,128,11,3
	.word	32361
	.byte	10,5,131,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	20767
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_LBISTCTRL0',0,5,136,11,3
	.word	32423
	.byte	10,5,139,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	20916
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_LBISTCTRL1',0,5,144,11,3
	.word	32491
	.byte	10,5,147,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21077
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_LBISTCTRL2',0,5,152,11,3
	.word	32559
	.byte	10,5,155,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21207
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_LCLCON0',0,5,160,11,3
	.word	32627
	.byte	10,5,163,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21341
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_LCLTEST',0,5,168,11,3
	.word	32692
	.byte	10,5,171,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21456
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_MANID',0,5,176,11,3
	.word	32757
	.byte	10,5,179,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21567
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_OMR',0,5,184,11,3
	.word	32820
	.byte	10,5,187,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	21725
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_OSCCON',0,5,192,11,3
	.word	32881
	.byte	10,5,195,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22065
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_OUT',0,5,200,11,3
	.word	32945
	.byte	10,5,203,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22166
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_OVCCON',0,5,208,11,3
	.word	33006
	.byte	10,5,211,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22433
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_OVCENABLE',0,5,216,11,3
	.word	33070
	.byte	10,5,219,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22569
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PDISC',0,5,224,11,3
	.word	33137
	.byte	10,5,227,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22680
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PDR',0,5,232,11,3
	.word	33200
	.byte	10,5,235,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	22813
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PDRR',0,5,240,11,3
	.word	33261
	.byte	10,5,243,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23016
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLCON0',0,5,248,11,3
	.word	33323
	.byte	10,5,251,11,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23372
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLCON1',0,5,128,12,3
	.word	33388
	.byte	10,5,131,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23550
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLCON2',0,5,136,12,3
	.word	33453
	.byte	10,5,139,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	23650
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLERAYCON0',0,5,144,12,3
	.word	33518
	.byte	10,5,147,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24020
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLERAYCON1',0,5,152,12,3
	.word	33587
	.byte	10,5,155,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24206
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLERAYSTAT',0,5,160,12,3
	.word	33656
	.byte	10,5,163,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24404
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PLLSTAT',0,5,168,12,3
	.word	33725
	.byte	10,5,171,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24637
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMCSR',0,5,176,12,3
	.word	33790
	.byte	10,5,179,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	24789
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMSWCR0',0,5,184,12,3
	.word	33853
	.byte	10,5,187,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25348
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMSWCR1',0,5,192,12,3
	.word	33918
	.byte	10,5,195,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25531
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMSWCR3',0,5,200,12,3
	.word	33983
	.byte	10,5,203,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	25700
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMSWSTAT',0,5,208,12,3
	.word	34048
	.byte	10,5,211,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	26267
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMSWSTATCLR',0,5,216,12,3
	.word	34114
	.byte	10,5,219,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	26583
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_PMSWUTCNT',0,5,224,12,3
	.word	34183
	.byte	10,5,227,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	26911
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_RSTCON',0,5,232,12,3
	.word	34250
	.byte	10,5,235,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	26702
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_RSTCON2',0,5,240,12,3
	.word	34314
	.byte	10,5,243,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	27122
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_RSTSTAT',0,5,248,12,3
	.word	34379
	.byte	10,5,251,12,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	27554
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_SAFECON',0,5,128,13,3
	.word	34444
	.byte	10,5,131,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	27650
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_STSTAT',0,5,136,13,3
	.word	34509
	.byte	10,5,139,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	27910
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_SWRSTCON',0,5,144,13,3
	.word	34573
	.byte	10,5,147,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28035
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_SYSCON',0,5,152,13,3
	.word	34639
	.byte	10,5,155,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28232
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_TRAPCLR',0,5,160,13,3
	.word	34703
	.byte	10,5,163,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28385
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_TRAPDIS',0,5,168,13,3
	.word	34768
	.byte	10,5,171,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28538
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_TRAPSET',0,5,176,13,3
	.word	34833
	.byte	10,5,179,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28691
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_TRAPSTAT',0,5,184,13,3
	.word	34898
	.byte	10,5,187,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28846
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_WDTCPU_CON0',0,5,192,13,3
	.word	34964
	.byte	10,5,195,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	28976
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_WDTCPU_CON1',0,5,200,13,3
	.word	35033
	.byte	10,5,203,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29214
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_WDTCPU_SR',0,5,208,13,3
	.word	35102
	.byte	10,5,211,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29437
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_WDTS_CON0',0,5,216,13,3
	.word	35169
	.byte	10,5,219,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29563
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_WDTS_CON1',0,5,224,13,3
	.word	35236
	.byte	10,5,227,13,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	29815
	.byte	2,35,0,0,5
	.byte	'Ifx_SCU_WDTS_SR',0,5,232,13,3
	.word	35303
	.byte	8
	.byte	'_Ifx_SCU_WDTCPU',0,5,243,13,25,12,11
	.byte	'CON0',0,4
	.word	34964
	.byte	2,35,0,11
	.byte	'CON1',0,4
	.word	35033
	.byte	2,35,4,11
	.byte	'SR',0,4
	.word	35102
	.byte	2,35,8,0,14
	.word	35368
	.byte	5
	.byte	'Ifx_SCU_WDTCPU',0,5,248,13,3
	.word	35431
	.byte	8
	.byte	'_Ifx_SCU_WDTS',0,5,251,13,25,12,11
	.byte	'CON0',0,4
	.word	35169
	.byte	2,35,0,11
	.byte	'CON1',0,4
	.word	35236
	.byte	2,35,4,11
	.byte	'SR',0,4
	.word	35303
	.byte	2,35,8,0,14
	.word	35460
	.byte	5
	.byte	'Ifx_SCU_WDTS',0,5,128,14,3
	.word	35521
	.byte	8
	.byte	'_Ifx_CPU_A_Bits',0,6,45,16,4,9
	.byte	'ADDR',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_A_Bits',0,6,48,3
	.word	35548
	.byte	8
	.byte	'_Ifx_CPU_BIV_Bits',0,6,51,16,4,9
	.byte	'VSS',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'BIV',0,4
	.word	7984
	.byte	31,0,2,35,0,0,5
	.byte	'Ifx_CPU_BIV_Bits',0,6,55,3
	.word	35609
	.byte	8
	.byte	'_Ifx_CPU_BTV_Bits',0,6,58,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'BTV',0,4
	.word	7984
	.byte	31,0,2,35,0,0,5
	.byte	'Ifx_CPU_BTV_Bits',0,6,62,3
	.word	35688
	.byte	8
	.byte	'_Ifx_CPU_CCNT_Bits',0,6,65,16,4,9
	.byte	'CountValue',0,4
	.word	7984
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	7984
	.byte	1,0,2,35,0,0,5
	.byte	'Ifx_CPU_CCNT_Bits',0,6,69,3
	.word	35774
	.byte	8
	.byte	'_Ifx_CPU_CCTRL_Bits',0,6,72,16,4,9
	.byte	'CM',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'CE',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'M1',0,4
	.word	7984
	.byte	3,27,2,35,0,9
	.byte	'M2',0,4
	.word	7984
	.byte	3,24,2,35,0,9
	.byte	'M3',0,4
	.word	7984
	.byte	3,21,2,35,0,9
	.byte	'reserved_11',0,4
	.word	7984
	.byte	21,0,2,35,0,0,5
	.byte	'Ifx_CPU_CCTRL_Bits',0,6,80,3
	.word	35863
	.byte	8
	.byte	'_Ifx_CPU_COMPAT_Bits',0,6,83,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	3,29,2,35,0,9
	.byte	'RM',0,4
	.word	7984
	.byte	1,28,2,35,0,9
	.byte	'SP',0,4
	.word	7984
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	7984
	.byte	27,0,2,35,0,0,5
	.byte	'Ifx_CPU_COMPAT_Bits',0,6,89,3
	.word	36009
	.byte	8
	.byte	'_Ifx_CPU_CORE_ID_Bits',0,6,92,16,4,9
	.byte	'CORE_ID',0,4
	.word	7984
	.byte	3,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	7984
	.byte	29,0,2,35,0,0,5
	.byte	'Ifx_CPU_CORE_ID_Bits',0,6,96,3
	.word	36136
	.byte	8
	.byte	'_Ifx_CPU_CPR_L_Bits',0,6,99,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	3,29,2,35,0,9
	.byte	'LOWBND',0,4
	.word	7984
	.byte	29,0,2,35,0,0,5
	.byte	'Ifx_CPU_CPR_L_Bits',0,6,103,3
	.word	36234
	.byte	8
	.byte	'_Ifx_CPU_CPR_U_Bits',0,6,106,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	3,29,2,35,0,9
	.byte	'UPPBND',0,4
	.word	7984
	.byte	29,0,2,35,0,0,5
	.byte	'Ifx_CPU_CPR_U_Bits',0,6,110,3
	.word	36327
	.byte	8
	.byte	'_Ifx_CPU_CPU_ID_Bits',0,6,113,16,4,9
	.byte	'MODREV',0,4
	.word	7984
	.byte	8,24,2,35,0,9
	.byte	'MOD_32B',0,4
	.word	7984
	.byte	8,16,2,35,0,9
	.byte	'MOD',0,4
	.word	7984
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_CPU_CPU_ID_Bits',0,6,118,3
	.word	36420
	.byte	8
	.byte	'_Ifx_CPU_CPXE_Bits',0,6,121,16,4,9
	.byte	'XE',0,4
	.word	7984
	.byte	8,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	7984
	.byte	24,0,2,35,0,0,5
	.byte	'Ifx_CPU_CPXE_Bits',0,6,125,3
	.word	36527
	.byte	8
	.byte	'_Ifx_CPU_CREVT_Bits',0,6,128,1,16,4,9
	.byte	'EVTA',0,4
	.word	7984
	.byte	3,29,2,35,0,9
	.byte	'BBM',0,4
	.word	7984
	.byte	1,28,2,35,0,9
	.byte	'BOD',0,4
	.word	7984
	.byte	1,27,2,35,0,9
	.byte	'SUSP',0,4
	.word	7984
	.byte	1,26,2,35,0,9
	.byte	'CNT',0,4
	.word	7984
	.byte	2,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	7984
	.byte	24,0,2,35,0,0,5
	.byte	'Ifx_CPU_CREVT_Bits',0,6,136,1,3
	.word	36614
	.byte	8
	.byte	'_Ifx_CPU_CUS_ID_Bits',0,6,139,1,16,4,9
	.byte	'CID',0,4
	.word	7984
	.byte	3,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	7984
	.byte	29,0,2,35,0,0,5
	.byte	'Ifx_CPU_CUS_ID_Bits',0,6,143,1,3
	.word	36768
	.byte	8
	.byte	'_Ifx_CPU_D_Bits',0,6,146,1,16,4,9
	.byte	'DATA',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_D_Bits',0,6,149,1,3
	.word	36862
	.byte	8
	.byte	'_Ifx_CPU_DATR_Bits',0,6,152,1,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	3,29,2,35,0,9
	.byte	'SBE',0,4
	.word	7984
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	7984
	.byte	5,23,2,35,0,9
	.byte	'CWE',0,4
	.word	7984
	.byte	1,22,2,35,0,9
	.byte	'CFE',0,4
	.word	7984
	.byte	1,21,2,35,0,9
	.byte	'reserved_11',0,4
	.word	7984
	.byte	3,18,2,35,0,9
	.byte	'SOE',0,4
	.word	7984
	.byte	1,17,2,35,0,9
	.byte	'SME',0,4
	.word	7984
	.byte	1,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	7984
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_CPU_DATR_Bits',0,6,163,1,3
	.word	36925
	.byte	8
	.byte	'_Ifx_CPU_DBGSR_Bits',0,6,166,1,16,4,9
	.byte	'DE',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'HALT',0,4
	.word	7984
	.byte	2,29,2,35,0,9
	.byte	'SIH',0,4
	.word	7984
	.byte	1,28,2,35,0,9
	.byte	'SUSP',0,4
	.word	7984
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	7984
	.byte	1,26,2,35,0,9
	.byte	'PREVSUSP',0,4
	.word	7984
	.byte	1,25,2,35,0,9
	.byte	'PEVT',0,4
	.word	7984
	.byte	1,24,2,35,0,9
	.byte	'EVTSRC',0,4
	.word	7984
	.byte	5,19,2,35,0,9
	.byte	'reserved_13',0,4
	.word	7984
	.byte	19,0,2,35,0,0,5
	.byte	'Ifx_CPU_DBGSR_Bits',0,6,177,1,3
	.word	37143
	.byte	8
	.byte	'_Ifx_CPU_DBGTCR_Bits',0,6,180,1,16,4,9
	.byte	'DTA',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	7984
	.byte	31,0,2,35,0,0,5
	.byte	'Ifx_CPU_DBGTCR_Bits',0,6,184,1,3
	.word	37358
	.byte	8
	.byte	'_Ifx_CPU_DCON0_Bits',0,6,187,1,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'DCBYP',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	7984
	.byte	30,0,2,35,0,0,5
	.byte	'Ifx_CPU_DCON0_Bits',0,6,192,1,3
	.word	37452
	.byte	8
	.byte	'_Ifx_CPU_DCON2_Bits',0,6,195,1,16,4,9
	.byte	'DCACHE_SZE',0,4
	.word	7984
	.byte	16,16,2,35,0,9
	.byte	'DSCRATCH_SZE',0,4
	.word	7984
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_CPU_DCON2_Bits',0,6,199,1,3
	.word	37568
	.byte	8
	.byte	'_Ifx_CPU_DCX_Bits',0,6,202,1,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	6,26,2,35,0,9
	.byte	'DCXValue',0,4
	.word	7984
	.byte	26,0,2,35,0,0,5
	.byte	'Ifx_CPU_DCX_Bits',0,6,206,1,3
	.word	37669
	.byte	8
	.byte	'_Ifx_CPU_DEADD_Bits',0,6,209,1,16,4,9
	.byte	'ERROR_ADDRESS',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_DEADD_Bits',0,6,212,1,3
	.word	37762
	.byte	8
	.byte	'_Ifx_CPU_DIEAR_Bits',0,6,215,1,16,4,9
	.byte	'TA',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_DIEAR_Bits',0,6,218,1,3
	.word	37842
	.byte	8
	.byte	'_Ifx_CPU_DIETR_Bits',0,6,221,1,16,4,9
	.byte	'IED',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'IE_T',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'IE_C',0,4
	.word	7984
	.byte	1,29,2,35,0,9
	.byte	'IE_S',0,4
	.word	7984
	.byte	1,28,2,35,0,9
	.byte	'IE_BI',0,4
	.word	7984
	.byte	1,27,2,35,0,9
	.byte	'E_INFO',0,4
	.word	7984
	.byte	6,21,2,35,0,9
	.byte	'IE_DUAL',0,4
	.word	7984
	.byte	1,20,2,35,0,9
	.byte	'IE_SP',0,4
	.word	7984
	.byte	1,19,2,35,0,9
	.byte	'IE_BS',0,4
	.word	7984
	.byte	1,18,2,35,0,9
	.byte	'reserved_14',0,4
	.word	7984
	.byte	18,0,2,35,0,0,5
	.byte	'Ifx_CPU_DIETR_Bits',0,6,233,1,3
	.word	37911
	.byte	8
	.byte	'_Ifx_CPU_DMS_Bits',0,6,236,1,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'DMSValue',0,4
	.word	7984
	.byte	31,0,2,35,0,0,5
	.byte	'Ifx_CPU_DMS_Bits',0,6,240,1,3
	.word	38140
	.byte	8
	.byte	'_Ifx_CPU_DPR_L_Bits',0,6,243,1,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	3,29,2,35,0,9
	.byte	'LOWBND',0,4
	.word	7984
	.byte	29,0,2,35,0,0,5
	.byte	'Ifx_CPU_DPR_L_Bits',0,6,247,1,3
	.word	38233
	.byte	8
	.byte	'_Ifx_CPU_DPR_U_Bits',0,6,250,1,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	3,29,2,35,0,9
	.byte	'UPPBND',0,4
	.word	7984
	.byte	29,0,2,35,0,0,5
	.byte	'Ifx_CPU_DPR_U_Bits',0,6,254,1,3
	.word	38328
	.byte	8
	.byte	'_Ifx_CPU_DPRE_Bits',0,6,129,2,16,4,9
	.byte	'RE',0,4
	.word	7984
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	7984
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_CPU_DPRE_Bits',0,6,133,2,3
	.word	38423
	.byte	8
	.byte	'_Ifx_CPU_DPWE_Bits',0,6,136,2,16,4,9
	.byte	'WE',0,4
	.word	7984
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	7984
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_CPU_DPWE_Bits',0,6,140,2,3
	.word	38513
	.byte	8
	.byte	'_Ifx_CPU_DSTR_Bits',0,6,143,2,16,4,9
	.byte	'SRE',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'GAE',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'LBE',0,4
	.word	7984
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	7984
	.byte	3,26,2,35,0,9
	.byte	'CRE',0,4
	.word	7984
	.byte	1,25,2,35,0,9
	.byte	'reserved_7',0,4
	.word	7984
	.byte	7,18,2,35,0,9
	.byte	'DTME',0,4
	.word	7984
	.byte	1,17,2,35,0,9
	.byte	'LOE',0,4
	.word	7984
	.byte	1,16,2,35,0,9
	.byte	'SDE',0,4
	.word	7984
	.byte	1,15,2,35,0,9
	.byte	'SCE',0,4
	.word	7984
	.byte	1,14,2,35,0,9
	.byte	'CAC',0,4
	.word	7984
	.byte	1,13,2,35,0,9
	.byte	'MPE',0,4
	.word	7984
	.byte	1,12,2,35,0,9
	.byte	'CLE',0,4
	.word	7984
	.byte	1,11,2,35,0,9
	.byte	'reserved_21',0,4
	.word	7984
	.byte	3,8,2,35,0,9
	.byte	'ALN',0,4
	.word	7984
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	7984
	.byte	7,0,2,35,0,0,5
	.byte	'Ifx_CPU_DSTR_Bits',0,6,161,2,3
	.word	38603
	.byte	8
	.byte	'_Ifx_CPU_EXEVT_Bits',0,6,164,2,16,4,9
	.byte	'EVTA',0,4
	.word	7984
	.byte	3,29,2,35,0,9
	.byte	'BBM',0,4
	.word	7984
	.byte	1,28,2,35,0,9
	.byte	'BOD',0,4
	.word	7984
	.byte	1,27,2,35,0,9
	.byte	'SUSP',0,4
	.word	7984
	.byte	1,26,2,35,0,9
	.byte	'CNT',0,4
	.word	7984
	.byte	2,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	7984
	.byte	24,0,2,35,0,0,5
	.byte	'Ifx_CPU_EXEVT_Bits',0,6,172,2,3
	.word	38927
	.byte	8
	.byte	'_Ifx_CPU_FCX_Bits',0,6,175,2,16,4,9
	.byte	'FCXO',0,4
	.word	7984
	.byte	16,16,2,35,0,9
	.byte	'FCXS',0,4
	.word	7984
	.byte	4,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	7984
	.byte	12,0,2,35,0,0,5
	.byte	'Ifx_CPU_FCX_Bits',0,6,180,2,3
	.word	39081
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_CON_Bits',0,6,183,2,16,4,9
	.byte	'TST',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'TCL',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	7984
	.byte	6,24,2,35,0,9
	.byte	'RM',0,4
	.word	7984
	.byte	2,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	7984
	.byte	8,14,2,35,0,9
	.byte	'FXE',0,4
	.word	7984
	.byte	1,13,2,35,0,9
	.byte	'FUE',0,4
	.word	7984
	.byte	1,12,2,35,0,9
	.byte	'FZE',0,4
	.word	7984
	.byte	1,11,2,35,0,9
	.byte	'FVE',0,4
	.word	7984
	.byte	1,10,2,35,0,9
	.byte	'FIE',0,4
	.word	7984
	.byte	1,9,2,35,0,9
	.byte	'reserved_23',0,4
	.word	7984
	.byte	3,6,2,35,0,9
	.byte	'FX',0,4
	.word	7984
	.byte	1,5,2,35,0,9
	.byte	'FU',0,4
	.word	7984
	.byte	1,4,2,35,0,9
	.byte	'FZ',0,4
	.word	7984
	.byte	1,3,2,35,0,9
	.byte	'FV',0,4
	.word	7984
	.byte	1,2,2,35,0,9
	.byte	'FI',0,4
	.word	7984
	.byte	1,1,2,35,0,9
	.byte	'reserved_31',0,4
	.word	7984
	.byte	1,0,2,35,0,0,5
	.byte	'Ifx_CPU_FPU_TRAP_CON_Bits',0,6,202,2,3
	.word	39187
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_OPC_Bits',0,6,205,2,16,4,9
	.byte	'OPC',0,4
	.word	7984
	.byte	8,24,2,35,0,9
	.byte	'FMT',0,4
	.word	7984
	.byte	1,23,2,35,0,9
	.byte	'reserved_9',0,4
	.word	7984
	.byte	7,16,2,35,0,9
	.byte	'DREG',0,4
	.word	7984
	.byte	4,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	7984
	.byte	12,0,2,35,0,0,5
	.byte	'Ifx_CPU_FPU_TRAP_OPC_Bits',0,6,212,2,3
	.word	39536
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_PC_Bits',0,6,215,2,16,4,9
	.byte	'PC',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_FPU_TRAP_PC_Bits',0,6,218,2,3
	.word	39696
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_SRC1_Bits',0,6,221,2,16,4,9
	.byte	'SRC1',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_FPU_TRAP_SRC1_Bits',0,6,224,2,3
	.word	39777
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_SRC2_Bits',0,6,227,2,16,4,9
	.byte	'SRC2',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_FPU_TRAP_SRC2_Bits',0,6,230,2,3
	.word	39864
	.byte	8
	.byte	'_Ifx_CPU_FPU_TRAP_SRC3_Bits',0,6,233,2,16,4,9
	.byte	'SRC3',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_FPU_TRAP_SRC3_Bits',0,6,236,2,3
	.word	39951
	.byte	8
	.byte	'_Ifx_CPU_ICNT_Bits',0,6,239,2,16,4,9
	.byte	'CountValue',0,4
	.word	7984
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	7984
	.byte	1,0,2,35,0,0,5
	.byte	'Ifx_CPU_ICNT_Bits',0,6,243,2,3
	.word	40038
	.byte	8
	.byte	'_Ifx_CPU_ICR_Bits',0,6,246,2,16,4,9
	.byte	'CCPN',0,4
	.word	7984
	.byte	10,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	7984
	.byte	5,17,2,35,0,9
	.byte	'IE',0,4
	.word	7984
	.byte	1,16,2,35,0,9
	.byte	'PIPN',0,4
	.word	7984
	.byte	10,6,2,35,0,9
	.byte	'reserved_26',0,4
	.word	7984
	.byte	6,0,2,35,0,0,5
	.byte	'Ifx_CPU_ICR_Bits',0,6,253,2,3
	.word	40129
	.byte	8
	.byte	'_Ifx_CPU_ISP_Bits',0,6,128,3,16,4,9
	.byte	'ISP',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_ISP_Bits',0,6,131,3,3
	.word	40272
	.byte	8
	.byte	'_Ifx_CPU_LCX_Bits',0,6,134,3,16,4,9
	.byte	'LCXO',0,4
	.word	7984
	.byte	16,16,2,35,0,9
	.byte	'LCXS',0,4
	.word	7984
	.byte	4,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	7984
	.byte	12,0,2,35,0,0,5
	.byte	'Ifx_CPU_LCX_Bits',0,6,139,3,3
	.word	40338
	.byte	8
	.byte	'_Ifx_CPU_M1CNT_Bits',0,6,142,3,16,4,9
	.byte	'CountValue',0,4
	.word	7984
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	7984
	.byte	1,0,2,35,0,0,5
	.byte	'Ifx_CPU_M1CNT_Bits',0,6,146,3,3
	.word	40444
	.byte	8
	.byte	'_Ifx_CPU_M2CNT_Bits',0,6,149,3,16,4,9
	.byte	'CountValue',0,4
	.word	7984
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	7984
	.byte	1,0,2,35,0,0,5
	.byte	'Ifx_CPU_M2CNT_Bits',0,6,153,3,3
	.word	40537
	.byte	8
	.byte	'_Ifx_CPU_M3CNT_Bits',0,6,156,3,16,4,9
	.byte	'CountValue',0,4
	.word	7984
	.byte	31,1,2,35,0,9
	.byte	'SOvf',0,4
	.word	7984
	.byte	1,0,2,35,0,0,5
	.byte	'Ifx_CPU_M3CNT_Bits',0,6,160,3,3
	.word	40630
	.byte	8
	.byte	'_Ifx_CPU_PC_Bits',0,6,163,3,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'PC',0,4
	.word	7984
	.byte	31,0,2,35,0,0,5
	.byte	'Ifx_CPU_PC_Bits',0,6,167,3,3
	.word	40723
	.byte	8
	.byte	'_Ifx_CPU_PCON0_Bits',0,6,170,3,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'PCBYP',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	7984
	.byte	30,0,2,35,0,0,5
	.byte	'Ifx_CPU_PCON0_Bits',0,6,175,3,3
	.word	40808
	.byte	8
	.byte	'_Ifx_CPU_PCON1_Bits',0,6,178,3,16,4,9
	.byte	'PCINV',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'PBINV',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	7984
	.byte	30,0,2,35,0,0,5
	.byte	'Ifx_CPU_PCON1_Bits',0,6,183,3,3
	.word	40924
	.byte	8
	.byte	'_Ifx_CPU_PCON2_Bits',0,6,186,3,16,4,9
	.byte	'PCACHE_SZE',0,4
	.word	7984
	.byte	16,16,2,35,0,9
	.byte	'PSCRATCH_SZE',0,4
	.word	7984
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_CPU_PCON2_Bits',0,6,190,3,3
	.word	41035
	.byte	8
	.byte	'_Ifx_CPU_PCXI_Bits',0,6,193,3,16,4,9
	.byte	'PCXO',0,4
	.word	7984
	.byte	16,16,2,35,0,9
	.byte	'PCXS',0,4
	.word	7984
	.byte	4,12,2,35,0,9
	.byte	'UL',0,4
	.word	7984
	.byte	1,11,2,35,0,9
	.byte	'PIE',0,4
	.word	7984
	.byte	1,10,2,35,0,9
	.byte	'PCPN',0,4
	.word	7984
	.byte	10,0,2,35,0,0,5
	.byte	'Ifx_CPU_PCXI_Bits',0,6,200,3,3
	.word	41136
	.byte	8
	.byte	'_Ifx_CPU_PIEAR_Bits',0,6,203,3,16,4,9
	.byte	'TA',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_PIEAR_Bits',0,6,206,3,3
	.word	41266
	.byte	8
	.byte	'_Ifx_CPU_PIETR_Bits',0,6,209,3,16,4,9
	.byte	'IED',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'IE_T',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'IE_C',0,4
	.word	7984
	.byte	1,29,2,35,0,9
	.byte	'IE_S',0,4
	.word	7984
	.byte	1,28,2,35,0,9
	.byte	'IE_BI',0,4
	.word	7984
	.byte	1,27,2,35,0,9
	.byte	'E_INFO',0,4
	.word	7984
	.byte	6,21,2,35,0,9
	.byte	'IE_DUAL',0,4
	.word	7984
	.byte	1,20,2,35,0,9
	.byte	'IE_SP',0,4
	.word	7984
	.byte	1,19,2,35,0,9
	.byte	'IE_BS',0,4
	.word	7984
	.byte	1,18,2,35,0,9
	.byte	'reserved_14',0,4
	.word	7984
	.byte	18,0,2,35,0,0,5
	.byte	'Ifx_CPU_PIETR_Bits',0,6,221,3,3
	.word	41335
	.byte	8
	.byte	'_Ifx_CPU_PMA0_Bits',0,6,224,3,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	13,19,2,35,0,9
	.byte	'DAC',0,4
	.word	7984
	.byte	3,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	7984
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_CPU_PMA0_Bits',0,6,229,3,3
	.word	41564
	.byte	8
	.byte	'_Ifx_CPU_PMA1_Bits',0,6,232,3,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	14,18,2,35,0,9
	.byte	'CAC',0,4
	.word	7984
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	7984
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_CPU_PMA1_Bits',0,6,237,3,3
	.word	41677
	.byte	8
	.byte	'_Ifx_CPU_PMA2_Bits',0,6,240,3,16,4,9
	.byte	'PSI',0,4
	.word	7984
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	7984
	.byte	16,0,2,35,0,0,5
	.byte	'Ifx_CPU_PMA2_Bits',0,6,244,3,3
	.word	41790
	.byte	8
	.byte	'_Ifx_CPU_PSTR_Bits',0,6,247,3,16,4,9
	.byte	'FRE',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'FBE',0,4
	.word	7984
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	7984
	.byte	9,20,2,35,0,9
	.byte	'FPE',0,4
	.word	7984
	.byte	1,19,2,35,0,9
	.byte	'reserved_13',0,4
	.word	7984
	.byte	1,18,2,35,0,9
	.byte	'FME',0,4
	.word	7984
	.byte	1,17,2,35,0,9
	.byte	'reserved_15',0,4
	.word	7984
	.byte	17,0,2,35,0,0,5
	.byte	'Ifx_CPU_PSTR_Bits',0,6,129,4,3
	.word	41881
	.byte	8
	.byte	'_Ifx_CPU_PSW_Bits',0,6,132,4,16,4,9
	.byte	'CDC',0,4
	.word	7984
	.byte	7,25,2,35,0,9
	.byte	'CDE',0,4
	.word	7984
	.byte	1,24,2,35,0,9
	.byte	'GW',0,4
	.word	7984
	.byte	1,23,2,35,0,9
	.byte	'IS',0,4
	.word	7984
	.byte	1,22,2,35,0,9
	.byte	'IO',0,4
	.word	7984
	.byte	2,20,2,35,0,9
	.byte	'PRS',0,4
	.word	7984
	.byte	2,18,2,35,0,9
	.byte	'S',0,4
	.word	7984
	.byte	1,17,2,35,0,9
	.byte	'reserved_15',0,4
	.word	7984
	.byte	12,5,2,35,0,9
	.byte	'SAV',0,4
	.word	7984
	.byte	1,4,2,35,0,9
	.byte	'AV',0,4
	.word	7984
	.byte	1,3,2,35,0,9
	.byte	'SV',0,4
	.word	7984
	.byte	1,2,2,35,0,9
	.byte	'V',0,4
	.word	7984
	.byte	1,1,2,35,0,9
	.byte	'C',0,4
	.word	7984
	.byte	1,0,2,35,0,0,5
	.byte	'Ifx_CPU_PSW_Bits',0,6,147,4,3
	.word	42084
	.byte	8
	.byte	'_Ifx_CPU_SEGEN_Bits',0,6,150,4,16,4,9
	.byte	'ADFLIP',0,4
	.word	7984
	.byte	8,24,2,35,0,9
	.byte	'ADTYPE',0,4
	.word	7984
	.byte	2,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	7984
	.byte	21,1,2,35,0,9
	.byte	'AE',0,4
	.word	7984
	.byte	1,0,2,35,0,0,5
	.byte	'Ifx_CPU_SEGEN_Bits',0,6,156,4,3
	.word	42327
	.byte	8
	.byte	'_Ifx_CPU_SMACON_Bits',0,6,159,4,16,4,9
	.byte	'PC',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'PT',0,4
	.word	7984
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	7984
	.byte	5,24,2,35,0,9
	.byte	'DC',0,4
	.word	7984
	.byte	1,23,2,35,0,9
	.byte	'reserved_9',0,4
	.word	7984
	.byte	1,22,2,35,0,9
	.byte	'DT',0,4
	.word	7984
	.byte	1,21,2,35,0,9
	.byte	'reserved_11',0,4
	.word	7984
	.byte	13,8,2,35,0,9
	.byte	'IODT',0,4
	.word	7984
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	7984
	.byte	7,0,2,35,0,0,5
	.byte	'Ifx_CPU_SMACON_Bits',0,6,171,4,3
	.word	42455
	.byte	8
	.byte	'_Ifx_CPU_SPROT_ACCENA_Bits',0,6,174,4,16,4,9
	.byte	'EN',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_SPROT_ACCENA_Bits',0,6,177,4,3
	.word	42696
	.byte	8
	.byte	'_Ifx_CPU_SPROT_ACCENB_Bits',0,6,180,4,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_SPROT_ACCENB_Bits',0,6,183,4,3
	.word	42779
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,6,186,4,16,4,9
	.byte	'EN',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA_Bits',0,6,189,4,3
	.word	42870
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,6,192,4,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB_Bits',0,6,195,4,3
	.word	42961
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN_LA_Bits',0,6,198,4,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	5,27,2,35,0,9
	.byte	'ADDR',0,4
	.word	7984
	.byte	27,0,2,35,0,0,5
	.byte	'Ifx_CPU_SPROT_RGN_LA_Bits',0,6,202,4,3
	.word	43060
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN_UA_Bits',0,6,205,4,16,4,9
	.byte	'reserved_0',0,4
	.word	7984
	.byte	5,27,2,35,0,9
	.byte	'ADDR',0,4
	.word	7984
	.byte	27,0,2,35,0,0,5
	.byte	'Ifx_CPU_SPROT_RGN_UA_Bits',0,6,209,4,3
	.word	43167
	.byte	8
	.byte	'_Ifx_CPU_SWEVT_Bits',0,6,212,4,16,4,9
	.byte	'EVTA',0,4
	.word	7984
	.byte	3,29,2,35,0,9
	.byte	'BBM',0,4
	.word	7984
	.byte	1,28,2,35,0,9
	.byte	'BOD',0,4
	.word	7984
	.byte	1,27,2,35,0,9
	.byte	'SUSP',0,4
	.word	7984
	.byte	1,26,2,35,0,9
	.byte	'CNT',0,4
	.word	7984
	.byte	2,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	7984
	.byte	24,0,2,35,0,0,5
	.byte	'Ifx_CPU_SWEVT_Bits',0,6,220,4,3
	.word	43274
	.byte	8
	.byte	'_Ifx_CPU_SYSCON_Bits',0,6,223,4,16,4,9
	.byte	'FCDSF',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'PROTEN',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'TPROTEN',0,4
	.word	7984
	.byte	1,29,2,35,0,9
	.byte	'IS',0,4
	.word	7984
	.byte	1,28,2,35,0,9
	.byte	'IT',0,4
	.word	7984
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	7984
	.byte	27,0,2,35,0,0,5
	.byte	'Ifx_CPU_SYSCON_Bits',0,6,231,4,3
	.word	43428
	.byte	8
	.byte	'_Ifx_CPU_TASK_ASI_Bits',0,6,234,4,16,4,9
	.byte	'ASI',0,4
	.word	7984
	.byte	5,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	7984
	.byte	27,0,2,35,0,0,5
	.byte	'Ifx_CPU_TASK_ASI_Bits',0,6,238,4,3
	.word	43589
	.byte	8
	.byte	'_Ifx_CPU_TPS_CON_Bits',0,6,241,4,16,4,9
	.byte	'TEXP0',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'TEXP1',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'TEXP2',0,4
	.word	7984
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	7984
	.byte	13,16,2,35,0,9
	.byte	'TTRAP',0,4
	.word	7984
	.byte	1,15,2,35,0,9
	.byte	'reserved_17',0,4
	.word	7984
	.byte	15,0,2,35,0,0,5
	.byte	'Ifx_CPU_TPS_CON_Bits',0,6,249,4,3
	.word	43687
	.byte	8
	.byte	'_Ifx_CPU_TPS_TIMER_Bits',0,6,252,4,16,4,9
	.byte	'Timer',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_TPS_TIMER_Bits',0,6,255,4,3
	.word	43859
	.byte	8
	.byte	'_Ifx_CPU_TR_ADR_Bits',0,6,130,5,16,4,9
	.byte	'ADDR',0,4
	.word	7984
	.byte	32,0,2,35,0,0,5
	.byte	'Ifx_CPU_TR_ADR_Bits',0,6,133,5,3
	.word	43939
	.byte	8
	.byte	'_Ifx_CPU_TR_EVT_Bits',0,6,136,5,16,4,9
	.byte	'EVTA',0,4
	.word	7984
	.byte	3,29,2,35,0,9
	.byte	'BBM',0,4
	.word	7984
	.byte	1,28,2,35,0,9
	.byte	'BOD',0,4
	.word	7984
	.byte	1,27,2,35,0,9
	.byte	'SUSP',0,4
	.word	7984
	.byte	1,26,2,35,0,9
	.byte	'CNT',0,4
	.word	7984
	.byte	2,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	7984
	.byte	4,20,2,35,0,9
	.byte	'TYP',0,4
	.word	7984
	.byte	1,19,2,35,0,9
	.byte	'RNG',0,4
	.word	7984
	.byte	1,18,2,35,0,9
	.byte	'reserved_14',0,4
	.word	7984
	.byte	1,17,2,35,0,9
	.byte	'ASI_EN',0,4
	.word	7984
	.byte	1,16,2,35,0,9
	.byte	'ASI',0,4
	.word	7984
	.byte	5,11,2,35,0,9
	.byte	'reserved_21',0,4
	.word	7984
	.byte	6,5,2,35,0,9
	.byte	'AST',0,4
	.word	7984
	.byte	1,4,2,35,0,9
	.byte	'ALD',0,4
	.word	7984
	.byte	1,3,2,35,0,9
	.byte	'reserved_29',0,4
	.word	7984
	.byte	3,0,2,35,0,0,5
	.byte	'Ifx_CPU_TR_EVT_Bits',0,6,153,5,3
	.word	44012
	.byte	8
	.byte	'_Ifx_CPU_TRIG_ACC_Bits',0,6,156,5,16,4,9
	.byte	'T0',0,4
	.word	7984
	.byte	1,31,2,35,0,9
	.byte	'T1',0,4
	.word	7984
	.byte	1,30,2,35,0,9
	.byte	'T2',0,4
	.word	7984
	.byte	1,29,2,35,0,9
	.byte	'T3',0,4
	.word	7984
	.byte	1,28,2,35,0,9
	.byte	'T4',0,4
	.word	7984
	.byte	1,27,2,35,0,9
	.byte	'T5',0,4
	.word	7984
	.byte	1,26,2,35,0,9
	.byte	'T6',0,4
	.word	7984
	.byte	1,25,2,35,0,9
	.byte	'T7',0,4
	.word	7984
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	7984
	.byte	24,0,2,35,0,0,5
	.byte	'Ifx_CPU_TRIG_ACC_Bits',0,6,167,5,3
	.word	44330
	.byte	10,6,175,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35548
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_A',0,6,180,5,3
	.word	44525
	.byte	10,6,183,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35609
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_BIV',0,6,188,5,3
	.word	44584
	.byte	10,6,191,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35688
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_BTV',0,6,196,5,3
	.word	44645
	.byte	10,6,199,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35774
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_CCNT',0,6,204,5,3
	.word	44706
	.byte	10,6,207,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	35863
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_CCTRL',0,6,212,5,3
	.word	44768
	.byte	10,6,215,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36009
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_COMPAT',0,6,220,5,3
	.word	44831
	.byte	10,6,223,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36136
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_CORE_ID',0,6,228,5,3
	.word	44895
	.byte	10,6,231,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36234
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_CPR_L',0,6,236,5,3
	.word	44960
	.byte	10,6,239,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36327
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_CPR_U',0,6,244,5,3
	.word	45023
	.byte	10,6,247,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36420
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_CPU_ID',0,6,252,5,3
	.word	45086
	.byte	10,6,255,5,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36527
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_CPXE',0,6,132,6,3
	.word	45150
	.byte	10,6,135,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36614
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_CREVT',0,6,140,6,3
	.word	45212
	.byte	10,6,143,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36768
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_CUS_ID',0,6,148,6,3
	.word	45275
	.byte	10,6,151,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36862
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_D',0,6,156,6,3
	.word	45339
	.byte	10,6,159,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	36925
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DATR',0,6,164,6,3
	.word	45398
	.byte	10,6,167,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37143
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DBGSR',0,6,172,6,3
	.word	45460
	.byte	10,6,175,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37358
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DBGTCR',0,6,180,6,3
	.word	45523
	.byte	10,6,183,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37452
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DCON0',0,6,188,6,3
	.word	45587
	.byte	10,6,191,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37568
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DCON2',0,6,196,6,3
	.word	45650
	.byte	10,6,199,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37669
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DCX',0,6,204,6,3
	.word	45713
	.byte	10,6,207,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37762
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DEADD',0,6,212,6,3
	.word	45774
	.byte	10,6,215,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37842
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DIEAR',0,6,220,6,3
	.word	45837
	.byte	10,6,223,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	37911
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DIETR',0,6,228,6,3
	.word	45900
	.byte	10,6,231,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38140
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DMS',0,6,236,6,3
	.word	45963
	.byte	10,6,239,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38233
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DPR_L',0,6,244,6,3
	.word	46024
	.byte	10,6,247,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38328
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DPR_U',0,6,252,6,3
	.word	46087
	.byte	10,6,255,6,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38423
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DPRE',0,6,132,7,3
	.word	46150
	.byte	10,6,135,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38513
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DPWE',0,6,140,7,3
	.word	46212
	.byte	10,6,143,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38603
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_DSTR',0,6,148,7,3
	.word	46274
	.byte	10,6,151,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	38927
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_EXEVT',0,6,156,7,3
	.word	46336
	.byte	10,6,159,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39081
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_FCX',0,6,164,7,3
	.word	46399
	.byte	10,6,167,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39187
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_FPU_TRAP_CON',0,6,172,7,3
	.word	46460
	.byte	10,6,175,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39536
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_FPU_TRAP_OPC',0,6,180,7,3
	.word	46530
	.byte	10,6,183,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39696
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_FPU_TRAP_PC',0,6,188,7,3
	.word	46600
	.byte	10,6,191,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39777
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_FPU_TRAP_SRC1',0,6,196,7,3
	.word	46669
	.byte	10,6,199,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39864
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_FPU_TRAP_SRC2',0,6,204,7,3
	.word	46740
	.byte	10,6,207,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	39951
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_FPU_TRAP_SRC3',0,6,212,7,3
	.word	46811
	.byte	10,6,215,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40038
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_ICNT',0,6,220,7,3
	.word	46882
	.byte	10,6,223,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40129
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_ICR',0,6,228,7,3
	.word	46944
	.byte	10,6,231,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40272
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_ISP',0,6,236,7,3
	.word	47005
	.byte	10,6,239,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40338
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_LCX',0,6,244,7,3
	.word	47066
	.byte	10,6,247,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40444
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_M1CNT',0,6,252,7,3
	.word	47127
	.byte	10,6,255,7,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40537
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_M2CNT',0,6,132,8,3
	.word	47190
	.byte	10,6,135,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40630
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_M3CNT',0,6,140,8,3
	.word	47253
	.byte	10,6,143,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40723
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_PC',0,6,148,8,3
	.word	47316
	.byte	10,6,151,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40808
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_PCON0',0,6,156,8,3
	.word	47376
	.byte	10,6,159,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	40924
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_PCON1',0,6,164,8,3
	.word	47439
	.byte	10,6,167,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41035
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_PCON2',0,6,172,8,3
	.word	47502
	.byte	10,6,175,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41136
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_PCXI',0,6,180,8,3
	.word	47565
	.byte	10,6,183,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41266
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_PIEAR',0,6,188,8,3
	.word	47627
	.byte	10,6,191,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41335
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_PIETR',0,6,196,8,3
	.word	47690
	.byte	10,6,199,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41564
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_PMA0',0,6,204,8,3
	.word	47753
	.byte	10,6,207,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41677
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_PMA1',0,6,212,8,3
	.word	47815
	.byte	10,6,215,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41790
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_PMA2',0,6,220,8,3
	.word	47877
	.byte	10,6,223,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	41881
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_PSTR',0,6,228,8,3
	.word	47939
	.byte	10,6,231,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42084
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_PSW',0,6,236,8,3
	.word	48001
	.byte	10,6,239,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42327
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_SEGEN',0,6,244,8,3
	.word	48062
	.byte	10,6,247,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42455
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_SMACON',0,6,252,8,3
	.word	48125
	.byte	10,6,255,8,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42696
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_SPROT_ACCENA',0,6,132,9,3
	.word	48189
	.byte	10,6,135,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42779
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_SPROT_ACCENB',0,6,140,9,3
	.word	48259
	.byte	10,6,143,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42870
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_SPROT_RGN_ACCENA',0,6,148,9,3
	.word	48329
	.byte	10,6,151,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	42961
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_SPROT_RGN_ACCENB',0,6,156,9,3
	.word	48403
	.byte	10,6,159,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43060
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_SPROT_RGN_LA',0,6,164,9,3
	.word	48477
	.byte	10,6,167,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43167
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_SPROT_RGN_UA',0,6,172,9,3
	.word	48547
	.byte	10,6,175,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43274
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_SWEVT',0,6,180,9,3
	.word	48617
	.byte	10,6,183,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43428
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_SYSCON',0,6,188,9,3
	.word	48680
	.byte	10,6,191,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43589
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_TASK_ASI',0,6,196,9,3
	.word	48744
	.byte	10,6,199,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43687
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_TPS_CON',0,6,204,9,3
	.word	48810
	.byte	10,6,207,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43859
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_TPS_TIMER',0,6,212,9,3
	.word	48875
	.byte	10,6,215,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	43939
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_TR_ADR',0,6,220,9,3
	.word	48942
	.byte	10,6,223,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	44012
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_TR_EVT',0,6,228,9,3
	.word	49006
	.byte	10,6,231,9,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	44330
	.byte	2,35,0,0,5
	.byte	'Ifx_CPU_TRIG_ACC',0,6,236,9,3
	.word	49070
	.byte	8
	.byte	'_Ifx_CPU_CPR',0,6,247,9,25,8,11
	.byte	'L',0,4
	.word	44960
	.byte	2,35,0,11
	.byte	'U',0,4
	.word	45023
	.byte	2,35,4,0,14
	.word	49136
	.byte	5
	.byte	'Ifx_CPU_CPR',0,6,251,9,3
	.word	49178
	.byte	8
	.byte	'_Ifx_CPU_DPR',0,6,254,9,25,8,11
	.byte	'L',0,4
	.word	46024
	.byte	2,35,0,11
	.byte	'U',0,4
	.word	46087
	.byte	2,35,4,0,14
	.word	49204
	.byte	5
	.byte	'Ifx_CPU_DPR',0,6,130,10,3
	.word	49246
	.byte	8
	.byte	'_Ifx_CPU_SPROT_RGN',0,6,133,10,25,16,11
	.byte	'LA',0,4
	.word	48477
	.byte	2,35,0,11
	.byte	'UA',0,4
	.word	48547
	.byte	2,35,4,11
	.byte	'ACCENA',0,4
	.word	48329
	.byte	2,35,8,11
	.byte	'ACCENB',0,4
	.word	48403
	.byte	2,35,12,0,14
	.word	49272
	.byte	5
	.byte	'Ifx_CPU_SPROT_RGN',0,6,139,10,3
	.word	49354
	.byte	8
	.byte	'_Ifx_CPU_TPS',0,6,142,10,25,16,11
	.byte	'CON',0,4
	.word	48810
	.byte	2,35,0,12,12
	.word	48875
	.byte	13,2,0,11
	.byte	'TIMER',0,12
	.word	49418
	.byte	2,35,4,0,14
	.word	49386
	.byte	5
	.byte	'Ifx_CPU_TPS',0,6,146,10,3
	.word	49443
	.byte	8
	.byte	'_Ifx_CPU_TR',0,6,149,10,25,8,11
	.byte	'EVT',0,4
	.word	49006
	.byte	2,35,0,11
	.byte	'ADR',0,4
	.word	48942
	.byte	2,35,4,0,14
	.word	49469
	.byte	5
	.byte	'Ifx_CPU_TR',0,6,153,10,3
	.word	49514
	.byte	8
	.byte	'_Ifx_SRC_SRCR_Bits',0,7,45,16,4,9
	.byte	'SRPN',0,1
	.word	309
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	309
	.byte	2,6,2,35,1,9
	.byte	'SRE',0,1
	.word	309
	.byte	1,5,2,35,1,9
	.byte	'TOS',0,1
	.word	309
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	309
	.byte	4,0,2,35,1,9
	.byte	'ECC',0,1
	.word	309
	.byte	5,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	309
	.byte	3,0,2,35,2,9
	.byte	'SRR',0,1
	.word	309
	.byte	1,7,2,35,3,9
	.byte	'CLRR',0,1
	.word	309
	.byte	1,6,2,35,3,9
	.byte	'SETR',0,1
	.word	309
	.byte	1,5,2,35,3,9
	.byte	'IOV',0,1
	.word	309
	.byte	1,4,2,35,3,9
	.byte	'IOVCLR',0,1
	.word	309
	.byte	1,3,2,35,3,9
	.byte	'SWS',0,1
	.word	309
	.byte	1,2,2,35,3,9
	.byte	'SWSCLR',0,1
	.word	309
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	309
	.byte	1,0,2,35,3,0,5
	.byte	'Ifx_SRC_SRCR_Bits',0,7,62,3
	.word	49539
	.byte	10,7,70,9,4,11
	.byte	'U',0,4
	.word	999
	.byte	2,35,0,11
	.byte	'I',0,4
	.word	9269
	.byte	2,35,0,11
	.byte	'B',0,4
	.word	49539
	.byte	2,35,0,0,5
	.byte	'Ifx_SRC_SRCR',0,7,75,3
	.word	49855
	.byte	8
	.byte	'_Ifx_SRC_ASCLIN',0,7,86,25,12,11
	.byte	'TX',0,4
	.word	49855
	.byte	2,35,0,11
	.byte	'RX',0,4
	.word	49855
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	49855
	.byte	2,35,8,0,14
	.word	49915
	.byte	5
	.byte	'Ifx_SRC_ASCLIN',0,7,91,3
	.word	49974
	.byte	8
	.byte	'_Ifx_SRC_BCUSPB',0,7,94,25,4,11
	.byte	'SBSRC',0,4
	.word	49855
	.byte	2,35,0,0,14
	.word	50002
	.byte	5
	.byte	'Ifx_SRC_BCUSPB',0,7,97,3
	.word	50039
	.byte	8
	.byte	'_Ifx_SRC_CAN',0,7,100,25,64,12,64
	.word	49855
	.byte	13,15,0,11
	.byte	'INT',0,64
	.word	50085
	.byte	2,35,0,0,14
	.word	50067
	.byte	5
	.byte	'Ifx_SRC_CAN',0,7,103,3
	.word	50108
	.byte	8
	.byte	'_Ifx_SRC_CAN1',0,7,106,25,32,12,32
	.word	49855
	.byte	13,7,0,11
	.byte	'INT',0,32
	.word	50152
	.byte	2,35,0,0,14
	.word	50133
	.byte	5
	.byte	'Ifx_SRC_CAN1',0,7,109,3
	.word	50175
	.byte	8
	.byte	'_Ifx_SRC_CCU6',0,7,112,25,16,11
	.byte	'SR0',0,4
	.word	49855
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	49855
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	49855
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	49855
	.byte	2,35,12,0,14
	.word	50201
	.byte	5
	.byte	'Ifx_SRC_CCU6',0,7,118,3
	.word	50273
	.byte	8
	.byte	'_Ifx_SRC_CERBERUS',0,7,121,25,8,12,8
	.word	49855
	.byte	13,1,0,11
	.byte	'SR',0,8
	.word	50322
	.byte	2,35,0,0,14
	.word	50299
	.byte	5
	.byte	'Ifx_SRC_CERBERUS',0,7,124,3
	.word	50344
	.byte	8
	.byte	'_Ifx_SRC_CPU',0,7,127,25,32,11
	.byte	'SBSRC',0,4
	.word	49855
	.byte	2,35,0,12,28
	.word	309
	.byte	13,27,0,11
	.byte	'reserved_4',0,28
	.word	50407
	.byte	2,35,4,0,14
	.word	50374
	.byte	5
	.byte	'Ifx_SRC_CPU',0,7,131,1,3
	.word	50437
	.byte	8
	.byte	'_Ifx_SRC_DMA',0,7,134,1,25,80,11
	.byte	'ERR',0,4
	.word	49855
	.byte	2,35,0,11
	.byte	'reserved_4',0,12
	.word	12509
	.byte	2,35,4,11
	.byte	'CH',0,64
	.word	50085
	.byte	2,35,16,0,14
	.word	50463
	.byte	5
	.byte	'Ifx_SRC_DMA',0,7,139,1,3
	.word	50528
	.byte	8
	.byte	'_Ifx_SRC_EMEM',0,7,142,1,25,4,11
	.byte	'SR',0,4
	.word	49855
	.byte	2,35,0,0,14
	.word	50554
	.byte	5
	.byte	'Ifx_SRC_EMEM',0,7,145,1,3
	.word	50587
	.byte	8
	.byte	'_Ifx_SRC_ERAY',0,7,148,1,25,80,11
	.byte	'INT',0,8
	.word	50322
	.byte	2,35,0,11
	.byte	'TINT',0,8
	.word	50322
	.byte	2,35,8,11
	.byte	'NDAT',0,8
	.word	50322
	.byte	2,35,16,11
	.byte	'MBSC',0,8
	.word	50322
	.byte	2,35,24,11
	.byte	'OBUSY',0,4
	.word	49855
	.byte	2,35,32,11
	.byte	'IBUSY',0,4
	.word	49855
	.byte	2,35,36,12,40
	.word	309
	.byte	13,39,0,11
	.byte	'reserved_28',0,40
	.word	50719
	.byte	2,35,40,0,14
	.word	50614
	.byte	5
	.byte	'Ifx_SRC_ERAY',0,7,157,1,3
	.word	50750
	.byte	8
	.byte	'_Ifx_SRC_ETH',0,7,160,1,25,4,11
	.byte	'SR',0,4
	.word	49855
	.byte	2,35,0,0,14
	.word	50777
	.byte	5
	.byte	'Ifx_SRC_ETH',0,7,163,1,3
	.word	50809
	.byte	8
	.byte	'_Ifx_SRC_EVR',0,7,166,1,25,8,11
	.byte	'WUT',0,4
	.word	49855
	.byte	2,35,0,11
	.byte	'SCDC',0,4
	.word	49855
	.byte	2,35,4,0,14
	.word	50835
	.byte	5
	.byte	'Ifx_SRC_EVR',0,7,170,1,3
	.word	50882
	.byte	8
	.byte	'_Ifx_SRC_FFT',0,7,173,1,25,12,11
	.byte	'DONE',0,4
	.word	49855
	.byte	2,35,0,11
	.byte	'ERR',0,4
	.word	49855
	.byte	2,35,4,11
	.byte	'RFS',0,4
	.word	49855
	.byte	2,35,8,0,14
	.word	50908
	.byte	5
	.byte	'Ifx_SRC_FFT',0,7,178,1,3
	.word	50968
	.byte	8
	.byte	'_Ifx_SRC_GPSR',0,7,181,1,25,128,12,11
	.byte	'SR0',0,4
	.word	49855
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	49855
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	49855
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	49855
	.byte	2,35,12,12,240,11
	.word	309
	.byte	13,239,11,0,11
	.byte	'reserved_10',0,240,11
	.word	51067
	.byte	2,35,16,0,14
	.word	50994
	.byte	5
	.byte	'Ifx_SRC_GPSR',0,7,188,1,3
	.word	51101
	.byte	8
	.byte	'_Ifx_SRC_GPT12',0,7,191,1,25,48,11
	.byte	'CIRQ',0,4
	.word	49855
	.byte	2,35,0,11
	.byte	'T2',0,4
	.word	49855
	.byte	2,35,4,11
	.byte	'T3',0,4
	.word	49855
	.byte	2,35,8,11
	.byte	'T4',0,4
	.word	49855
	.byte	2,35,12,11
	.byte	'T5',0,4
	.word	49855
	.byte	2,35,16,11
	.byte	'T6',0,4
	.word	49855
	.byte	2,35,20,12,24
	.word	309
	.byte	13,23,0,11
	.byte	'reserved_18',0,24
	.word	51223
	.byte	2,35,24,0,14
	.word	51128
	.byte	5
	.byte	'Ifx_SRC_GPT12',0,7,200,1,3
	.word	51254
	.byte	8
	.byte	'_Ifx_SRC_GTM',0,7,203,1,25,192,11,11
	.byte	'AEIIRQ',0,4
	.word	49855
	.byte	2,35,0,12,236,2
	.word	309
	.byte	13,235,2,0,11
	.byte	'reserved_4',0,236,2
	.word	51318
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	49855
	.byte	3,35,240,2,11
	.byte	'reserved_174',0,12
	.word	12509
	.byte	3,35,244,2,12,32
	.word	50152
	.byte	13,0,0,11
	.byte	'TIM',0,32
	.word	51387
	.byte	3,35,128,3,12,224,7
	.word	309
	.byte	13,223,7,0,11
	.byte	'reserved_1A0',0,224,7
	.word	51410
	.byte	3,35,160,3,12,64
	.word	50152
	.byte	13,1,0,11
	.byte	'TOM',0,64
	.word	51445
	.byte	3,35,128,11,0,14
	.word	51282
	.byte	5
	.byte	'Ifx_SRC_GTM',0,7,212,1,3
	.word	51469
	.byte	8
	.byte	'_Ifx_SRC_HSM',0,7,215,1,25,8,11
	.byte	'HSM',0,8
	.word	50322
	.byte	2,35,0,0,14
	.word	51495
	.byte	5
	.byte	'Ifx_SRC_HSM',0,7,218,1,3
	.word	51528
	.byte	8
	.byte	'_Ifx_SRC_LMU',0,7,221,1,25,4,11
	.byte	'SR',0,4
	.word	49855
	.byte	2,35,0,0,14
	.word	51554
	.byte	5
	.byte	'Ifx_SRC_LMU',0,7,224,1,3
	.word	51586
	.byte	8
	.byte	'_Ifx_SRC_PMU',0,7,227,1,25,4,11
	.byte	'SR',0,4
	.word	49855
	.byte	2,35,0,0,14
	.word	51612
	.byte	5
	.byte	'Ifx_SRC_PMU',0,7,230,1,3
	.word	51644
	.byte	8
	.byte	'_Ifx_SRC_QSPI',0,7,233,1,25,24,11
	.byte	'TX',0,4
	.word	49855
	.byte	2,35,0,11
	.byte	'RX',0,4
	.word	49855
	.byte	2,35,4,11
	.byte	'ERR',0,4
	.word	49855
	.byte	2,35,8,11
	.byte	'PT',0,4
	.word	49855
	.byte	2,35,12,11
	.byte	'HC',0,4
	.word	49855
	.byte	2,35,16,11
	.byte	'U',0,4
	.word	49855
	.byte	2,35,20,0,14
	.word	51670
	.byte	5
	.byte	'Ifx_SRC_QSPI',0,7,241,1,3
	.word	51763
	.byte	8
	.byte	'_Ifx_SRC_SCU',0,7,244,1,25,20,11
	.byte	'DTS',0,4
	.word	49855
	.byte	2,35,0,12,16
	.word	49855
	.byte	13,3,0,11
	.byte	'ERU',0,16
	.word	51822
	.byte	2,35,4,0,14
	.word	51790
	.byte	5
	.byte	'Ifx_SRC_SCU',0,7,248,1,3
	.word	51845
	.byte	8
	.byte	'_Ifx_SRC_SENT',0,7,251,1,25,16,11
	.byte	'SR',0,16
	.word	51822
	.byte	2,35,0,0,14
	.word	51871
	.byte	5
	.byte	'Ifx_SRC_SENT',0,7,254,1,3
	.word	51904
	.byte	8
	.byte	'_Ifx_SRC_SMU',0,7,129,2,25,12,12,12
	.word	49855
	.byte	13,2,0,11
	.byte	'SR',0,12
	.word	51950
	.byte	2,35,0,0,14
	.word	51931
	.byte	5
	.byte	'Ifx_SRC_SMU',0,7,132,2,3
	.word	51972
	.byte	8
	.byte	'_Ifx_SRC_STM',0,7,135,2,25,96,11
	.byte	'SR0',0,4
	.word	49855
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	49855
	.byte	2,35,4,12,88
	.word	309
	.byte	13,87,0,11
	.byte	'reserved_8',0,88
	.word	52043
	.byte	2,35,8,0,14
	.word	51998
	.byte	5
	.byte	'Ifx_SRC_STM',0,7,140,2,3
	.word	52073
	.byte	8
	.byte	'_Ifx_SRC_VADCCG',0,7,143,2,25,192,2,11
	.byte	'SR0',0,4
	.word	49855
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	49855
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	49855
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	49855
	.byte	2,35,12,12,176,2
	.word	309
	.byte	13,175,2,0,11
	.byte	'reserved_10',0,176,2
	.word	52174
	.byte	2,35,16,0,14
	.word	52099
	.byte	5
	.byte	'Ifx_SRC_VADCCG',0,7,150,2,3
	.word	52208
	.byte	8
	.byte	'_Ifx_SRC_VADCG',0,7,153,2,25,16,11
	.byte	'SR0',0,4
	.word	49855
	.byte	2,35,0,11
	.byte	'SR1',0,4
	.word	49855
	.byte	2,35,4,11
	.byte	'SR2',0,4
	.word	49855
	.byte	2,35,8,11
	.byte	'SR3',0,4
	.word	49855
	.byte	2,35,12,0,14
	.word	52237
	.byte	5
	.byte	'Ifx_SRC_VADCG',0,7,159,2,3
	.word	52311
	.byte	8
	.byte	'_Ifx_SRC_XBAR',0,7,162,2,25,4,11
	.byte	'SRC',0,4
	.word	49855
	.byte	2,35,0,0,14
	.word	52339
	.byte	5
	.byte	'Ifx_SRC_XBAR',0,7,165,2,3
	.word	52373
	.byte	8
	.byte	'_Ifx_SRC_GASCLIN',0,7,178,2,25,24,12,24
	.word	49915
	.byte	13,1,0,14
	.word	52423
	.byte	11
	.byte	'ASCLIN',0,24
	.word	52432
	.byte	2,35,0,0,14
	.word	52400
	.byte	5
	.byte	'Ifx_SRC_GASCLIN',0,7,181,2,3
	.word	52454
	.byte	8
	.byte	'_Ifx_SRC_GBCU',0,7,184,2,25,4,14
	.word	50002
	.byte	11
	.byte	'SPB',0,4
	.word	52504
	.byte	2,35,0,0,14
	.word	52484
	.byte	5
	.byte	'Ifx_SRC_GBCU',0,7,187,2,3
	.word	52523
	.byte	8
	.byte	'_Ifx_SRC_GCAN',0,7,190,2,25,96,12,64
	.word	50067
	.byte	13,0,0,14
	.word	52570
	.byte	11
	.byte	'CAN',0,64
	.word	52579
	.byte	2,35,0,12,32
	.word	50133
	.byte	13,0,0,14
	.word	52597
	.byte	11
	.byte	'CAN1',0,32
	.word	52606
	.byte	2,35,64,0,14
	.word	52550
	.byte	5
	.byte	'Ifx_SRC_GCAN',0,7,194,2,3
	.word	52626
	.byte	8
	.byte	'_Ifx_SRC_GCCU6',0,7,197,2,25,32,12,32
	.word	50201
	.byte	13,1,0,14
	.word	52674
	.byte	11
	.byte	'CCU6',0,32
	.word	52683
	.byte	2,35,0,0,14
	.word	52653
	.byte	5
	.byte	'Ifx_SRC_GCCU6',0,7,200,2,3
	.word	52703
	.byte	8
	.byte	'_Ifx_SRC_GCERBERUS',0,7,203,2,25,8,14
	.word	50299
	.byte	11
	.byte	'CERBERUS',0,8
	.word	52756
	.byte	2,35,0,0,14
	.word	52731
	.byte	5
	.byte	'Ifx_SRC_GCERBERUS',0,7,206,2,3
	.word	52780
	.byte	8
	.byte	'_Ifx_SRC_GCPU',0,7,209,2,25,32,12,32
	.word	50374
	.byte	13,0,0,14
	.word	52832
	.byte	11
	.byte	'CPU',0,32
	.word	52841
	.byte	2,35,0,0,14
	.word	52812
	.byte	5
	.byte	'Ifx_SRC_GCPU',0,7,212,2,3
	.word	52860
	.byte	8
	.byte	'_Ifx_SRC_GDMA',0,7,215,2,25,80,12,80
	.word	50463
	.byte	13,0,0,14
	.word	52907
	.byte	11
	.byte	'DMA',0,80
	.word	52916
	.byte	2,35,0,0,14
	.word	52887
	.byte	5
	.byte	'Ifx_SRC_GDMA',0,7,218,2,3
	.word	52935
	.byte	8
	.byte	'_Ifx_SRC_GEMEM',0,7,221,2,25,4,12,4
	.word	50554
	.byte	13,0,0,14
	.word	52983
	.byte	11
	.byte	'EMEM',0,4
	.word	52992
	.byte	2,35,0,0,14
	.word	52962
	.byte	5
	.byte	'Ifx_SRC_GEMEM',0,7,224,2,3
	.word	53012
	.byte	8
	.byte	'_Ifx_SRC_GERAY',0,7,227,2,25,80,12,80
	.word	50614
	.byte	13,0,0,14
	.word	53061
	.byte	11
	.byte	'ERAY',0,80
	.word	53070
	.byte	2,35,0,0,14
	.word	53040
	.byte	5
	.byte	'Ifx_SRC_GERAY',0,7,230,2,3
	.word	53090
	.byte	8
	.byte	'_Ifx_SRC_GETH',0,7,233,2,25,4,12,4
	.word	50777
	.byte	13,0,0,14
	.word	53138
	.byte	11
	.byte	'ETH',0,4
	.word	53147
	.byte	2,35,0,0,14
	.word	53118
	.byte	5
	.byte	'Ifx_SRC_GETH',0,7,236,2,3
	.word	53166
	.byte	8
	.byte	'_Ifx_SRC_GEVR',0,7,239,2,25,8,12,8
	.word	50835
	.byte	13,0,0,14
	.word	53213
	.byte	11
	.byte	'EVR',0,8
	.word	53222
	.byte	2,35,0,0,14
	.word	53193
	.byte	5
	.byte	'Ifx_SRC_GEVR',0,7,242,2,3
	.word	53241
	.byte	8
	.byte	'_Ifx_SRC_GFFT',0,7,245,2,25,12,12,12
	.word	50908
	.byte	13,0,0,14
	.word	53288
	.byte	11
	.byte	'FFT',0,12
	.word	53297
	.byte	2,35,0,0,14
	.word	53268
	.byte	5
	.byte	'Ifx_SRC_GFFT',0,7,248,2,3
	.word	53316
	.byte	8
	.byte	'_Ifx_SRC_GGPSR',0,7,251,2,25,128,12,12,128,12
	.word	50994
	.byte	13,0,0,14
	.word	53365
	.byte	11
	.byte	'GPSR',0,128,12
	.word	53375
	.byte	2,35,0,0,14
	.word	53343
	.byte	5
	.byte	'Ifx_SRC_GGPSR',0,7,254,2,3
	.word	53396
	.byte	8
	.byte	'_Ifx_SRC_GGPT12',0,7,129,3,25,48,12,48
	.word	51128
	.byte	13,0,0,14
	.word	53446
	.byte	11
	.byte	'GPT12',0,48
	.word	53455
	.byte	2,35,0,0,14
	.word	53424
	.byte	5
	.byte	'Ifx_SRC_GGPT12',0,7,132,3,3
	.word	53476
	.byte	8
	.byte	'_Ifx_SRC_GGTM',0,7,135,3,25,192,11,12,192,11
	.word	51282
	.byte	13,0,0,14
	.word	53526
	.byte	11
	.byte	'GTM',0,192,11
	.word	53536
	.byte	2,35,0,0,14
	.word	53505
	.byte	5
	.byte	'Ifx_SRC_GGTM',0,7,138,3,3
	.word	53556
	.byte	8
	.byte	'_Ifx_SRC_GHSM',0,7,141,3,25,8,12,8
	.word	51495
	.byte	13,0,0,14
	.word	53603
	.byte	11
	.byte	'HSM',0,8
	.word	53612
	.byte	2,35,0,0,14
	.word	53583
	.byte	5
	.byte	'Ifx_SRC_GHSM',0,7,144,3,3
	.word	53631
	.byte	8
	.byte	'_Ifx_SRC_GLMU',0,7,147,3,25,4,12,4
	.word	51554
	.byte	13,0,0,14
	.word	53678
	.byte	11
	.byte	'LMU',0,4
	.word	53687
	.byte	2,35,0,0,14
	.word	53658
	.byte	5
	.byte	'Ifx_SRC_GLMU',0,7,150,3,3
	.word	53706
	.byte	8
	.byte	'_Ifx_SRC_GPMU',0,7,153,3,25,8,12,8
	.word	51612
	.byte	13,1,0,14
	.word	53753
	.byte	11
	.byte	'PMU',0,8
	.word	53762
	.byte	2,35,0,0,14
	.word	53733
	.byte	5
	.byte	'Ifx_SRC_GPMU',0,7,156,3,3
	.word	53781
	.byte	8
	.byte	'_Ifx_SRC_GQSPI',0,7,159,3,25,96,12,96
	.word	51670
	.byte	13,3,0,14
	.word	53829
	.byte	11
	.byte	'QSPI',0,96
	.word	53838
	.byte	2,35,0,0,14
	.word	53808
	.byte	5
	.byte	'Ifx_SRC_GQSPI',0,7,162,3,3
	.word	53858
	.byte	8
	.byte	'_Ifx_SRC_GSCU',0,7,165,3,25,20,14
	.word	51790
	.byte	11
	.byte	'SCU',0,20
	.word	53906
	.byte	2,35,0,0,14
	.word	53886
	.byte	5
	.byte	'Ifx_SRC_GSCU',0,7,168,3,3
	.word	53925
	.byte	8
	.byte	'_Ifx_SRC_GSENT',0,7,171,3,25,16,12,16
	.word	51871
	.byte	13,0,0,14
	.word	53973
	.byte	11
	.byte	'SENT',0,16
	.word	53982
	.byte	2,35,0,0,14
	.word	53952
	.byte	5
	.byte	'Ifx_SRC_GSENT',0,7,174,3,3
	.word	54002
	.byte	8
	.byte	'_Ifx_SRC_GSMU',0,7,177,3,25,12,12,12
	.word	51931
	.byte	13,0,0,14
	.word	54050
	.byte	11
	.byte	'SMU',0,12
	.word	54059
	.byte	2,35,0,0,14
	.word	54030
	.byte	5
	.byte	'Ifx_SRC_GSMU',0,7,180,3,3
	.word	54078
	.byte	8
	.byte	'_Ifx_SRC_GSTM',0,7,183,3,25,96,12,96
	.word	51998
	.byte	13,0,0,14
	.word	54125
	.byte	11
	.byte	'STM',0,96
	.word	54134
	.byte	2,35,0,0,14
	.word	54105
	.byte	5
	.byte	'Ifx_SRC_GSTM',0,7,186,3,3
	.word	54153
	.byte	8
	.byte	'_Ifx_SRC_GVADC',0,7,189,3,25,224,4,12,64
	.word	52237
	.byte	13,3,0,14
	.word	54202
	.byte	11
	.byte	'G',0,64
	.word	54211
	.byte	2,35,0,12,224,1
	.word	309
	.byte	13,223,1,0,11
	.byte	'reserved_40',0,224,1
	.word	54227
	.byte	2,35,64,12,192,2
	.word	52099
	.byte	13,0,0,14
	.word	54260
	.byte	11
	.byte	'CG',0,192,2
	.word	54270
	.byte	3,35,160,2,0,14
	.word	54180
	.byte	5
	.byte	'Ifx_SRC_GVADC',0,7,194,3,3
	.word	54290
	.byte	8
	.byte	'_Ifx_SRC_GXBAR',0,7,197,3,25,4,14
	.word	52339
	.byte	11
	.byte	'XBAR',0,4
	.word	54339
	.byte	2,35,0,0,14
	.word	54318
	.byte	5
	.byte	'Ifx_SRC_GXBAR',0,7,200,3,3
	.word	54359
	.byte	5
	.byte	'Dma_StatusType',0,8,121,22
	.word	999
	.byte	5
	.byte	'Dma_ErrorStatusType',0,8,141,1,22
	.word	999
	.byte	15,8,147,1,9,1,16
	.byte	'DMA_CHANNEL0',0,0,16
	.byte	'DMA_CHANNEL1',0,1,16
	.byte	'DMA_CHANNEL2',0,2,16
	.byte	'DMA_CHANNEL3',0,3,16
	.byte	'DMA_CHANNEL4',0,4,16
	.byte	'DMA_CHANNEL5',0,5,16
	.byte	'DMA_CHANNEL6',0,6,16
	.byte	'DMA_CHANNEL7',0,7,16
	.byte	'DMA_CHANNEL8',0,8,16
	.byte	'DMA_CHANNEL9',0,9,16
	.byte	'DMA_CHANNEL10',0,10,16
	.byte	'DMA_CHANNEL11',0,11,16
	.byte	'DMA_CHANNEL12',0,12,16
	.byte	'DMA_CHANNEL13',0,13,16
	.byte	'DMA_CHANNEL14',0,14,16
	.byte	'DMA_CHANNEL15',0,15,16
	.byte	'DMA_CHANNEL16',0,16,16
	.byte	'DMA_CHANNEL17',0,17,16
	.byte	'DMA_CHANNEL18',0,18,16
	.byte	'DMA_CHANNEL19',0,19,16
	.byte	'DMA_CHANNEL20',0,20,16
	.byte	'DMA_CHANNEL21',0,21,16
	.byte	'DMA_CHANNEL22',0,22,16
	.byte	'DMA_CHANNEL23',0,23,16
	.byte	'DMA_CHANNEL24',0,24,16
	.byte	'DMA_CHANNEL25',0,25,16
	.byte	'DMA_CHANNEL26',0,26,16
	.byte	'DMA_CHANNEL27',0,27,16
	.byte	'DMA_CHANNEL28',0,28,16
	.byte	'DMA_CHANNEL29',0,29,16
	.byte	'DMA_CHANNEL30',0,30,16
	.byte	'DMA_CHANNEL31',0,31,16
	.byte	'DMA_CHANNEL32',0,32,16
	.byte	'DMA_CHANNEL33',0,33,16
	.byte	'DMA_CHANNEL34',0,34,16
	.byte	'DMA_CHANNEL35',0,35,16
	.byte	'DMA_CHANNEL36',0,36,16
	.byte	'DMA_CHANNEL37',0,37,16
	.byte	'DMA_CHANNEL38',0,38,16
	.byte	'DMA_CHANNEL39',0,39,16
	.byte	'DMA_CHANNEL40',0,40,16
	.byte	'DMA_CHANNEL41',0,41,16
	.byte	'DMA_CHANNEL42',0,42,16
	.byte	'DMA_CHANNEL43',0,43,16
	.byte	'DMA_CHANNEL44',0,44,16
	.byte	'DMA_CHANNEL45',0,45,16
	.byte	'DMA_CHANNEL46',0,46,16
	.byte	'DMA_CHANNEL47',0,47,16
	.byte	'DMA_CHANNEL48',0,48,16
	.byte	'DMA_CHANNEL49',0,49,16
	.byte	'DMA_CHANNEL50',0,50,16
	.byte	'DMA_CHANNEL51',0,51,16
	.byte	'DMA_CHANNEL52',0,52,16
	.byte	'DMA_CHANNEL53',0,53,16
	.byte	'DMA_CHANNEL54',0,54,16
	.byte	'DMA_CHANNEL55',0,55,16
	.byte	'DMA_CHANNEL56',0,56,16
	.byte	'DMA_CHANNEL57',0,57,16
	.byte	'DMA_CHANNEL58',0,58,16
	.byte	'DMA_CHANNEL59',0,59,16
	.byte	'DMA_CHANNEL60',0,60,16
	.byte	'DMA_CHANNEL61',0,61,16
	.byte	'DMA_CHANNEL62',0,62,16
	.byte	'DMA_CHANNEL63',0,63,16
	.byte	'DMA_CHANNEL64',0,192,0,16
	.byte	'DMA_CHANNEL65',0,193,0,16
	.byte	'DMA_CHANNEL66',0,194,0,16
	.byte	'DMA_CHANNEL67',0,195,0,16
	.byte	'DMA_CHANNEL68',0,196,0,16
	.byte	'DMA_CHANNEL69',0,197,0,16
	.byte	'DMA_CHANNEL70',0,198,0,16
	.byte	'DMA_CHANNEL71',0,199,0,16
	.byte	'DMA_CHANNEL72',0,200,0,16
	.byte	'DMA_CHANNEL73',0,201,0,16
	.byte	'DMA_CHANNEL74',0,202,0,16
	.byte	'DMA_CHANNEL75',0,203,0,16
	.byte	'DMA_CHANNEL76',0,204,0,16
	.byte	'DMA_CHANNEL77',0,205,0,16
	.byte	'DMA_CHANNEL78',0,206,0,16
	.byte	'DMA_CHANNEL79',0,207,0,16
	.byte	'DMA_CHANNEL80',0,208,0,16
	.byte	'DMA_CHANNEL81',0,209,0,16
	.byte	'DMA_CHANNEL82',0,210,0,16
	.byte	'DMA_CHANNEL83',0,211,0,16
	.byte	'DMA_CHANNEL84',0,212,0,16
	.byte	'DMA_CHANNEL85',0,213,0,16
	.byte	'DMA_CHANNEL86',0,214,0,16
	.byte	'DMA_CHANNEL87',0,215,0,16
	.byte	'DMA_CHANNEL88',0,216,0,16
	.byte	'DMA_CHANNEL89',0,217,0,16
	.byte	'DMA_CHANNEL90',0,218,0,16
	.byte	'DMA_CHANNEL91',0,219,0,16
	.byte	'DMA_CHANNEL92',0,220,0,16
	.byte	'DMA_CHANNEL93',0,221,0,16
	.byte	'DMA_CHANNEL94',0,222,0,16
	.byte	'DMA_CHANNEL95',0,223,0,16
	.byte	'DMA_CHANNEL96',0,224,0,16
	.byte	'DMA_CHANNEL97',0,225,0,16
	.byte	'DMA_CHANNEL98',0,226,0,16
	.byte	'DMA_CHANNEL99',0,227,0,16
	.byte	'DMA_CHANNEL100',0,228,0,16
	.byte	'DMA_CHANNEL101',0,229,0,16
	.byte	'DMA_CHANNEL102',0,230,0,16
	.byte	'DMA_CHANNEL103',0,231,0,16
	.byte	'DMA_CHANNEL104',0,232,0,16
	.byte	'DMA_CHANNEL105',0,233,0,16
	.byte	'DMA_CHANNEL106',0,234,0,16
	.byte	'DMA_CHANNEL107',0,235,0,16
	.byte	'DMA_CHANNEL108',0,236,0,16
	.byte	'DMA_CHANNEL109',0,237,0,16
	.byte	'DMA_CHANNEL110',0,238,0,16
	.byte	'DMA_CHANNEL111',0,239,0,16
	.byte	'DMA_CHANNEL112',0,240,0,16
	.byte	'DMA_CHANNEL113',0,241,0,16
	.byte	'DMA_CHANNEL114',0,242,0,16
	.byte	'DMA_CHANNEL115',0,243,0,16
	.byte	'DMA_CHANNEL116',0,244,0,16
	.byte	'DMA_CHANNEL117',0,245,0,16
	.byte	'DMA_CHANNEL118',0,246,0,16
	.byte	'DMA_CHANNEL119',0,247,0,16
	.byte	'DMA_CHANNEL120',0,248,0,16
	.byte	'DMA_CHANNEL121',0,249,0,16
	.byte	'DMA_CHANNEL122',0,250,0,16
	.byte	'DMA_CHANNEL123',0,251,0,16
	.byte	'DMA_CHANNEL124',0,252,0,16
	.byte	'DMA_CHANNEL125',0,253,0,16
	.byte	'DMA_CHANNEL126',0,254,0,16
	.byte	'DMA_CHANNEL127',0,255,0,16
	.byte	'DMA_CHANNEL_INVALID',0,255,1,0,5
	.byte	'Dma_ChannelType',0,8,149,2,2
	.word	54439
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L204:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,46,0,3,8,58,15,59,15,57,15,54,15,39,12,63,12,60,12,0,0
	.byte	3,59,0,3,8,0,0,4,15,0,73,19,0,0,5,22,0,3,8,58,15,59,15,57,15,73,19,0,0,6,21,0,54,15,0,0,7,36,0,3,8,11
	.byte	15,62,15,0,0,8,19,1,3,8,58,15,59,15,57,15,11,15,0,0,9,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,10,23
	.byte	1,58,15,59,15,57,15,11,15,0,0,11,13,0,3,8,11,15,73,19,56,9,0,0,12,1,1,11,15,73,19,0,0,13,33,0,47,15,0
	.byte	0,14,53,0,73,19,0,0,15,4,1,58,15,59,15,57,15,11,15,0,0,16,40,0,3,8,28,13,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L205:
	.word	.L807-.L806
.L806:
	.half	3
	.word	.L809-.L808
.L808:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Os.h',0,0,0,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxDma_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxScu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxCpu_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxSrc_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Mcal_DmaLib.h',0,0,0,0,0
.L809:
.L807:
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Adc_StartGroup')
	.sect	'.debug_info'
.L206:
	.word	234
	.half	3
	.word	.L207
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L209,.L208
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Adc_StartGroup',0,1,88,6,1,1,1
	.word	.L3,.L706,.L2
	.byte	4
	.word	.L3,.L706
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Adc_StartGroup')
	.sect	'.debug_abbrev'
.L207:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Adc_StartGroup')
	.sect	'.debug_line'
.L208:
	.word	.L811-.L810
.L810:
	.half	3
	.word	.L813-.L812
.L812:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L813:
	.byte	5,3,7,0,5,2
	.word	.L3
	.byte	3,217,0,1,5,1,7,9
	.half	.L210-.L3
	.byte	3,1,0,1,1
.L811:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Adc_StartGroup')
	.sect	'.debug_ranges'
.L209:
	.word	-1,.L3,0,.L210-.L3,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Adc_StopGroup')
	.sect	'.debug_info'
.L211:
	.word	233
	.half	3
	.word	.L212
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L214,.L213
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Adc_StopGroup',0,1,93,6,1,1,1
	.word	.L5,.L707,.L4
	.byte	4
	.word	.L5,.L707
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Adc_StopGroup')
	.sect	'.debug_abbrev'
.L212:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Adc_StopGroup')
	.sect	'.debug_line'
.L213:
	.word	.L815-.L814
.L814:
	.half	3
	.word	.L817-.L816
.L816:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L817:
	.byte	5,3,7,0,5,2
	.word	.L5
	.byte	3,222,0,1,5,1,7,9
	.half	.L215-.L5
	.byte	3,1,0,1,1
.L815:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Adc_StopGroup')
	.sect	'.debug_ranges'
.L214:
	.word	-1,.L5,0,.L215-.L5,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Adc_ReadGroup')
	.sect	'.debug_info'
.L216:
	.word	233
	.half	3
	.word	.L217
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L219,.L218
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Adc_ReadGroup',0,1,98,6,1,1,1
	.word	.L7,.L708,.L6
	.byte	4
	.word	.L7,.L708
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Adc_ReadGroup')
	.sect	'.debug_abbrev'
.L217:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Adc_ReadGroup')
	.sect	'.debug_line'
.L218:
	.word	.L819-.L818
.L818:
	.half	3
	.word	.L821-.L820
.L820:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L821:
	.byte	5,3,7,0,5,2
	.word	.L7
	.byte	3,227,0,1,5,1,7,9
	.half	.L220-.L7
	.byte	3,1,0,1,1
.L819:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Adc_ReadGroup')
	.sect	'.debug_ranges'
.L219:
	.word	-1,.L7,0,.L220-.L7,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Adc_EnableHwTrig')
	.sect	'.debug_info'
.L221:
	.word	236
	.half	3
	.word	.L222
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L224,.L223
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Adc_EnableHwTrig',0,1,103,6,1,1,1
	.word	.L9,.L709,.L8
	.byte	4
	.word	.L9,.L709
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Adc_EnableHwTrig')
	.sect	'.debug_abbrev'
.L222:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Adc_EnableHwTrig')
	.sect	'.debug_line'
.L223:
	.word	.L823-.L822
.L822:
	.half	3
	.word	.L825-.L824
.L824:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L825:
	.byte	5,3,7,0,5,2
	.word	.L9
	.byte	3,232,0,1,5,1,7,9
	.half	.L225-.L9
	.byte	3,1,0,1,1
.L823:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Adc_EnableHwTrig')
	.sect	'.debug_ranges'
.L224:
	.word	-1,.L9,0,.L225-.L9,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Adc_DisableHwTrig')
	.sect	'.debug_info'
.L226:
	.word	237
	.half	3
	.word	.L227
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L229,.L228
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Adc_DisableHwTrig',0,1,108,6,1,1,1
	.word	.L11,.L710,.L10
	.byte	4
	.word	.L11,.L710
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Adc_DisableHwTrig')
	.sect	'.debug_abbrev'
.L227:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Adc_DisableHwTrig')
	.sect	'.debug_line'
.L228:
	.word	.L827-.L826
.L826:
	.half	3
	.word	.L829-.L828
.L828:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L829:
	.byte	5,3,7,0,5,2
	.word	.L11
	.byte	3,237,0,1,5,1,7,9
	.half	.L230-.L11
	.byte	3,1,0,1,1
.L827:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Adc_DisableHwTrig')
	.sect	'.debug_ranges'
.L229:
	.word	-1,.L11,0,.L230-.L11,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Adc_GetGrpStatus')
	.sect	'.debug_info'
.L231:
	.word	236
	.half	3
	.word	.L232
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L234,.L233
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Adc_GetGrpStatus',0,1,113,6,1,1,1
	.word	.L13,.L711,.L12
	.byte	4
	.word	.L13,.L711
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Adc_GetGrpStatus')
	.sect	'.debug_abbrev'
.L232:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Adc_GetGrpStatus')
	.sect	'.debug_line'
.L233:
	.word	.L831-.L830
.L830:
	.half	3
	.word	.L833-.L832
.L832:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L833:
	.byte	5,3,7,0,5,2
	.word	.L13
	.byte	3,242,0,1,5,1,7,9
	.half	.L235-.L13
	.byte	3,1,0,1,1
.L831:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Adc_GetGrpStatus')
	.sect	'.debug_ranges'
.L234:
	.word	-1,.L13,0,.L235-.L13,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Adc_GetStreamLastPtr')
	.sect	'.debug_info'
.L236:
	.word	240
	.half	3
	.word	.L237
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L239,.L238
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Adc_GetStreamLastPtr',0,1,118,6,1,1,1
	.word	.L15,.L712,.L14
	.byte	4
	.word	.L15,.L712
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Adc_GetStreamLastPtr')
	.sect	'.debug_abbrev'
.L237:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Adc_GetStreamLastPtr')
	.sect	'.debug_line'
.L238:
	.word	.L835-.L834
.L834:
	.half	3
	.word	.L837-.L836
.L836:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L837:
	.byte	5,3,7,0,5,2
	.word	.L15
	.byte	3,247,0,1,5,1,7,9
	.half	.L240-.L15
	.byte	3,1,0,1,1
.L835:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Adc_GetStreamLastPtr')
	.sect	'.debug_ranges'
.L239:
	.word	-1,.L15,0,.L240-.L15,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Adc_ScheduleStart')
	.sect	'.debug_info'
.L241:
	.word	237
	.half	3
	.word	.L242
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L244,.L243
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Adc_ScheduleStart',0,1,123,6,1,1,1
	.word	.L17,.L713,.L16
	.byte	4
	.word	.L17,.L713
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Adc_ScheduleStart')
	.sect	'.debug_abbrev'
.L242:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Adc_ScheduleStart')
	.sect	'.debug_line'
.L243:
	.word	.L839-.L838
.L838:
	.half	3
	.word	.L841-.L840
.L840:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L841:
	.byte	5,3,7,0,5,2
	.word	.L17
	.byte	3,252,0,1,5,1,7,9
	.half	.L245-.L17
	.byte	3,1,0,1,1
.L839:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Adc_ScheduleStart')
	.sect	'.debug_ranges'
.L244:
	.word	-1,.L17,0,.L245-.L17,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Adc_ScheduleStop')
	.sect	'.debug_info'
.L246:
	.word	237
	.half	3
	.word	.L247
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L249,.L248
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Adc_ScheduleStop',0,1,128,1,6,1,1,1
	.word	.L19,.L714,.L18
	.byte	4
	.word	.L19,.L714
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Adc_ScheduleStop')
	.sect	'.debug_abbrev'
.L247:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Adc_ScheduleStop')
	.sect	'.debug_line'
.L248:
	.word	.L843-.L842
.L842:
	.half	3
	.word	.L845-.L844
.L844:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L845:
	.byte	5,3,7,0,5,2
	.word	.L19
	.byte	3,129,1,1,5,1,7,9
	.half	.L250-.L19
	.byte	3,1,0,1,1
.L843:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Adc_ScheduleStop')
	.sect	'.debug_ranges'
.L249:
	.word	-1,.L19,0,.L250-.L19,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Adc_ScheduleNext')
	.sect	'.debug_info'
.L251:
	.word	237
	.half	3
	.word	.L252
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L254,.L253
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Adc_ScheduleNext',0,1,133,1,6,1,1,1
	.word	.L21,.L715,.L20
	.byte	4
	.word	.L21,.L715
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Adc_ScheduleNext')
	.sect	'.debug_abbrev'
.L252:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Adc_ScheduleNext')
	.sect	'.debug_line'
.L253:
	.word	.L847-.L846
.L846:
	.half	3
	.word	.L849-.L848
.L848:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L849:
	.byte	5,3,7,0,5,2
	.word	.L21
	.byte	3,134,1,1,5,1,7,9
	.half	.L255-.L21
	.byte	3,1,0,1,1
.L847:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Adc_ScheduleNext')
	.sect	'.debug_ranges'
.L254:
	.word	-1,.L21,0,.L255-.L21,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Adc_PushQueue')
	.sect	'.debug_info'
.L256:
	.word	234
	.half	3
	.word	.L257
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L259,.L258
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Adc_PushQueue',0,1,138,1,6,1,1,1
	.word	.L23,.L716,.L22
	.byte	4
	.word	.L23,.L716
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Adc_PushQueue')
	.sect	'.debug_abbrev'
.L257:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Adc_PushQueue')
	.sect	'.debug_line'
.L258:
	.word	.L851-.L850
.L850:
	.half	3
	.word	.L853-.L852
.L852:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L853:
	.byte	5,3,7,0,5,2
	.word	.L23
	.byte	3,139,1,1,5,1,7,9
	.half	.L260-.L23
	.byte	3,1,0,1,1
.L851:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Adc_PushQueue')
	.sect	'.debug_ranges'
.L259:
	.word	-1,.L23,0,.L260-.L23,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Adc_PopQueue')
	.sect	'.debug_info'
.L261:
	.word	233
	.half	3
	.word	.L262
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L264,.L263
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Adc_PopQueue',0,1,143,1,6,1,1,1
	.word	.L25,.L717,.L24
	.byte	4
	.word	.L25,.L717
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Adc_PopQueue')
	.sect	'.debug_abbrev'
.L262:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Adc_PopQueue')
	.sect	'.debug_line'
.L263:
	.word	.L855-.L854
.L854:
	.half	3
	.word	.L857-.L856
.L856:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L857:
	.byte	5,3,7,0,5,2
	.word	.L25
	.byte	3,144,1,1,5,1,7,9
	.half	.L265-.L25
	.byte	3,1,0,1,1
.L855:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Adc_PopQueue')
	.sect	'.debug_ranges'
.L264:
	.word	-1,.L25,0,.L265-.L25,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Adc_StartGroup')
	.sect	'.debug_info'
.L266:
	.word	234
	.half	3
	.word	.L267
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L269,.L268
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Adc_StartGroup',0,1,148,1,6,1,1,1
	.word	.L27,.L718,.L26
	.byte	4
	.word	.L27,.L718
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Adc_StartGroup')
	.sect	'.debug_abbrev'
.L267:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Adc_StartGroup')
	.sect	'.debug_line'
.L268:
	.word	.L859-.L858
.L858:
	.half	3
	.word	.L861-.L860
.L860:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L861:
	.byte	5,3,7,0,5,2
	.word	.L27
	.byte	3,149,1,1,5,1,7,9
	.half	.L270-.L27
	.byte	3,1,0,1,1
.L859:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Adc_StartGroup')
	.sect	'.debug_ranges'
.L269:
	.word	-1,.L27,0,.L270-.L27,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Adc_StopGroup')
	.sect	'.debug_info'
.L271:
	.word	233
	.half	3
	.word	.L272
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L274,.L273
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Adc_StopGroup',0,1,153,1,6,1,1,1
	.word	.L29,.L719,.L28
	.byte	4
	.word	.L29,.L719
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Adc_StopGroup')
	.sect	'.debug_abbrev'
.L272:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Adc_StopGroup')
	.sect	'.debug_line'
.L273:
	.word	.L863-.L862
.L862:
	.half	3
	.word	.L865-.L864
.L864:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L865:
	.byte	5,3,7,0,5,2
	.word	.L29
	.byte	3,154,1,1,5,1,7,9
	.half	.L275-.L29
	.byte	3,1,0,1,1
.L863:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Adc_StopGroup')
	.sect	'.debug_ranges'
.L274:
	.word	-1,.L29,0,.L275-.L29,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Adc_ReadGroup')
	.sect	'.debug_info'
.L276:
	.word	233
	.half	3
	.word	.L277
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L279,.L278
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Adc_ReadGroup',0,1,158,1,6,1,1,1
	.word	.L31,.L720,.L30
	.byte	4
	.word	.L31,.L720
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Adc_ReadGroup')
	.sect	'.debug_abbrev'
.L277:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Adc_ReadGroup')
	.sect	'.debug_line'
.L278:
	.word	.L867-.L866
.L866:
	.half	3
	.word	.L869-.L868
.L868:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L869:
	.byte	5,3,7,0,5,2
	.word	.L31
	.byte	3,159,1,1,5,1,7,9
	.half	.L280-.L31
	.byte	3,1,0,1,1
.L867:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Adc_ReadGroup')
	.sect	'.debug_ranges'
.L279:
	.word	-1,.L31,0,.L280-.L31,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Adc_EnableHwTrig')
	.sect	'.debug_info'
.L281:
	.word	236
	.half	3
	.word	.L282
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L284,.L283
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Adc_EnableHwTrig',0,1,163,1,6,1,1,1
	.word	.L33,.L721,.L32
	.byte	4
	.word	.L33,.L721
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Adc_EnableHwTrig')
	.sect	'.debug_abbrev'
.L282:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Adc_EnableHwTrig')
	.sect	'.debug_line'
.L283:
	.word	.L871-.L870
.L870:
	.half	3
	.word	.L873-.L872
.L872:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L873:
	.byte	5,3,7,0,5,2
	.word	.L33
	.byte	3,164,1,1,5,1,7,9
	.half	.L285-.L33
	.byte	3,1,0,1,1
.L871:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Adc_EnableHwTrig')
	.sect	'.debug_ranges'
.L284:
	.word	-1,.L33,0,.L285-.L33,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Adc_DisableHwTrig')
	.sect	'.debug_info'
.L286:
	.word	237
	.half	3
	.word	.L287
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L289,.L288
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Adc_DisableHwTrig',0,1,168,1,6,1,1,1
	.word	.L35,.L722,.L34
	.byte	4
	.word	.L35,.L722
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Adc_DisableHwTrig')
	.sect	'.debug_abbrev'
.L287:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Adc_DisableHwTrig')
	.sect	'.debug_line'
.L288:
	.word	.L875-.L874
.L874:
	.half	3
	.word	.L877-.L876
.L876:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L877:
	.byte	5,3,7,0,5,2
	.word	.L35
	.byte	3,169,1,1,5,1,7,9
	.half	.L290-.L35
	.byte	3,1,0,1,1
.L875:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Adc_DisableHwTrig')
	.sect	'.debug_ranges'
.L289:
	.word	-1,.L35,0,.L290-.L35,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Adc_GetGrpStatus')
	.sect	'.debug_info'
.L291:
	.word	236
	.half	3
	.word	.L292
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L294,.L293
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Adc_GetGrpStatus',0,1,173,1,6,1,1,1
	.word	.L37,.L723,.L36
	.byte	4
	.word	.L37,.L723
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Adc_GetGrpStatus')
	.sect	'.debug_abbrev'
.L292:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Adc_GetGrpStatus')
	.sect	'.debug_line'
.L293:
	.word	.L879-.L878
.L878:
	.half	3
	.word	.L881-.L880
.L880:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L881:
	.byte	5,3,7,0,5,2
	.word	.L37
	.byte	3,174,1,1,5,1,7,9
	.half	.L295-.L37
	.byte	3,1,0,1,1
.L879:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Adc_GetGrpStatus')
	.sect	'.debug_ranges'
.L294:
	.word	-1,.L37,0,.L295-.L37,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Adc_GetStreamLastPtr')
	.sect	'.debug_info'
.L296:
	.word	240
	.half	3
	.word	.L297
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L299,.L298
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Adc_GetStreamLastPtr',0,1,178,1,6,1,1,1
	.word	.L39,.L724,.L38
	.byte	4
	.word	.L39,.L724
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Adc_GetStreamLastPtr')
	.sect	'.debug_abbrev'
.L297:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Adc_GetStreamLastPtr')
	.sect	'.debug_line'
.L298:
	.word	.L883-.L882
.L882:
	.half	3
	.word	.L885-.L884
.L884:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L885:
	.byte	5,3,7,0,5,2
	.word	.L39
	.byte	3,179,1,1,5,1,7,9
	.half	.L300-.L39
	.byte	3,1,0,1,1
.L883:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Adc_GetStreamLastPtr')
	.sect	'.debug_ranges'
.L299:
	.word	-1,.L39,0,.L300-.L39,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Adc_ScheduleStart')
	.sect	'.debug_info'
.L301:
	.word	237
	.half	3
	.word	.L302
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L304,.L303
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Adc_ScheduleStart',0,1,183,1,6,1,1,1
	.word	.L41,.L725,.L40
	.byte	4
	.word	.L41,.L725
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Adc_ScheduleStart')
	.sect	'.debug_abbrev'
.L302:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Adc_ScheduleStart')
	.sect	'.debug_line'
.L303:
	.word	.L887-.L886
.L886:
	.half	3
	.word	.L889-.L888
.L888:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L889:
	.byte	5,3,7,0,5,2
	.word	.L41
	.byte	3,184,1,1,5,1,7,9
	.half	.L305-.L41
	.byte	3,1,0,1,1
.L887:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Adc_ScheduleStart')
	.sect	'.debug_ranges'
.L304:
	.word	-1,.L41,0,.L305-.L41,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Adc_ScheduleStop')
	.sect	'.debug_info'
.L306:
	.word	236
	.half	3
	.word	.L307
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L309,.L308
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Adc_ScheduleStop',0,1,188,1,6,1,1,1
	.word	.L43,.L726,.L42
	.byte	4
	.word	.L43,.L726
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Adc_ScheduleStop')
	.sect	'.debug_abbrev'
.L307:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Adc_ScheduleStop')
	.sect	'.debug_line'
.L308:
	.word	.L891-.L890
.L890:
	.half	3
	.word	.L893-.L892
.L892:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L893:
	.byte	5,3,7,0,5,2
	.word	.L43
	.byte	3,189,1,1,5,1,7,9
	.half	.L310-.L43
	.byte	3,1,0,1,1
.L891:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Adc_ScheduleStop')
	.sect	'.debug_ranges'
.L309:
	.word	-1,.L43,0,.L310-.L43,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Adc_ScheduleNext')
	.sect	'.debug_info'
.L311:
	.word	236
	.half	3
	.word	.L312
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L314,.L313
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Adc_ScheduleNext',0,1,193,1,6,1,1,1
	.word	.L45,.L727,.L44
	.byte	4
	.word	.L45,.L727
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Adc_ScheduleNext')
	.sect	'.debug_abbrev'
.L312:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Adc_ScheduleNext')
	.sect	'.debug_line'
.L313:
	.word	.L895-.L894
.L894:
	.half	3
	.word	.L897-.L896
.L896:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L897:
	.byte	5,3,7,0,5,2
	.word	.L45
	.byte	3,194,1,1,5,1,7,9
	.half	.L315-.L45
	.byte	3,1,0,1,1
.L895:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Adc_ScheduleNext')
	.sect	'.debug_ranges'
.L314:
	.word	-1,.L45,0,.L315-.L45,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Adc_PushQueue')
	.sect	'.debug_info'
.L316:
	.word	233
	.half	3
	.word	.L317
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L319,.L318
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Adc_PushQueue',0,1,198,1,6,1,1,1
	.word	.L47,.L728,.L46
	.byte	4
	.word	.L47,.L728
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Adc_PushQueue')
	.sect	'.debug_abbrev'
.L317:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Adc_PushQueue')
	.sect	'.debug_line'
.L318:
	.word	.L899-.L898
.L898:
	.half	3
	.word	.L901-.L900
.L900:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L901:
	.byte	5,3,7,0,5,2
	.word	.L47
	.byte	3,199,1,1,5,1,7,9
	.half	.L320-.L47
	.byte	3,1,0,1,1
.L899:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Adc_PushQueue')
	.sect	'.debug_ranges'
.L319:
	.word	-1,.L47,0,.L320-.L47,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Adc_PopQueue')
	.sect	'.debug_info'
.L321:
	.word	232
	.half	3
	.word	.L322
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L324,.L323
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Adc_PopQueue',0,1,203,1,6,1,1,1
	.word	.L49,.L729,.L48
	.byte	4
	.word	.L49,.L729
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Adc_PopQueue')
	.sect	'.debug_abbrev'
.L322:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Adc_PopQueue')
	.sect	'.debug_line'
.L323:
	.word	.L903-.L902
.L902:
	.half	3
	.word	.L905-.L904
.L904:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L905:
	.byte	5,3,7,0,5,2
	.word	.L49
	.byte	3,204,1,1,5,1,7,9
	.half	.L325-.L49
	.byte	3,1,0,1,1
.L903:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Adc_PopQueue')
	.sect	'.debug_ranges'
.L324:
	.word	-1,.L49,0,.L325-.L49,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.debug_info'
.L326:
	.word	248
	.half	3
	.word	.L327
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L329,.L328
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Icu_17_GtmCcu6_EnableWakeup',0,1,209,1,6,1,1,1
	.word	.L51,.L730,.L50
	.byte	4
	.word	.L51,.L730
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.debug_abbrev'
.L327:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.debug_line'
.L328:
	.word	.L907-.L906
.L906:
	.half	3
	.word	.L909-.L908
.L908:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L909:
	.byte	5,3,7,0,5,2
	.word	.L51
	.byte	3,210,1,1,5,1,7,9
	.half	.L330-.L51
	.byte	3,1,0,1,1
.L907:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.debug_ranges'
.L329:
	.word	-1,.L51,0,.L330-.L51,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.debug_info'
.L331:
	.word	247
	.half	3
	.word	.L332
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L334,.L333
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Icu_17_GtmCcu6_EnableWakeup',0,1,214,1,6,1,1,1
	.word	.L53,.L731,.L52
	.byte	4
	.word	.L53,.L731
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.debug_abbrev'
.L332:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.debug_line'
.L333:
	.word	.L911-.L910
.L910:
	.half	3
	.word	.L913-.L912
.L912:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L913:
	.byte	5,3,7,0,5,2
	.word	.L53
	.byte	3,215,1,1,5,1,7,9
	.half	.L335-.L53
	.byte	3,1,0,1,1
.L911:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.debug_ranges'
.L334:
	.word	-1,.L53,0,.L335-.L53,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.debug_info'
.L336:
	.word	254
	.half	3
	.word	.L337
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L339,.L338
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Icu_17_GtmCcu6_EnableNotification',0,1,219,1,6,1,1,1
	.word	.L55,.L732,.L54
	.byte	4
	.word	.L55,.L732
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.debug_abbrev'
.L337:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.debug_line'
.L338:
	.word	.L915-.L914
.L914:
	.half	3
	.word	.L917-.L916
.L916:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L917:
	.byte	5,3,7,0,5,2
	.word	.L55
	.byte	3,220,1,1,5,1,7,9
	.half	.L340-.L55
	.byte	3,1,0,1,1
.L915:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.debug_ranges'
.L339:
	.word	-1,.L55,0,.L340-.L55,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.debug_info'
.L341:
	.word	253
	.half	3
	.word	.L342
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L344,.L343
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Icu_17_GtmCcu6_EnableNotification',0,1,224,1,6,1,1,1
	.word	.L57,.L733,.L56
	.byte	4
	.word	.L57,.L733
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.debug_abbrev'
.L342:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.debug_line'
.L343:
	.word	.L919-.L918
.L918:
	.half	3
	.word	.L921-.L920
.L920:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L921:
	.byte	5,3,7,0,5,2
	.word	.L57
	.byte	3,225,1,1,5,1,7,9
	.half	.L345-.L57
	.byte	3,1,0,1,1
.L919:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.debug_ranges'
.L344:
	.word	-1,.L57,0,.L345-.L57,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.debug_info'
.L346:
	.word	250
	.half	3
	.word	.L347
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L349,.L348
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount',0,1,229,1,6,1,1,1
	.word	.L59,.L734,.L58
	.byte	4
	.word	.L59,.L734
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.debug_abbrev'
.L347:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.debug_line'
.L348:
	.word	.L923-.L922
.L922:
	.half	3
	.word	.L925-.L924
.L924:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L925:
	.byte	5,3,7,0,5,2
	.word	.L59
	.byte	3,230,1,1,5,1,7,9
	.half	.L350-.L59
	.byte	3,1,0,1,1
.L923:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.debug_ranges'
.L349:
	.word	-1,.L59,0,.L350-.L59,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.debug_info'
.L351:
	.word	249
	.half	3
	.word	.L352
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L354,.L353
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount',0,1,234,1,6,1,1,1
	.word	.L61,.L735,.L60
	.byte	4
	.word	.L61,.L735
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.debug_abbrev'
.L352:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.debug_line'
.L353:
	.word	.L927-.L926
.L926:
	.half	3
	.word	.L929-.L928
.L928:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L929:
	.byte	5,3,7,0,5,2
	.word	.L61
	.byte	3,235,1,1,5,1,7,9
	.half	.L355-.L61
	.byte	3,1,0,1,1
.L927:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.debug_ranges'
.L354:
	.word	-1,.L61,0,.L355-.L61,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.debug_info'
.L356:
	.word	249
	.half	3
	.word	.L357
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L359,.L358
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate',0,1,239,1,6,1,1,1
	.word	.L63,.L736,.L62
	.byte	4
	.word	.L63,.L736
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.debug_abbrev'
.L357:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.debug_line'
.L358:
	.word	.L931-.L930
.L930:
	.half	3
	.word	.L933-.L932
.L932:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L933:
	.byte	5,3,7,0,5,2
	.word	.L63
	.byte	3,240,1,1,5,1,7,9
	.half	.L360-.L63
	.byte	3,1,0,1,1
.L931:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.debug_ranges'
.L359:
	.word	-1,.L63,0,.L360-.L63,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.debug_info'
.L361:
	.word	248
	.half	3
	.word	.L362
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L364,.L363
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate',0,1,244,1,6,1,1,1
	.word	.L65,.L737,.L64
	.byte	4
	.word	.L65,.L737
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.debug_abbrev'
.L362:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.debug_line'
.L363:
	.word	.L935-.L934
.L934:
	.half	3
	.word	.L937-.L936
.L936:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L937:
	.byte	5,3,7,0,5,2
	.word	.L65
	.byte	3,245,1,1,5,1,7,9
	.half	.L365-.L65
	.byte	3,1,0,1,1
.L935:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.debug_ranges'
.L364:
	.word	-1,.L65,0,.L365-.L65,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.debug_info'
.L366:
	.word	254
	.half	3
	.word	.L367
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L369,.L368
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle',0,1,249,1,6,1,1,1
	.word	.L67,.L738,.L66
	.byte	4
	.word	.L67,.L738
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.debug_abbrev'
.L367:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.debug_line'
.L368:
	.word	.L939-.L938
.L938:
	.half	3
	.word	.L941-.L940
.L940:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L941:
	.byte	5,3,7,0,5,2
	.word	.L67
	.byte	3,250,1,1,5,1,7,9
	.half	.L370-.L67
	.byte	3,1,0,1,1
.L939:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.debug_ranges'
.L369:
	.word	-1,.L67,0,.L370-.L67,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.debug_info'
.L371:
	.word	253
	.half	3
	.word	.L372
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L374,.L373
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle',0,1,254,1,6,1,1,1
	.word	.L69,.L739,.L68
	.byte	4
	.word	.L69,.L739
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.debug_abbrev'
.L372:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.debug_line'
.L373:
	.word	.L943-.L942
.L942:
	.half	3
	.word	.L945-.L944
.L944:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L945:
	.byte	5,3,7,0,5,2
	.word	.L69
	.byte	3,255,1,1,5,1,7,9
	.half	.L375-.L69
	.byte	3,1,0,1,1
.L943:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.debug_ranges'
.L374:
	.word	-1,.L69,0,.L375-.L69,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.debug_info'
.L376:
	.word	253
	.half	3
	.word	.L377
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L379,.L378
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate',0,1,131,2,6,1,1,1
	.word	.L71,.L740,.L70
	.byte	4
	.word	.L71,.L740
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.debug_abbrev'
.L377:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.debug_line'
.L378:
	.word	.L947-.L946
.L946:
	.half	3
	.word	.L949-.L948
.L948:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L949:
	.byte	5,3,7,0,5,2
	.word	.L71
	.byte	3,132,2,1,5,1,7,9
	.half	.L380-.L71
	.byte	3,1,0,1,1
.L947:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.debug_ranges'
.L379:
	.word	-1,.L71,0,.L380-.L71,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.debug_info'
.L381:
	.word	252
	.half	3
	.word	.L382
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L384,.L383
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate',0,1,136,2,6,1,1,1
	.word	.L73,.L741,.L72
	.byte	4
	.word	.L73,.L741
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.debug_abbrev'
.L382:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.debug_line'
.L383:
	.word	.L951-.L950
.L950:
	.half	3
	.word	.L953-.L952
.L952:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L953:
	.byte	5,3,7,0,5,2
	.word	.L73
	.byte	3,137,2,1,5,1,7,9
	.half	.L385-.L73
	.byte	3,1,0,1,1
.L951:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.debug_ranges'
.L384:
	.word	-1,.L73,0,.L385-.L73,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Can_17_MCanP_CanDisInt')
	.sect	'.debug_info'
.L386:
	.word	243
	.half	3
	.word	.L387
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L389,.L388
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Can_17_MCanP_CanDisInt',0,1,143,2,6,1,1,1
	.word	.L75,.L742,.L74
	.byte	4
	.word	.L75,.L742
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Can_17_MCanP_CanDisInt')
	.sect	'.debug_abbrev'
.L387:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Can_17_MCanP_CanDisInt')
	.sect	'.debug_line'
.L388:
	.word	.L955-.L954
.L954:
	.half	3
	.word	.L957-.L956
.L956:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L957:
	.byte	5,3,7,0,5,2
	.word	.L75
	.byte	3,144,2,1,5,1,7,9
	.half	.L390-.L75
	.byte	3,1,0,1,1
.L955:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Can_17_MCanP_CanDisInt')
	.sect	'.debug_ranges'
.L389:
	.word	-1,.L75,0,.L390-.L75,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Can_17_MCanP_CanDisInt')
	.sect	'.debug_info'
.L391:
	.word	242
	.half	3
	.word	.L392
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L394,.L393
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Can_17_MCanP_CanDisInt',0,1,148,2,6,1,1,1
	.word	.L77,.L743,.L76
	.byte	4
	.word	.L77,.L743
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Can_17_MCanP_CanDisInt')
	.sect	'.debug_abbrev'
.L392:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Can_17_MCanP_CanDisInt')
	.sect	'.debug_line'
.L393:
	.word	.L959-.L958
.L958:
	.half	3
	.word	.L961-.L960
.L960:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L961:
	.byte	5,3,7,0,5,2
	.word	.L77
	.byte	3,149,2,1,5,1,7,9
	.half	.L395-.L77
	.byte	3,1,0,1,1
.L959:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Can_17_MCanP_CanDisInt')
	.sect	'.debug_ranges'
.L394:
	.word	-1,.L77,0,.L395-.L77,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Can_17_MCanP_CanEnInt')
	.sect	'.debug_info'
.L396:
	.word	242
	.half	3
	.word	.L397
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L399,.L398
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Can_17_MCanP_CanEnInt',0,1,153,2,6,1,1,1
	.word	.L79,.L744,.L78
	.byte	4
	.word	.L79,.L744
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Can_17_MCanP_CanEnInt')
	.sect	'.debug_abbrev'
.L397:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Can_17_MCanP_CanEnInt')
	.sect	'.debug_line'
.L398:
	.word	.L963-.L962
.L962:
	.half	3
	.word	.L965-.L964
.L964:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L965:
	.byte	5,3,7,0,5,2
	.word	.L79
	.byte	3,154,2,1,5,1,7,9
	.half	.L400-.L79
	.byte	3,1,0,1,1
.L963:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Can_17_MCanP_CanEnInt')
	.sect	'.debug_ranges'
.L399:
	.word	-1,.L79,0,.L400-.L79,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Can_17_MCanP_CanEnInt')
	.sect	'.debug_info'
.L401:
	.word	241
	.half	3
	.word	.L402
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L404,.L403
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Can_17_MCanP_CanEnInt',0,1,158,2,6,1,1,1
	.word	.L81,.L745,.L80
	.byte	4
	.word	.L81,.L745
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Can_17_MCanP_CanEnInt')
	.sect	'.debug_abbrev'
.L402:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Can_17_MCanP_CanEnInt')
	.sect	'.debug_line'
.L403:
	.word	.L967-.L966
.L966:
	.half	3
	.word	.L969-.L968
.L968:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L969:
	.byte	5,3,7,0,5,2
	.word	.L81
	.byte	3,159,2,1,5,1,7,9
	.half	.L405-.L81
	.byte	3,1,0,1,1
.L967:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Can_17_MCanP_CanEnInt')
	.sect	'.debug_ranges'
.L404:
	.word	-1,.L81,0,.L405-.L81,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Can_17_MCanP_CanWrMO')
	.sect	'.debug_info'
.L406:
	.word	241
	.half	3
	.word	.L407
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L409,.L408
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Can_17_MCanP_CanWrMO',0,1,163,2,6,1,1,1
	.word	.L83,.L746,.L82
	.byte	4
	.word	.L83,.L746
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Can_17_MCanP_CanWrMO')
	.sect	'.debug_abbrev'
.L407:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Can_17_MCanP_CanWrMO')
	.sect	'.debug_line'
.L408:
	.word	.L971-.L970
.L970:
	.half	3
	.word	.L973-.L972
.L972:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L973:
	.byte	5,3,7,0,5,2
	.word	.L83
	.byte	3,164,2,1,5,1,7,9
	.half	.L410-.L83
	.byte	3,1,0,1,1
.L971:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Can_17_MCanP_CanWrMO')
	.sect	'.debug_ranges'
.L409:
	.word	-1,.L83,0,.L410-.L83,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Can_17_MCanP_CanWrMO')
	.sect	'.debug_info'
.L411:
	.word	240
	.half	3
	.word	.L412
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L414,.L413
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Can_17_MCanP_CanWrMO',0,1,168,2,6,1,1,1
	.word	.L85,.L747,.L84
	.byte	4
	.word	.L85,.L747
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Can_17_MCanP_CanWrMO')
	.sect	'.debug_abbrev'
.L412:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Can_17_MCanP_CanWrMO')
	.sect	'.debug_line'
.L413:
	.word	.L975-.L974
.L974:
	.half	3
	.word	.L977-.L976
.L976:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L977:
	.byte	5,3,7,0,5,2
	.word	.L85
	.byte	3,169,2,1,5,1,7,9
	.half	.L415-.L85
	.byte	3,1,0,1,1
.L975:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Can_17_MCanP_CanWrMO')
	.sect	'.debug_ranges'
.L414:
	.word	-1,.L85,0,.L415-.L85,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Fr_17_Eray_ControllerInit')
	.sect	'.debug_info'
.L416:
	.word	246
	.half	3
	.word	.L417
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L419,.L418
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Fr_17_Eray_ControllerInit',0,1,175,2,6,1,1,1
	.word	.L87,.L748,.L86
	.byte	4
	.word	.L87,.L748
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Fr_17_Eray_ControllerInit')
	.sect	'.debug_abbrev'
.L417:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Fr_17_Eray_ControllerInit')
	.sect	'.debug_line'
.L418:
	.word	.L979-.L978
.L978:
	.half	3
	.word	.L981-.L980
.L980:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L981:
	.byte	5,3,7,0,5,2
	.word	.L87
	.byte	3,176,2,1,5,1,7,9
	.half	.L420-.L87
	.byte	3,1,0,1,1
.L979:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Fr_17_Eray_ControllerInit')
	.sect	'.debug_ranges'
.L419:
	.word	-1,.L87,0,.L420-.L87,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Fr_17_Eray_ControllerInit')
	.sect	'.debug_info'
.L421:
	.word	245
	.half	3
	.word	.L422
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L424,.L423
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Fr_17_Eray_ControllerInit',0,1,180,2,6,1,1,1
	.word	.L89,.L749,.L88
	.byte	4
	.word	.L89,.L749
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Fr_17_Eray_ControllerInit')
	.sect	'.debug_abbrev'
.L422:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Fr_17_Eray_ControllerInit')
	.sect	'.debug_line'
.L423:
	.word	.L983-.L982
.L982:
	.half	3
	.word	.L985-.L984
.L984:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L985:
	.byte	5,3,7,0,5,2
	.word	.L89
	.byte	3,181,2,1,5,1,7,9
	.half	.L425-.L89
	.byte	3,1,0,1,1
.L983:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Fr_17_Eray_ControllerInit')
	.sect	'.debug_ranges'
.L424:
	.word	-1,.L89,0,.L425-.L89,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Fr_17_Eray_SetWakeupChannel')
	.sect	'.debug_info'
.L426:
	.word	248
	.half	3
	.word	.L427
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L429,.L428
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Fr_17_Eray_SetWakeupChannel',0,1,185,2,6,1,1,1
	.word	.L91,.L750,.L90
	.byte	4
	.word	.L91,.L750
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Fr_17_Eray_SetWakeupChannel')
	.sect	'.debug_abbrev'
.L427:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Fr_17_Eray_SetWakeupChannel')
	.sect	'.debug_line'
.L428:
	.word	.L987-.L986
.L986:
	.half	3
	.word	.L989-.L988
.L988:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L989:
	.byte	5,3,7,0,5,2
	.word	.L91
	.byte	3,186,2,1,5,1,7,9
	.half	.L430-.L91
	.byte	3,1,0,1,1
.L987:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Fr_17_Eray_SetWakeupChannel')
	.sect	'.debug_ranges'
.L429:
	.word	-1,.L91,0,.L430-.L91,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Fr_17_Eray_SetWakeupChannel')
	.sect	'.debug_info'
.L431:
	.word	247
	.half	3
	.word	.L432
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L434,.L433
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Fr_17_Eray_SetWakeupChannel',0,1,190,2,6,1,1,1
	.word	.L93,.L751,.L92
	.byte	4
	.word	.L93,.L751
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Fr_17_Eray_SetWakeupChannel')
	.sect	'.debug_abbrev'
.L432:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Fr_17_Eray_SetWakeupChannel')
	.sect	'.debug_line'
.L433:
	.word	.L991-.L990
.L990:
	.half	3
	.word	.L993-.L992
.L992:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L993:
	.byte	5,3,7,0,5,2
	.word	.L93
	.byte	3,191,2,1,5,1,7,9
	.half	.L435-.L93
	.byte	3,1,0,1,1
.L991:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Fr_17_Eray_SetWakeupChannel')
	.sect	'.debug_ranges'
.L434:
	.word	-1,.L93,0,.L435-.L93,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Spi_WriteIB')
	.sect	'.debug_info'
.L436:
	.word	232
	.half	3
	.word	.L437
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L439,.L438
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Spi_WriteIB',0,1,197,2,6,1,1,1
	.word	.L95,.L752,.L94
	.byte	4
	.word	.L95,.L752
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Spi_WriteIB')
	.sect	'.debug_abbrev'
.L437:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Spi_WriteIB')
	.sect	'.debug_line'
.L438:
	.word	.L995-.L994
.L994:
	.half	3
	.word	.L997-.L996
.L996:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L997:
	.byte	5,3,7,0,5,2
	.word	.L95
	.byte	3,198,2,1,5,1,7,9
	.half	.L440-.L95
	.byte	3,1,0,1,1
.L995:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Spi_WriteIB')
	.sect	'.debug_ranges'
.L439:
	.word	-1,.L95,0,.L440-.L95,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Spi_WriteIB')
	.sect	'.debug_info'
.L441:
	.word	231
	.half	3
	.word	.L442
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L444,.L443
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Spi_WriteIB',0,1,201,2,6,1,1,1
	.word	.L97,.L753,.L96
	.byte	4
	.word	.L97,.L753
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Spi_WriteIB')
	.sect	'.debug_abbrev'
.L442:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Spi_WriteIB')
	.sect	'.debug_line'
.L443:
	.word	.L999-.L998
.L998:
	.half	3
	.word	.L1001-.L1000
.L1000:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1001:
	.byte	5,3,7,0,5,2
	.word	.L97
	.byte	3,202,2,1,5,1,7,9
	.half	.L445-.L97
	.byte	3,1,0,1,1
.L999:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Spi_WriteIB')
	.sect	'.debug_ranges'
.L444:
	.word	-1,.L97,0,.L445-.L97,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Spi_AsyncTransmit')
	.sect	'.debug_info'
.L446:
	.word	238
	.half	3
	.word	.L447
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L449,.L448
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Spi_AsyncTransmit',0,1,206,2,6,1,1,1
	.word	.L99,.L754,.L98
	.byte	4
	.word	.L99,.L754
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Spi_AsyncTransmit')
	.sect	'.debug_abbrev'
.L447:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Spi_AsyncTransmit')
	.sect	'.debug_line'
.L448:
	.word	.L1003-.L1002
.L1002:
	.half	3
	.word	.L1005-.L1004
.L1004:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1005:
	.byte	5,3,7,0,5,2
	.word	.L99
	.byte	3,207,2,1,5,1,7,9
	.half	.L450-.L99
	.byte	3,1,0,1,1
.L1003:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Spi_AsyncTransmit')
	.sect	'.debug_ranges'
.L449:
	.word	-1,.L99,0,.L450-.L99,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Spi_AsyncTransmit')
	.sect	'.debug_info'
.L451:
	.word	237
	.half	3
	.word	.L452
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L454,.L453
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Spi_AsyncTransmit',0,1,210,2,6,1,1,1
	.word	.L101,.L755,.L100
	.byte	4
	.word	.L101,.L755
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Spi_AsyncTransmit')
	.sect	'.debug_abbrev'
.L452:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Spi_AsyncTransmit')
	.sect	'.debug_line'
.L453:
	.word	.L1007-.L1006
.L1006:
	.half	3
	.word	.L1009-.L1008
.L1008:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1009:
	.byte	5,3,7,0,5,2
	.word	.L101
	.byte	3,211,2,1,5,1,7,9
	.half	.L455-.L101
	.byte	3,1,0,1,1
.L1007:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Spi_AsyncTransmit')
	.sect	'.debug_ranges'
.L454:
	.word	-1,.L101,0,.L455-.L101,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Spi_GetSequenceResult')
	.sect	'.debug_info'
.L456:
	.word	242
	.half	3
	.word	.L457
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L459,.L458
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Spi_GetSequenceResult',0,1,215,2,6,1,1,1
	.word	.L103,.L756,.L102
	.byte	4
	.word	.L103,.L756
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Spi_GetSequenceResult')
	.sect	'.debug_abbrev'
.L457:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Spi_GetSequenceResult')
	.sect	'.debug_line'
.L458:
	.word	.L1011-.L1010
.L1010:
	.half	3
	.word	.L1013-.L1012
.L1012:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1013:
	.byte	5,3,7,0,5,2
	.word	.L103
	.byte	3,216,2,1,5,1,7,9
	.half	.L460-.L103
	.byte	3,1,0,1,1
.L1011:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Spi_GetSequenceResult')
	.sect	'.debug_ranges'
.L459:
	.word	-1,.L103,0,.L460-.L103,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Spi_GetSequenceResult')
	.sect	'.debug_info'
.L461:
	.word	241
	.half	3
	.word	.L462
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L464,.L463
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Spi_GetSequenceResult',0,1,219,2,6,1,1,1
	.word	.L105,.L757,.L104
	.byte	4
	.word	.L105,.L757
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Spi_GetSequenceResult')
	.sect	'.debug_abbrev'
.L462:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Spi_GetSequenceResult')
	.sect	'.debug_line'
.L463:
	.word	.L1015-.L1014
.L1014:
	.half	3
	.word	.L1017-.L1016
.L1016:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1017:
	.byte	5,3,7,0,5,2
	.word	.L105
	.byte	3,220,2,1,5,1,7,9
	.half	.L465-.L105
	.byte	3,1,0,1,1
.L1015:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Spi_GetSequenceResult')
	.sect	'.debug_ranges'
.L464:
	.word	-1,.L105,0,.L465-.L105,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Spi_Cancel')
	.sect	'.debug_info'
.L466:
	.word	231
	.half	3
	.word	.L467
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L469,.L468
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Spi_Cancel',0,1,223,2,6,1,1,1
	.word	.L107,.L758,.L106
	.byte	4
	.word	.L107,.L758
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Spi_Cancel')
	.sect	'.debug_abbrev'
.L467:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Spi_Cancel')
	.sect	'.debug_line'
.L468:
	.word	.L1019-.L1018
.L1018:
	.half	3
	.word	.L1021-.L1020
.L1020:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1021:
	.byte	5,3,7,0,5,2
	.word	.L107
	.byte	3,224,2,1,5,1,7,9
	.half	.L470-.L107
	.byte	3,1,0,1,1
.L1019:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Spi_Cancel')
	.sect	'.debug_ranges'
.L469:
	.word	-1,.L107,0,.L470-.L107,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Spi_Cancel')
	.sect	'.debug_info'
.L471:
	.word	230
	.half	3
	.word	.L472
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L474,.L473
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Spi_Cancel',0,1,227,2,6,1,1,1
	.word	.L109,.L759,.L108
	.byte	4
	.word	.L109,.L759
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Spi_Cancel')
	.sect	'.debug_abbrev'
.L472:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Spi_Cancel')
	.sect	'.debug_line'
.L473:
	.word	.L1023-.L1022
.L1022:
	.half	3
	.word	.L1025-.L1024
.L1024:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1025:
	.byte	5,3,7,0,5,2
	.word	.L109
	.byte	3,228,2,1,5,1,7,9
	.half	.L475-.L109
	.byte	3,1,0,1,1
.L1023:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Spi_Cancel')
	.sect	'.debug_ranges'
.L474:
	.word	-1,.L109,0,.L475-.L109,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Spi_Init')
	.sect	'.debug_info'
.L476:
	.word	229
	.half	3
	.word	.L477
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L479,.L478
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Spi_Init',0,1,231,2,6,1,1,1
	.word	.L111,.L760,.L110
	.byte	4
	.word	.L111,.L760
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Spi_Init')
	.sect	'.debug_abbrev'
.L477:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Spi_Init')
	.sect	'.debug_line'
.L478:
	.word	.L1027-.L1026
.L1026:
	.half	3
	.word	.L1029-.L1028
.L1028:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1029:
	.byte	5,3,7,0,5,2
	.word	.L111
	.byte	3,232,2,1,5,1,7,9
	.half	.L480-.L111
	.byte	3,1,0,1,1
.L1027:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Spi_Init')
	.sect	'.debug_ranges'
.L479:
	.word	-1,.L111,0,.L480-.L111,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Spi_Init')
	.sect	'.debug_info'
.L481:
	.word	228
	.half	3
	.word	.L482
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L484,.L483
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Spi_Init',0,1,235,2,6,1,1,1
	.word	.L113,.L761,.L112
	.byte	4
	.word	.L113,.L761
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Spi_Init')
	.sect	'.debug_abbrev'
.L482:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Spi_Init')
	.sect	'.debug_line'
.L483:
	.word	.L1031-.L1030
.L1030:
	.half	3
	.word	.L1033-.L1032
.L1032:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1033:
	.byte	5,3,7,0,5,2
	.word	.L113
	.byte	3,236,2,1,5,1,7,9
	.half	.L485-.L113
	.byte	3,1,0,1,1
.L1031:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Spi_Init')
	.sect	'.debug_ranges'
.L484:
	.word	-1,.L113,0,.L485-.L113,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Spi_DeInit')
	.sect	'.debug_info'
.L486:
	.word	231
	.half	3
	.word	.L487
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L489,.L488
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Spi_DeInit',0,1,239,2,6,1,1,1
	.word	.L115,.L762,.L114
	.byte	4
	.word	.L115,.L762
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Spi_DeInit')
	.sect	'.debug_abbrev'
.L487:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Spi_DeInit')
	.sect	'.debug_line'
.L488:
	.word	.L1035-.L1034
.L1034:
	.half	3
	.word	.L1037-.L1036
.L1036:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1037:
	.byte	5,3,7,0,5,2
	.word	.L115
	.byte	3,240,2,1,5,1,7,9
	.half	.L490-.L115
	.byte	3,1,0,1,1
.L1035:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Spi_DeInit')
	.sect	'.debug_ranges'
.L489:
	.word	-1,.L115,0,.L490-.L115,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Spi_DeInit')
	.sect	'.debug_info'
.L491:
	.word	230
	.half	3
	.word	.L492
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L494,.L493
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Spi_DeInit',0,1,243,2,6,1,1,1
	.word	.L117,.L763,.L116
	.byte	4
	.word	.L117,.L763
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Spi_DeInit')
	.sect	'.debug_abbrev'
.L492:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Spi_DeInit')
	.sect	'.debug_line'
.L493:
	.word	.L1039-.L1038
.L1038:
	.half	3
	.word	.L1041-.L1040
.L1040:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1041:
	.byte	5,3,7,0,5,2
	.word	.L117
	.byte	3,244,2,1,5,1,7,9
	.half	.L495-.L117
	.byte	3,1,0,1,1
.L1039:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Spi_DeInit')
	.sect	'.debug_ranges'
.L494:
	.word	-1,.L117,0,.L495-.L117,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Spi_SyncTransmit')
	.sect	'.debug_info'
.L496:
	.word	237
	.half	3
	.word	.L497
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L499,.L498
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Spi_SyncTransmit',0,1,247,2,6,1,1,1
	.word	.L119,.L764,.L118
	.byte	4
	.word	.L119,.L764
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Spi_SyncTransmit')
	.sect	'.debug_abbrev'
.L497:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Spi_SyncTransmit')
	.sect	'.debug_line'
.L498:
	.word	.L1043-.L1042
.L1042:
	.half	3
	.word	.L1045-.L1044
.L1044:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1045:
	.byte	5,3,7,0,5,2
	.word	.L119
	.byte	3,248,2,1,5,1,7,9
	.half	.L500-.L119
	.byte	3,1,0,1,1
.L1043:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Spi_SyncTransmit')
	.sect	'.debug_ranges'
.L499:
	.word	-1,.L119,0,.L500-.L119,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Spi_SyncTransmit')
	.sect	'.debug_info'
.L501:
	.word	236
	.half	3
	.word	.L502
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L504,.L503
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Spi_SyncTransmit',0,1,251,2,6,1,1,1
	.word	.L121,.L765,.L120
	.byte	4
	.word	.L121,.L765
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Spi_SyncTransmit')
	.sect	'.debug_abbrev'
.L502:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Spi_SyncTransmit')
	.sect	'.debug_line'
.L503:
	.word	.L1047-.L1046
.L1046:
	.half	3
	.word	.L1049-.L1048
.L1048:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1049:
	.byte	5,3,7,0,5,2
	.word	.L121
	.byte	3,252,2,1,5,1,7,9
	.half	.L505-.L121
	.byte	3,1,0,1,1
.L1047:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Spi_SyncTransmit')
	.sect	'.debug_ranges'
.L504:
	.word	-1,.L121,0,.L505-.L121,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Wdg_17_Scu_TimerHandling')
	.sect	'.debug_info'
.L506:
	.word	245
	.half	3
	.word	.L507
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L509,.L508
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Wdg_17_Scu_TimerHandling',0,1,132,3,6,1,1,1
	.word	.L123,.L766,.L122
	.byte	4
	.word	.L123,.L766
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Wdg_17_Scu_TimerHandling')
	.sect	'.debug_abbrev'
.L507:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Wdg_17_Scu_TimerHandling')
	.sect	'.debug_line'
.L508:
	.word	.L1051-.L1050
.L1050:
	.half	3
	.word	.L1053-.L1052
.L1052:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1053:
	.byte	5,3,7,0,5,2
	.word	.L123
	.byte	3,133,3,1,5,1,7,9
	.half	.L510-.L123
	.byte	3,1,0,1,1
.L1051:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Wdg_17_Scu_TimerHandling')
	.sect	'.debug_ranges'
.L509:
	.word	-1,.L123,0,.L510-.L123,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Wdg_17_Scu_Trigger')
	.sect	'.debug_info'
.L511:
	.word	239
	.half	3
	.word	.L512
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L514,.L513
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Wdg_17_Scu_Trigger',0,1,136,3,6,1,1,1
	.word	.L125,.L767,.L124
	.byte	4
	.word	.L125,.L767
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Wdg_17_Scu_Trigger')
	.sect	'.debug_abbrev'
.L512:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Wdg_17_Scu_Trigger')
	.sect	'.debug_line'
.L513:
	.word	.L1055-.L1054
.L1054:
	.half	3
	.word	.L1057-.L1056
.L1056:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1057:
	.byte	5,3,7,0,5,2
	.word	.L125
	.byte	3,137,3,1,5,1,7,9
	.half	.L515-.L125
	.byte	3,1,0,1,1
.L1055:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Wdg_17_Scu_Trigger')
	.sect	'.debug_ranges'
.L514:
	.word	-1,.L125,0,.L515-.L125,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Wdg_17_Scu_ChangeMode')
	.sect	'.debug_info'
.L516:
	.word	242
	.half	3
	.word	.L517
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L519,.L518
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Wdg_17_Scu_ChangeMode',0,1,140,3,6,1,1,1
	.word	.L127,.L768,.L126
	.byte	4
	.word	.L127,.L768
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Wdg_17_Scu_ChangeMode')
	.sect	'.debug_abbrev'
.L517:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Wdg_17_Scu_ChangeMode')
	.sect	'.debug_line'
.L518:
	.word	.L1059-.L1058
.L1058:
	.half	3
	.word	.L1061-.L1060
.L1060:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1061:
	.byte	5,3,7,0,5,2
	.word	.L127
	.byte	3,141,3,1,5,1,7,9
	.half	.L520-.L127
	.byte	3,1,0,1,1
.L1059:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Wdg_17_Scu_ChangeMode')
	.sect	'.debug_ranges'
.L519:
	.word	-1,.L127,0,.L520-.L127,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyInit')
	.sect	'.debug_info'
.L521:
	.word	242
	.half	3
	.word	.L522
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L524,.L523
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Wdg_17_Scu_SafetyInit',0,1,144,3,6,1,1,1
	.word	.L129,.L769,.L128
	.byte	4
	.word	.L129,.L769
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyInit')
	.sect	'.debug_abbrev'
.L522:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyInit')
	.sect	'.debug_line'
.L523:
	.word	.L1063-.L1062
.L1062:
	.half	3
	.word	.L1065-.L1064
.L1064:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1065:
	.byte	5,3,7,0,5,2
	.word	.L129
	.byte	3,145,3,1,5,1,7,9
	.half	.L525-.L129
	.byte	3,1,0,1,1
.L1063:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyInit')
	.sect	'.debug_ranges'
.L524:
	.word	-1,.L129,0,.L525-.L129,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyTrigger')
	.sect	'.debug_info'
.L526:
	.word	245
	.half	3
	.word	.L527
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L529,.L528
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Wdg_17_Scu_SafetyTrigger',0,1,148,3,6,1,1,1
	.word	.L131,.L770,.L130
	.byte	4
	.word	.L131,.L770
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyTrigger')
	.sect	'.debug_abbrev'
.L527:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyTrigger')
	.sect	'.debug_line'
.L528:
	.word	.L1067-.L1066
.L1066:
	.half	3
	.word	.L1069-.L1068
.L1068:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1069:
	.byte	5,3,7,0,5,2
	.word	.L131
	.byte	3,149,3,1,5,1,7,9
	.half	.L530-.L131
	.byte	3,1,0,1,1
.L1067:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyTrigger')
	.sect	'.debug_ranges'
.L529:
	.word	-1,.L131,0,.L530-.L131,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyOffMode')
	.sect	'.debug_info'
.L531:
	.word	245
	.half	3
	.word	.L532
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L534,.L533
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Wdg_17_Scu_SafetyOffMode',0,1,152,3,6,1,1,1
	.word	.L133,.L771,.L132
	.byte	4
	.word	.L133,.L771
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyOffMode')
	.sect	'.debug_abbrev'
.L532:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyOffMode')
	.sect	'.debug_line'
.L533:
	.word	.L1071-.L1070
.L1070:
	.half	3
	.word	.L1073-.L1072
.L1072:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1073:
	.byte	5,3,7,0,5,2
	.word	.L133
	.byte	3,153,3,1,5,1,7,9
	.half	.L535-.L133
	.byte	3,1,0,1,1
.L1071:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyOffMode')
	.sect	'.debug_ranges'
.L534:
	.word	-1,.L133,0,.L535-.L133,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Wdg_17_Scu_TimerHandling')
	.sect	'.debug_info'
.L536:
	.word	244
	.half	3
	.word	.L537
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L539,.L538
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Wdg_17_Scu_TimerHandling',0,1,157,3,6,1,1,1
	.word	.L135,.L772,.L134
	.byte	4
	.word	.L135,.L772
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Wdg_17_Scu_TimerHandling')
	.sect	'.debug_abbrev'
.L537:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Wdg_17_Scu_TimerHandling')
	.sect	'.debug_line'
.L538:
	.word	.L1075-.L1074
.L1074:
	.half	3
	.word	.L1077-.L1076
.L1076:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1077:
	.byte	5,3,7,0,5,2
	.word	.L135
	.byte	3,158,3,1,5,1,7,9
	.half	.L540-.L135
	.byte	3,1,0,1,1
.L1075:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Wdg_17_Scu_TimerHandling')
	.sect	'.debug_ranges'
.L539:
	.word	-1,.L135,0,.L540-.L135,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Wdg_17_Scu_Trigger')
	.sect	'.debug_info'
.L541:
	.word	238
	.half	3
	.word	.L542
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L544,.L543
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Wdg_17_Scu_Trigger',0,1,161,3,6,1,1,1
	.word	.L137,.L773,.L136
	.byte	4
	.word	.L137,.L773
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Wdg_17_Scu_Trigger')
	.sect	'.debug_abbrev'
.L542:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Wdg_17_Scu_Trigger')
	.sect	'.debug_line'
.L543:
	.word	.L1079-.L1078
.L1078:
	.half	3
	.word	.L1081-.L1080
.L1080:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1081:
	.byte	5,3,7,0,5,2
	.word	.L137
	.byte	3,162,3,1,5,1,7,9
	.half	.L545-.L137
	.byte	3,1,0,1,1
.L1079:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Wdg_17_Scu_Trigger')
	.sect	'.debug_ranges'
.L544:
	.word	-1,.L137,0,.L545-.L137,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Wdg_17_Scu_ChangeMode')
	.sect	'.debug_info'
.L546:
	.word	241
	.half	3
	.word	.L547
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L549,.L548
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Wdg_17_Scu_ChangeMode',0,1,165,3,6,1,1,1
	.word	.L139,.L774,.L138
	.byte	4
	.word	.L139,.L774
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Wdg_17_Scu_ChangeMode')
	.sect	'.debug_abbrev'
.L547:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Wdg_17_Scu_ChangeMode')
	.sect	'.debug_line'
.L548:
	.word	.L1083-.L1082
.L1082:
	.half	3
	.word	.L1085-.L1084
.L1084:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1085:
	.byte	5,3,7,0,5,2
	.word	.L139
	.byte	3,166,3,1,5,1,7,9
	.half	.L550-.L139
	.byte	3,1,0,1,1
.L1083:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Wdg_17_Scu_ChangeMode')
	.sect	'.debug_ranges'
.L549:
	.word	-1,.L139,0,.L550-.L139,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyInit')
	.sect	'.debug_info'
.L551:
	.word	241
	.half	3
	.word	.L552
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L554,.L553
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Wdg_17_Scu_SafetyInit',0,1,169,3,6,1,1,1
	.word	.L141,.L775,.L140
	.byte	4
	.word	.L141,.L775
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyInit')
	.sect	'.debug_abbrev'
.L552:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyInit')
	.sect	'.debug_line'
.L553:
	.word	.L1087-.L1086
.L1086:
	.half	3
	.word	.L1089-.L1088
.L1088:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1089:
	.byte	5,3,7,0,5,2
	.word	.L141
	.byte	3,170,3,1,5,1,7,9
	.half	.L555-.L141
	.byte	3,1,0,1,1
.L1087:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyInit')
	.sect	'.debug_ranges'
.L554:
	.word	-1,.L141,0,.L555-.L141,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyTrigger')
	.sect	'.debug_info'
.L556:
	.word	244
	.half	3
	.word	.L557
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L559,.L558
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Wdg_17_Scu_SafetyTrigger',0,1,173,3,6,1,1,1
	.word	.L143,.L776,.L142
	.byte	4
	.word	.L143,.L776
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyTrigger')
	.sect	'.debug_abbrev'
.L557:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyTrigger')
	.sect	'.debug_line'
.L558:
	.word	.L1091-.L1090
.L1090:
	.half	3
	.word	.L1093-.L1092
.L1092:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1093:
	.byte	5,3,7,0,5,2
	.word	.L143
	.byte	3,174,3,1,5,1,7,9
	.half	.L560-.L143
	.byte	3,1,0,1,1
.L1091:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyTrigger')
	.sect	'.debug_ranges'
.L559:
	.word	-1,.L143,0,.L560-.L143,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyOffMode')
	.sect	'.debug_info'
.L561:
	.word	244
	.half	3
	.word	.L562
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L564,.L563
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Wdg_17_Scu_SafetyOffMode',0,1,177,3,6,1,1,1
	.word	.L145,.L777,.L144
	.byte	4
	.word	.L145,.L777
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyOffMode')
	.sect	'.debug_abbrev'
.L562:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyOffMode')
	.sect	'.debug_line'
.L563:
	.word	.L1095-.L1094
.L1094:
	.half	3
	.word	.L1097-.L1096
.L1096:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1097:
	.byte	5,3,7,0,5,2
	.word	.L145
	.byte	3,178,3,1,5,1,7,9
	.half	.L565-.L145
	.byte	3,1,0,1,1
.L1095:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyOffMode')
	.sect	'.debug_ranges'
.L564:
	.word	-1,.L145,0,.L565-.L145,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Fls_17_Pmu_Init')
	.sect	'.debug_info'
.L566:
	.word	236
	.half	3
	.word	.L567
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L569,.L568
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Fls_17_Pmu_Init',0,1,185,3,6,1,1,1
	.word	.L147,.L778,.L146
	.byte	4
	.word	.L147,.L778
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Fls_17_Pmu_Init')
	.sect	'.debug_abbrev'
.L567:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Fls_17_Pmu_Init')
	.sect	'.debug_line'
.L568:
	.word	.L1099-.L1098
.L1098:
	.half	3
	.word	.L1101-.L1100
.L1100:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1101:
	.byte	5,3,7,0,5,2
	.word	.L147
	.byte	3,186,3,1,5,1,7,9
	.half	.L570-.L147
	.byte	3,1,0,1,1
.L1099:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Fls_17_Pmu_Init')
	.sect	'.debug_ranges'
.L569:
	.word	-1,.L147,0,.L570-.L147,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Fls_17_Pmu_Init')
	.sect	'.debug_info'
.L571:
	.word	235
	.half	3
	.word	.L572
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L574,.L573
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Fls_17_Pmu_Init',0,1,190,3,6,1,1,1
	.word	.L149,.L779,.L148
	.byte	4
	.word	.L149,.L779
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Fls_17_Pmu_Init')
	.sect	'.debug_abbrev'
.L572:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Fls_17_Pmu_Init')
	.sect	'.debug_line'
.L573:
	.word	.L1103-.L1102
.L1102:
	.half	3
	.word	.L1105-.L1104
.L1104:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1105:
	.byte	5,3,7,0,5,2
	.word	.L149
	.byte	3,191,3,1,5,1,7,9
	.half	.L575-.L149
	.byte	3,1,0,1,1
.L1103:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Fls_17_Pmu_Init')
	.sect	'.debug_ranges'
.L574:
	.word	-1,.L149,0,.L575-.L149,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Fls_17_Pmu_Erase')
	.sect	'.debug_info'
.L576:
	.word	237
	.half	3
	.word	.L577
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L579,.L578
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Fls_17_Pmu_Erase',0,1,195,3,6,1,1,1
	.word	.L151,.L780,.L150
	.byte	4
	.word	.L151,.L780
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Fls_17_Pmu_Erase')
	.sect	'.debug_abbrev'
.L577:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Fls_17_Pmu_Erase')
	.sect	'.debug_line'
.L578:
	.word	.L1107-.L1106
.L1106:
	.half	3
	.word	.L1109-.L1108
.L1108:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1109:
	.byte	5,3,7,0,5,2
	.word	.L151
	.byte	3,196,3,1,5,1,7,9
	.half	.L580-.L151
	.byte	3,1,0,1,1
.L1107:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Fls_17_Pmu_Erase')
	.sect	'.debug_ranges'
.L579:
	.word	-1,.L151,0,.L580-.L151,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Fls_17_Pmu_Erase')
	.sect	'.debug_info'
.L581:
	.word	236
	.half	3
	.word	.L582
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L584,.L583
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Fls_17_Pmu_Erase',0,1,200,3,6,1,1,1
	.word	.L153,.L781,.L152
	.byte	4
	.word	.L153,.L781
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Fls_17_Pmu_Erase')
	.sect	'.debug_abbrev'
.L582:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Fls_17_Pmu_Erase')
	.sect	'.debug_line'
.L583:
	.word	.L1111-.L1110
.L1110:
	.half	3
	.word	.L1113-.L1112
.L1112:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1113:
	.byte	5,3,7,0,5,2
	.word	.L153
	.byte	3,201,3,1,5,1,7,9
	.half	.L585-.L153
	.byte	3,1,0,1,1
.L1111:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Fls_17_Pmu_Erase')
	.sect	'.debug_ranges'
.L584:
	.word	-1,.L153,0,.L585-.L153,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Fls_17_Pmu_Write')
	.sect	'.debug_info'
.L586:
	.word	237
	.half	3
	.word	.L587
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L589,.L588
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Fls_17_Pmu_Write',0,1,205,3,6,1,1,1
	.word	.L155,.L782,.L154
	.byte	4
	.word	.L155,.L782
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Fls_17_Pmu_Write')
	.sect	'.debug_abbrev'
.L587:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Fls_17_Pmu_Write')
	.sect	'.debug_line'
.L588:
	.word	.L1115-.L1114
.L1114:
	.half	3
	.word	.L1117-.L1116
.L1116:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1117:
	.byte	5,3,7,0,5,2
	.word	.L155
	.byte	3,206,3,1,5,1,7,9
	.half	.L590-.L155
	.byte	3,1,0,1,1
.L1115:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Fls_17_Pmu_Write')
	.sect	'.debug_ranges'
.L589:
	.word	-1,.L155,0,.L590-.L155,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Fls_17_Pmu_Write')
	.sect	'.debug_info'
.L591:
	.word	236
	.half	3
	.word	.L592
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L594,.L593
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Fls_17_Pmu_Write',0,1,210,3,6,1,1,1
	.word	.L157,.L783,.L156
	.byte	4
	.word	.L157,.L783
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Fls_17_Pmu_Write')
	.sect	'.debug_abbrev'
.L592:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Fls_17_Pmu_Write')
	.sect	'.debug_line'
.L593:
	.word	.L1119-.L1118
.L1118:
	.half	3
	.word	.L1121-.L1120
.L1120:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1121:
	.byte	5,3,7,0,5,2
	.word	.L157
	.byte	3,211,3,1,5,1,7,9
	.half	.L595-.L157
	.byte	3,1,0,1,1
.L1119:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Fls_17_Pmu_Write')
	.sect	'.debug_ranges'
.L594:
	.word	-1,.L157,0,.L595-.L157,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Fls_17_Pmu_Main')
	.sect	'.debug_info'
.L596:
	.word	236
	.half	3
	.word	.L597
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L599,.L598
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Fls_17_Pmu_Main',0,1,215,3,6,1,1,1
	.word	.L159,.L784,.L158
	.byte	4
	.word	.L159,.L784
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Fls_17_Pmu_Main')
	.sect	'.debug_abbrev'
.L597:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Fls_17_Pmu_Main')
	.sect	'.debug_line'
.L598:
	.word	.L1123-.L1122
.L1122:
	.half	3
	.word	.L1125-.L1124
.L1124:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1125:
	.byte	5,3,7,0,5,2
	.word	.L159
	.byte	3,216,3,1,5,1,7,9
	.half	.L600-.L159
	.byte	3,1,0,1,1
.L1123:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Fls_17_Pmu_Main')
	.sect	'.debug_ranges'
.L599:
	.word	-1,.L159,0,.L600-.L159,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Fls_17_Pmu_Main')
	.sect	'.debug_info'
.L601:
	.word	235
	.half	3
	.word	.L602
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L604,.L603
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Fls_17_Pmu_Main',0,1,220,3,6,1,1,1
	.word	.L161,.L785,.L160
	.byte	4
	.word	.L161,.L785
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Fls_17_Pmu_Main')
	.sect	'.debug_abbrev'
.L602:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Fls_17_Pmu_Main')
	.sect	'.debug_line'
.L603:
	.word	.L1127-.L1126
.L1126:
	.half	3
	.word	.L1129-.L1128
.L1128:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1129:
	.byte	5,3,7,0,5,2
	.word	.L161
	.byte	3,221,3,1,5,1,7,9
	.half	.L605-.L161
	.byte	3,1,0,1,1
.L1127:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Fls_17_Pmu_Main')
	.sect	'.debug_ranges'
.L604:
	.word	-1,.L161,0,.L605-.L161,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Fls_17_Pmu_ResumeErase')
	.sect	'.debug_info'
.L606:
	.word	243
	.half	3
	.word	.L607
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L609,.L608
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Fls_17_Pmu_ResumeErase',0,1,225,3,6,1,1,1
	.word	.L163,.L786,.L162
	.byte	4
	.word	.L163,.L786
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Fls_17_Pmu_ResumeErase')
	.sect	'.debug_abbrev'
.L607:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Fls_17_Pmu_ResumeErase')
	.sect	'.debug_line'
.L608:
	.word	.L1131-.L1130
.L1130:
	.half	3
	.word	.L1133-.L1132
.L1132:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1133:
	.byte	5,3,7,0,5,2
	.word	.L163
	.byte	3,226,3,1,5,1,7,9
	.half	.L610-.L163
	.byte	3,1,0,1,1
.L1131:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Fls_17_Pmu_ResumeErase')
	.sect	'.debug_ranges'
.L609:
	.word	-1,.L163,0,.L610-.L163,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Fls_17_Pmu_ResumeErase')
	.sect	'.debug_info'
.L611:
	.word	242
	.half	3
	.word	.L612
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L614,.L613
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Fls_17_Pmu_ResumeErase',0,1,230,3,6,1,1,1
	.word	.L165,.L787,.L164
	.byte	4
	.word	.L165,.L787
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Fls_17_Pmu_ResumeErase')
	.sect	'.debug_abbrev'
.L612:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Fls_17_Pmu_ResumeErase')
	.sect	'.debug_line'
.L613:
	.word	.L1135-.L1134
.L1134:
	.half	3
	.word	.L1137-.L1136
.L1136:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1137:
	.byte	5,3,7,0,5,2
	.word	.L165
	.byte	3,231,3,1,5,1,7,9
	.half	.L615-.L165
	.byte	3,1,0,1,1
.L1135:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Fls_17_Pmu_ResumeErase')
	.sect	'.debug_ranges'
.L614:
	.word	-1,.L165,0,.L615-.L165,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_FlsLoader_Erase')
	.sect	'.debug_info'
.L616:
	.word	236
	.half	3
	.word	.L617
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L619,.L618
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_FlsLoader_Erase',0,1,238,3,6,1,1,1
	.word	.L167,.L788,.L166
	.byte	4
	.word	.L167,.L788
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_FlsLoader_Erase')
	.sect	'.debug_abbrev'
.L617:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_FlsLoader_Erase')
	.sect	'.debug_line'
.L618:
	.word	.L1139-.L1138
.L1138:
	.half	3
	.word	.L1141-.L1140
.L1140:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1141:
	.byte	5,3,7,0,5,2
	.word	.L167
	.byte	3,239,3,1,5,1,7,9
	.half	.L620-.L167
	.byte	3,1,0,1,1
.L1139:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_FlsLoader_Erase')
	.sect	'.debug_ranges'
.L619:
	.word	-1,.L167,0,.L620-.L167,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_FlsLoader_Write')
	.sect	'.debug_info'
.L621:
	.word	236
	.half	3
	.word	.L622
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L624,.L623
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_FlsLoader_Write',0,1,243,3,6,1,1,1
	.word	.L169,.L789,.L168
	.byte	4
	.word	.L169,.L789
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_FlsLoader_Write')
	.sect	'.debug_abbrev'
.L622:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_FlsLoader_Write')
	.sect	'.debug_line'
.L623:
	.word	.L1143-.L1142
.L1142:
	.half	3
	.word	.L1145-.L1144
.L1144:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1145:
	.byte	5,3,7,0,5,2
	.word	.L169
	.byte	3,244,3,1,5,1,7,9
	.half	.L625-.L169
	.byte	3,1,0,1,1
.L1143:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_FlsLoader_Write')
	.sect	'.debug_ranges'
.L624:
	.word	-1,.L169,0,.L625-.L169,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_FlsLoader_lLock')
	.sect	'.debug_info'
.L626:
	.word	236
	.half	3
	.word	.L627
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L629,.L628
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_FlsLoader_lLock',0,1,248,3,6,1,1,1
	.word	.L171,.L790,.L170
	.byte	4
	.word	.L171,.L790
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_FlsLoader_lLock')
	.sect	'.debug_abbrev'
.L627:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_FlsLoader_lLock')
	.sect	'.debug_line'
.L628:
	.word	.L1147-.L1146
.L1146:
	.half	3
	.word	.L1149-.L1148
.L1148:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1149:
	.byte	5,3,7,0,5,2
	.word	.L171
	.byte	3,249,3,1,5,1,7,9
	.half	.L630-.L171
	.byte	3,1,0,1,1
.L1147:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_FlsLoader_lLock')
	.sect	'.debug_ranges'
.L629:
	.word	-1,.L171,0,.L630-.L171,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_FlsLoader_lLock')
	.sect	'.debug_info'
.L631:
	.word	235
	.half	3
	.word	.L632
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L634,.L633
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_FlsLoader_lLock',0,1,253,3,6,1,1,1
	.word	.L173,.L791,.L172
	.byte	4
	.word	.L173,.L791
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_FlsLoader_lLock')
	.sect	'.debug_abbrev'
.L632:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_FlsLoader_lLock')
	.sect	'.debug_line'
.L633:
	.word	.L1151-.L1150
.L1150:
	.half	3
	.word	.L1153-.L1152
.L1152:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1153:
	.byte	5,3,7,0,5,2
	.word	.L173
	.byte	3,254,3,1,5,1,7,9
	.half	.L635-.L173
	.byte	3,1,0,1,1
.L1151:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_FlsLoader_lLock')
	.sect	'.debug_ranges'
.L634:
	.word	-1,.L173,0,.L635-.L173,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_FlsLoader_Write')
	.sect	'.debug_info'
.L636:
	.word	235
	.half	3
	.word	.L637
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L639,.L638
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_FlsLoader_Write',0,1,130,4,6,1,1,1
	.word	.L175,.L792,.L174
	.byte	4
	.word	.L175,.L792
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_FlsLoader_Write')
	.sect	'.debug_abbrev'
.L637:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_FlsLoader_Write')
	.sect	'.debug_line'
.L638:
	.word	.L1155-.L1154
.L1154:
	.half	3
	.word	.L1157-.L1156
.L1156:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1157:
	.byte	5,3,7,0,5,2
	.word	.L175
	.byte	3,131,4,1,5,1,7,9
	.half	.L640-.L175
	.byte	3,1,0,1,1
.L1155:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_FlsLoader_Write')
	.sect	'.debug_ranges'
.L639:
	.word	-1,.L175,0,.L640-.L175,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_FlsLoader_Erase')
	.sect	'.debug_info'
.L641:
	.word	235
	.half	3
	.word	.L642
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L644,.L643
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_FlsLoader_Erase',0,1,135,4,6,1,1,1
	.word	.L177,.L793,.L176
	.byte	4
	.word	.L177,.L793
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_FlsLoader_Erase')
	.sect	'.debug_abbrev'
.L642:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_FlsLoader_Erase')
	.sect	'.debug_line'
.L643:
	.word	.L1159-.L1158
.L1158:
	.half	3
	.word	.L1161-.L1160
.L1160:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1161:
	.byte	5,3,7,0,5,2
	.word	.L177
	.byte	3,136,4,1,5,1,7,9
	.half	.L645-.L177
	.byte	3,1,0,1,1
.L1159:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_FlsLoader_Erase')
	.sect	'.debug_ranges'
.L644:
	.word	-1,.L177,0,.L645-.L177,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Eth_17_EthMac_Transmit')
	.sect	'.debug_info'
.L646:
	.word	243
	.half	3
	.word	.L647
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L649,.L648
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Eth_17_EthMac_Transmit',0,1,143,4,6,1,1,1
	.word	.L179,.L794,.L178
	.byte	4
	.word	.L179,.L794
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Eth_17_EthMac_Transmit')
	.sect	'.debug_abbrev'
.L647:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Eth_17_EthMac_Transmit')
	.sect	'.debug_line'
.L648:
	.word	.L1163-.L1162
.L1162:
	.half	3
	.word	.L1165-.L1164
.L1164:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1165:
	.byte	5,3,7,0,5,2
	.word	.L179
	.byte	3,144,4,1,5,1,7,9
	.half	.L650-.L179
	.byte	3,1,0,1,1
.L1163:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Eth_17_EthMac_Transmit')
	.sect	'.debug_ranges'
.L649:
	.word	-1,.L179,0,.L650-.L179,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Eth_17_EthMac_Transmit')
	.sect	'.debug_info'
.L651:
	.word	242
	.half	3
	.word	.L652
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L654,.L653
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Eth_17_EthMac_Transmit',0,1,148,4,6,1,1,1
	.word	.L181,.L795,.L180
	.byte	4
	.word	.L181,.L795
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Eth_17_EthMac_Transmit')
	.sect	'.debug_abbrev'
.L652:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Eth_17_EthMac_Transmit')
	.sect	'.debug_line'
.L653:
	.word	.L1167-.L1166
.L1166:
	.half	3
	.word	.L1169-.L1168
.L1168:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1169:
	.byte	5,3,7,0,5,2
	.word	.L181
	.byte	3,149,4,1,5,1,7,9
	.half	.L655-.L181
	.byte	3,1,0,1,1
.L1167:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Eth_17_EthMac_Transmit')
	.sect	'.debug_ranges'
.L654:
	.word	-1,.L181,0,.L655-.L181,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.debug_info'
.L656:
	.word	250
	.half	3
	.word	.L657
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L659,.L658
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Eth_17_EthMac_ProvideTxBuffer',0,1,153,4,6,1,1,1
	.word	.L183,.L796,.L182
	.byte	4
	.word	.L183,.L796
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.debug_abbrev'
.L657:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.debug_line'
.L658:
	.word	.L1171-.L1170
.L1170:
	.half	3
	.word	.L1173-.L1172
.L1172:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1173:
	.byte	5,3,7,0,5,2
	.word	.L183
	.byte	3,154,4,1,5,1,7,9
	.half	.L660-.L183
	.byte	3,1,0,1,1
.L1171:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.debug_ranges'
.L659:
	.word	-1,.L183,0,.L660-.L183,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.debug_info'
.L661:
	.word	249
	.half	3
	.word	.L662
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L664,.L663
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Eth_17_EthMac_ProvideTxBuffer',0,1,158,4,6,1,1,1
	.word	.L185,.L797,.L184
	.byte	4
	.word	.L185,.L797
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.debug_abbrev'
.L662:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.debug_line'
.L663:
	.word	.L1175-.L1174
.L1174:
	.half	3
	.word	.L1177-.L1176
.L1176:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1177:
	.byte	5,3,7,0,5,2
	.word	.L185
	.byte	3,159,4,1,5,1,7,9
	.half	.L665-.L185
	.byte	3,1,0,1,1
.L1175:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.debug_ranges'
.L664:
	.word	-1,.L185,0,.L665-.L185,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Eth_17_EthMac_SetControllerMode')
	.sect	'.debug_info'
.L666:
	.word	252
	.half	3
	.word	.L667
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L669,.L668
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Eth_17_EthMac_SetControllerMode',0,1,163,4,6,1,1,1
	.word	.L187,.L798,.L186
	.byte	4
	.word	.L187,.L798
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Eth_17_EthMac_SetControllerMode')
	.sect	'.debug_abbrev'
.L667:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Eth_17_EthMac_SetControllerMode')
	.sect	'.debug_line'
.L668:
	.word	.L1179-.L1178
.L1178:
	.half	3
	.word	.L1181-.L1180
.L1180:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1181:
	.byte	5,3,7,0,5,2
	.word	.L187
	.byte	3,164,4,1,5,1,7,9
	.half	.L670-.L187
	.byte	3,1,0,1,1
.L1179:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Eth_17_EthMac_SetControllerMode')
	.sect	'.debug_ranges'
.L669:
	.word	-1,.L187,0,.L670-.L187,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Eth_17_EthMac_SetControllerMode')
	.sect	'.debug_info'
.L671:
	.word	251
	.half	3
	.word	.L672
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L674,.L673
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Eth_17_EthMac_SetControllerMode',0,1,168,4,6,1,1,1
	.word	.L189,.L799,.L188
	.byte	4
	.word	.L189,.L799
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Eth_17_EthMac_SetControllerMode')
	.sect	'.debug_abbrev'
.L672:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Eth_17_EthMac_SetControllerMode')
	.sect	'.debug_line'
.L673:
	.word	.L1183-.L1182
.L1182:
	.half	3
	.word	.L1185-.L1184
.L1184:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1185:
	.byte	5,3,7,0,5,2
	.word	.L189
	.byte	3,169,4,1,5,1,7,9
	.half	.L675-.L189
	.byte	3,1,0,1,1
.L1183:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Eth_17_EthMac_SetControllerMode')
	.sect	'.debug_ranges'
.L674:
	.word	-1,.L189,0,.L675-.L189,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.debug_info'
.L676:
	.word	249
	.half	3
	.word	.L677
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L679,.L678
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Eth_17_EthMac_TxRxIrqHandler',0,1,173,4,6,1,1,1
	.word	.L191,.L800,.L190
	.byte	4
	.word	.L191,.L800
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.debug_abbrev'
.L677:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.debug_line'
.L678:
	.word	.L1187-.L1186
.L1186:
	.half	3
	.word	.L1189-.L1188
.L1188:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1189:
	.byte	5,3,7,0,5,2
	.word	.L191
	.byte	3,174,4,1,5,1,7,9
	.half	.L680-.L191
	.byte	3,1,0,1,1
.L1187:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.debug_ranges'
.L679:
	.word	-1,.L191,0,.L680-.L191,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.debug_info'
.L681:
	.word	248
	.half	3
	.word	.L682
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L684,.L683
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Eth_17_EthMac_TxRxIrqHandler',0,1,178,4,6,1,1,1
	.word	.L193,.L801,.L192
	.byte	4
	.word	.L193,.L801
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.debug_abbrev'
.L682:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.debug_line'
.L683:
	.word	.L1191-.L1190
.L1190:
	.half	3
	.word	.L1193-.L1192
.L1192:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1193:
	.byte	5,3,7,0,5,2
	.word	.L193
	.byte	3,179,4,1,5,1,7,9
	.half	.L685-.L193
	.byte	3,1,0,1,1
.L1191:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.debug_ranges'
.L684:
	.word	-1,.L193,0,.L685-.L193,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Pwm_17_Gtm_StartChannel')
	.sect	'.debug_info'
.L686:
	.word	244
	.half	3
	.word	.L687
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L689,.L688
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Pwm_17_Gtm_StartChannel',0,1,184,4,6,1,1,1
	.word	.L195,.L802,.L194
	.byte	4
	.word	.L195,.L802
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Pwm_17_Gtm_StartChannel')
	.sect	'.debug_abbrev'
.L687:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Pwm_17_Gtm_StartChannel')
	.sect	'.debug_line'
.L688:
	.word	.L1195-.L1194
.L1194:
	.half	3
	.word	.L1197-.L1196
.L1196:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1197:
	.byte	5,3,7,0,5,2
	.word	.L195
	.byte	3,185,4,1,5,1,7,9
	.half	.L690-.L195
	.byte	3,1,0,1,1
.L1195:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Pwm_17_Gtm_StartChannel')
	.sect	'.debug_ranges'
.L689:
	.word	-1,.L195,0,.L690-.L195,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Pwm_17_Gtm_StartChannel')
	.sect	'.debug_info'
.L691:
	.word	243
	.half	3
	.word	.L692
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L694,.L693
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Pwm_17_Gtm_StartChannel',0,1,189,4,6,1,1,1
	.word	.L197,.L803,.L196
	.byte	4
	.word	.L197,.L803
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Pwm_17_Gtm_StartChannel')
	.sect	'.debug_abbrev'
.L692:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Pwm_17_Gtm_StartChannel')
	.sect	'.debug_line'
.L693:
	.word	.L1199-.L1198
.L1198:
	.half	3
	.word	.L1201-.L1200
.L1200:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1201:
	.byte	5,3,7,0,5,2
	.word	.L197
	.byte	3,190,4,1,5,1,7,9
	.half	.L695-.L197
	.byte	3,1,0,1,1
.L1199:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Pwm_17_Gtm_StartChannel')
	.sect	'.debug_ranges'
.L694:
	.word	-1,.L197,0,.L695-.L197,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Enter_Pwm_17_Gtm_SyncDuty')
	.sect	'.debug_info'
.L696:
	.word	240
	.half	3
	.word	.L697
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L699,.L698
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Enter_Pwm_17_Gtm_SyncDuty',0,1,194,4,6,1,1,1
	.word	.L199,.L804,.L198
	.byte	4
	.word	.L199,.L804
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Enter_Pwm_17_Gtm_SyncDuty')
	.sect	'.debug_abbrev'
.L697:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Enter_Pwm_17_Gtm_SyncDuty')
	.sect	'.debug_line'
.L698:
	.word	.L1203-.L1202
.L1202:
	.half	3
	.word	.L1205-.L1204
.L1204:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1205:
	.byte	5,3,7,0,5,2
	.word	.L199
	.byte	3,195,4,1,5,1,7,9
	.half	.L700-.L199
	.byte	3,1,0,1,1
.L1203:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Enter_Pwm_17_Gtm_SyncDuty')
	.sect	'.debug_ranges'
.L699:
	.word	-1,.L199,0,.L700-.L199,0,0
	.sdecl	'.debug_info',debug,cluster('SchM_Exit_Pwm_17_Gtm_SyncDuty')
	.sect	'.debug_info'
.L701:
	.word	239
	.half	3
	.word	.L702
	.byte	4,1
	.byte	'..\\mcal_src\\SchM.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L704,.L703
	.byte	2
	.word	.L202
	.byte	3
	.byte	'SchM_Exit_Pwm_17_Gtm_SyncDuty',0,1,199,4,6,1,1,1
	.word	.L201,.L805,.L200
	.byte	4
	.word	.L201,.L805
	.byte	0,0
	.sdecl	'.debug_abbrev',debug,cluster('SchM_Exit_Pwm_17_Gtm_SyncDuty')
	.sect	'.debug_abbrev'
.L702:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,0,17,1,18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('SchM_Exit_Pwm_17_Gtm_SyncDuty')
	.sect	'.debug_line'
.L703:
	.word	.L1207-.L1206
.L1206:
	.half	3
	.word	.L1209-.L1208
.L1208:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\SchM.c',0,0,0,0,0
.L1209:
	.byte	5,3,7,0,5,2
	.word	.L201
	.byte	3,200,4,1,5,1,7,9
	.half	.L705-.L201
	.byte	3,1,0,1,1
.L1207:
	.sdecl	'.debug_ranges',debug,cluster('SchM_Exit_Pwm_17_Gtm_SyncDuty')
	.sect	'.debug_ranges'
.L704:
	.word	-1,.L201,0,.L705-.L201,0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Adc_DisableHwTrig')
	.sect	'.debug_loc'
.L10:
	.word	-1,.L11,0,.L710-.L11
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Adc_EnableHwTrig')
	.sect	'.debug_loc'
.L8:
	.word	-1,.L9,0,.L709-.L9
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Adc_GetGrpStatus')
	.sect	'.debug_loc'
.L12:
	.word	-1,.L13,0,.L711-.L13
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Adc_GetStreamLastPtr')
	.sect	'.debug_loc'
.L14:
	.word	-1,.L15,0,.L712-.L15
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Adc_PopQueue')
	.sect	'.debug_loc'
.L24:
	.word	-1,.L25,0,.L717-.L25
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Adc_PushQueue')
	.sect	'.debug_loc'
.L22:
	.word	-1,.L23,0,.L716-.L23
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Adc_ReadGroup')
	.sect	'.debug_loc'
.L6:
	.word	-1,.L7,0,.L708-.L7
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Adc_ScheduleNext')
	.sect	'.debug_loc'
.L20:
	.word	-1,.L21,0,.L715-.L21
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Adc_ScheduleStart')
	.sect	'.debug_loc'
.L16:
	.word	-1,.L17,0,.L713-.L17
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Adc_ScheduleStop')
	.sect	'.debug_loc'
.L18:
	.word	-1,.L19,0,.L714-.L19
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Adc_StartGroup')
	.sect	'.debug_loc'
.L2:
	.word	-1,.L3,0,.L706-.L3
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Adc_StopGroup')
	.sect	'.debug_loc'
.L4:
	.word	-1,.L5,0,.L707-.L5
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Can_17_MCanP_CanDisInt')
	.sect	'.debug_loc'
.L74:
	.word	-1,.L75,0,.L742-.L75
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Can_17_MCanP_CanEnInt')
	.sect	'.debug_loc'
.L78:
	.word	-1,.L79,0,.L744-.L79
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Can_17_MCanP_CanWrMO')
	.sect	'.debug_loc'
.L82:
	.word	-1,.L83,0,.L746-.L83
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.debug_loc'
.L182:
	.word	-1,.L183,0,.L796-.L183
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Eth_17_EthMac_SetControllerMode')
	.sect	'.debug_loc'
.L186:
	.word	-1,.L187,0,.L798-.L187
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Eth_17_EthMac_Transmit')
	.sect	'.debug_loc'
.L178:
	.word	-1,.L179,0,.L794-.L179
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.debug_loc'
.L190:
	.word	-1,.L191,0,.L800-.L191
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_FlsLoader_Erase')
	.sect	'.debug_loc'
.L166:
	.word	-1,.L167,0,.L788-.L167
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_FlsLoader_Write')
	.sect	'.debug_loc'
.L168:
	.word	-1,.L169,0,.L789-.L169
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_FlsLoader_lLock')
	.sect	'.debug_loc'
.L170:
	.word	-1,.L171,0,.L790-.L171
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Fls_17_Pmu_Erase')
	.sect	'.debug_loc'
.L150:
	.word	-1,.L151,0,.L780-.L151
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Fls_17_Pmu_Init')
	.sect	'.debug_loc'
.L146:
	.word	-1,.L147,0,.L778-.L147
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Fls_17_Pmu_Main')
	.sect	'.debug_loc'
.L158:
	.word	-1,.L159,0,.L784-.L159
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Fls_17_Pmu_ResumeErase')
	.sect	'.debug_loc'
.L162:
	.word	-1,.L163,0,.L786-.L163
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Fls_17_Pmu_Write')
	.sect	'.debug_loc'
.L154:
	.word	-1,.L155,0,.L782-.L155
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Fr_17_Eray_ControllerInit')
	.sect	'.debug_loc'
.L86:
	.word	-1,.L87,0,.L748-.L87
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Fr_17_Eray_SetWakeupChannel')
	.sect	'.debug_loc'
.L90:
	.word	-1,.L91,0,.L750-.L91
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.debug_loc'
.L62:
	.word	-1,.L63,0,.L736-.L63
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.debug_loc'
.L66:
	.word	-1,.L67,0,.L738-.L67
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.debug_loc'
.L70:
	.word	-1,.L71,0,.L740-.L71
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.debug_loc'
.L54:
	.word	-1,.L55,0,.L732-.L55
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.debug_loc'
.L50:
	.word	-1,.L51,0,.L730-.L51
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.debug_loc'
.L58:
	.word	-1,.L59,0,.L734-.L59
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Pwm_17_Gtm_StartChannel')
	.sect	'.debug_loc'
.L194:
	.word	-1,.L195,0,.L802-.L195
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Pwm_17_Gtm_SyncDuty')
	.sect	'.debug_loc'
.L198:
	.word	-1,.L199,0,.L804-.L199
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Spi_AsyncTransmit')
	.sect	'.debug_loc'
.L98:
	.word	-1,.L99,0,.L754-.L99
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Spi_Cancel')
	.sect	'.debug_loc'
.L106:
	.word	-1,.L107,0,.L758-.L107
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Spi_DeInit')
	.sect	'.debug_loc'
.L114:
	.word	-1,.L115,0,.L762-.L115
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Spi_GetSequenceResult')
	.sect	'.debug_loc'
.L102:
	.word	-1,.L103,0,.L756-.L103
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Spi_Init')
	.sect	'.debug_loc'
.L110:
	.word	-1,.L111,0,.L760-.L111
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Spi_SyncTransmit')
	.sect	'.debug_loc'
.L118:
	.word	-1,.L119,0,.L764-.L119
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Spi_WriteIB')
	.sect	'.debug_loc'
.L94:
	.word	-1,.L95,0,.L752-.L95
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Wdg_17_Scu_ChangeMode')
	.sect	'.debug_loc'
.L126:
	.word	-1,.L127,0,.L768-.L127
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyInit')
	.sect	'.debug_loc'
.L128:
	.word	-1,.L129,0,.L769-.L129
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyOffMode')
	.sect	'.debug_loc'
.L132:
	.word	-1,.L133,0,.L771-.L133
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyTrigger')
	.sect	'.debug_loc'
.L130:
	.word	-1,.L131,0,.L770-.L131
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Wdg_17_Scu_TimerHandling')
	.sect	'.debug_loc'
.L122:
	.word	-1,.L123,0,.L766-.L123
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Enter_Wdg_17_Scu_Trigger')
	.sect	'.debug_loc'
.L124:
	.word	-1,.L125,0,.L767-.L125
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Adc_DisableHwTrig')
	.sect	'.debug_loc'
.L34:
	.word	-1,.L35,0,.L722-.L35
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Adc_EnableHwTrig')
	.sect	'.debug_loc'
.L32:
	.word	-1,.L33,0,.L721-.L33
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Adc_GetGrpStatus')
	.sect	'.debug_loc'
.L36:
	.word	-1,.L37,0,.L723-.L37
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Adc_GetStreamLastPtr')
	.sect	'.debug_loc'
.L38:
	.word	-1,.L39,0,.L724-.L39
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Adc_PopQueue')
	.sect	'.debug_loc'
.L48:
	.word	-1,.L49,0,.L729-.L49
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Adc_PushQueue')
	.sect	'.debug_loc'
.L46:
	.word	-1,.L47,0,.L728-.L47
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Adc_ReadGroup')
	.sect	'.debug_loc'
.L30:
	.word	-1,.L31,0,.L720-.L31
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Adc_ScheduleNext')
	.sect	'.debug_loc'
.L44:
	.word	-1,.L45,0,.L727-.L45
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Adc_ScheduleStart')
	.sect	'.debug_loc'
.L40:
	.word	-1,.L41,0,.L725-.L41
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Adc_ScheduleStop')
	.sect	'.debug_loc'
.L42:
	.word	-1,.L43,0,.L726-.L43
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Adc_StartGroup')
	.sect	'.debug_loc'
.L26:
	.word	-1,.L27,0,.L718-.L27
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Adc_StopGroup')
	.sect	'.debug_loc'
.L28:
	.word	-1,.L29,0,.L719-.L29
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Can_17_MCanP_CanDisInt')
	.sect	'.debug_loc'
.L76:
	.word	-1,.L77,0,.L743-.L77
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Can_17_MCanP_CanEnInt')
	.sect	'.debug_loc'
.L80:
	.word	-1,.L81,0,.L745-.L81
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Can_17_MCanP_CanWrMO')
	.sect	'.debug_loc'
.L84:
	.word	-1,.L85,0,.L747-.L85
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.debug_loc'
.L184:
	.word	-1,.L185,0,.L797-.L185
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Eth_17_EthMac_SetControllerMode')
	.sect	'.debug_loc'
.L188:
	.word	-1,.L189,0,.L799-.L189
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Eth_17_EthMac_Transmit')
	.sect	'.debug_loc'
.L180:
	.word	-1,.L181,0,.L795-.L181
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.debug_loc'
.L192:
	.word	-1,.L193,0,.L801-.L193
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_FlsLoader_Erase')
	.sect	'.debug_loc'
.L176:
	.word	-1,.L177,0,.L793-.L177
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_FlsLoader_Write')
	.sect	'.debug_loc'
.L174:
	.word	-1,.L175,0,.L792-.L175
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_FlsLoader_lLock')
	.sect	'.debug_loc'
.L172:
	.word	-1,.L173,0,.L791-.L173
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Fls_17_Pmu_Erase')
	.sect	'.debug_loc'
.L152:
	.word	-1,.L153,0,.L781-.L153
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Fls_17_Pmu_Init')
	.sect	'.debug_loc'
.L148:
	.word	-1,.L149,0,.L779-.L149
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Fls_17_Pmu_Main')
	.sect	'.debug_loc'
.L160:
	.word	-1,.L161,0,.L785-.L161
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Fls_17_Pmu_ResumeErase')
	.sect	'.debug_loc'
.L164:
	.word	-1,.L165,0,.L787-.L165
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Fls_17_Pmu_Write')
	.sect	'.debug_loc'
.L156:
	.word	-1,.L157,0,.L783-.L157
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Fr_17_Eray_ControllerInit')
	.sect	'.debug_loc'
.L88:
	.word	-1,.L89,0,.L749-.L89
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Fr_17_Eray_SetWakeupChannel')
	.sect	'.debug_loc'
.L92:
	.word	-1,.L93,0,.L751-.L93
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.debug_loc'
.L64:
	.word	-1,.L65,0,.L737-.L65
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.debug_loc'
.L68:
	.word	-1,.L69,0,.L739-.L69
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.debug_loc'
.L72:
	.word	-1,.L73,0,.L741-.L73
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.debug_loc'
.L56:
	.word	-1,.L57,0,.L733-.L57
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.debug_loc'
.L52:
	.word	-1,.L53,0,.L731-.L53
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.debug_loc'
.L60:
	.word	-1,.L61,0,.L735-.L61
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Pwm_17_Gtm_StartChannel')
	.sect	'.debug_loc'
.L196:
	.word	-1,.L197,0,.L803-.L197
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Pwm_17_Gtm_SyncDuty')
	.sect	'.debug_loc'
.L200:
	.word	-1,.L201,0,.L805-.L201
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Spi_AsyncTransmit')
	.sect	'.debug_loc'
.L100:
	.word	-1,.L101,0,.L755-.L101
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Spi_Cancel')
	.sect	'.debug_loc'
.L108:
	.word	-1,.L109,0,.L759-.L109
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Spi_DeInit')
	.sect	'.debug_loc'
.L116:
	.word	-1,.L117,0,.L763-.L117
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Spi_GetSequenceResult')
	.sect	'.debug_loc'
.L104:
	.word	-1,.L105,0,.L757-.L105
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Spi_Init')
	.sect	'.debug_loc'
.L112:
	.word	-1,.L113,0,.L761-.L113
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Spi_SyncTransmit')
	.sect	'.debug_loc'
.L120:
	.word	-1,.L121,0,.L765-.L121
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Spi_WriteIB')
	.sect	'.debug_loc'
.L96:
	.word	-1,.L97,0,.L753-.L97
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Wdg_17_Scu_ChangeMode')
	.sect	'.debug_loc'
.L138:
	.word	-1,.L139,0,.L774-.L139
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyInit')
	.sect	'.debug_loc'
.L140:
	.word	-1,.L141,0,.L775-.L141
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyOffMode')
	.sect	'.debug_loc'
.L144:
	.word	-1,.L145,0,.L777-.L145
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyTrigger')
	.sect	'.debug_loc'
.L142:
	.word	-1,.L143,0,.L776-.L143
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Wdg_17_Scu_TimerHandling')
	.sect	'.debug_loc'
.L134:
	.word	-1,.L135,0,.L772-.L135
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('SchM_Exit_Wdg_17_Scu_Trigger')
	.sect	'.debug_loc'
.L136:
	.word	-1,.L137,0,.L773-.L137
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L1210:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Adc_StartGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L3,.L706-.L3
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Adc_StopGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L5,.L707-.L5
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Adc_ReadGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L7,.L708-.L7
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Adc_EnableHwTrig')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L9,.L709-.L9
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Adc_DisableHwTrig')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L11,.L710-.L11
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Adc_GetGrpStatus')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L13,.L711-.L13
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Adc_GetStreamLastPtr')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L15,.L712-.L15
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Adc_ScheduleStart')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L17,.L713-.L17
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Adc_ScheduleStop')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L19,.L714-.L19
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Adc_ScheduleNext')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L21,.L715-.L21
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Adc_PushQueue')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L23,.L716-.L23
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Adc_PopQueue')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L25,.L717-.L25
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Adc_StartGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L27,.L718-.L27
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Adc_StopGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L29,.L719-.L29
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Adc_ReadGroup')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L31,.L720-.L31
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Adc_EnableHwTrig')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L33,.L721-.L33
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Adc_DisableHwTrig')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L35,.L722-.L35
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Adc_GetGrpStatus')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L37,.L723-.L37
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Adc_GetStreamLastPtr')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L39,.L724-.L39
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Adc_ScheduleStart')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L41,.L725-.L41
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Adc_ScheduleStop')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L43,.L726-.L43
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Adc_ScheduleNext')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L45,.L727-.L45
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Adc_PushQueue')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L47,.L728-.L47
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Adc_PopQueue')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L49,.L729-.L49
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L51,.L730-.L51
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableWakeup')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L53,.L731-.L53
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L55,.L732-.L55
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_EnableNotification')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L57,.L733-.L57
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L59,.L734-.L59
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_ResetEdgeCount')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L61,.L735-.L61
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L63,.L736-.L63
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_Ccu6IenUpdate')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L65,.L737-.L65
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L67,.L738-.L67
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuInterruptHandle')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L69,.L739-.L69
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L71,.L740-.L71
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Icu_17_GtmCcu6_CcuVariableupdate')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L73,.L741-.L73
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Can_17_MCanP_CanDisInt')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L75,.L742-.L75
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Can_17_MCanP_CanDisInt')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L77,.L743-.L77
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Can_17_MCanP_CanEnInt')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L79,.L744-.L79
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Can_17_MCanP_CanEnInt')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L81,.L745-.L81
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Can_17_MCanP_CanWrMO')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L83,.L746-.L83
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Can_17_MCanP_CanWrMO')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L85,.L747-.L85
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Fr_17_Eray_ControllerInit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L87,.L748-.L87
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Fr_17_Eray_ControllerInit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L89,.L749-.L89
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Fr_17_Eray_SetWakeupChannel')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L91,.L750-.L91
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Fr_17_Eray_SetWakeupChannel')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L93,.L751-.L93
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Spi_WriteIB')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L95,.L752-.L95
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Spi_WriteIB')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L97,.L753-.L97
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Spi_AsyncTransmit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L99,.L754-.L99
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Spi_AsyncTransmit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L101,.L755-.L101
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Spi_GetSequenceResult')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L103,.L756-.L103
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Spi_GetSequenceResult')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L105,.L757-.L105
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Spi_Cancel')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L107,.L758-.L107
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Spi_Cancel')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L109,.L759-.L109
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Spi_Init')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L111,.L760-.L111
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Spi_Init')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L113,.L761-.L113
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Spi_DeInit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L115,.L762-.L115
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Spi_DeInit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L117,.L763-.L117
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Spi_SyncTransmit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L119,.L764-.L119
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Spi_SyncTransmit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L121,.L765-.L121
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Wdg_17_Scu_TimerHandling')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L123,.L766-.L123
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Wdg_17_Scu_Trigger')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L125,.L767-.L125
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Wdg_17_Scu_ChangeMode')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L127,.L768-.L127
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyInit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L129,.L769-.L129
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyTrigger')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L131,.L770-.L131
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Wdg_17_Scu_SafetyOffMode')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L133,.L771-.L133
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Wdg_17_Scu_TimerHandling')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L135,.L772-.L135
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Wdg_17_Scu_Trigger')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L137,.L773-.L137
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Wdg_17_Scu_ChangeMode')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L139,.L774-.L139
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyInit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L141,.L775-.L141
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyTrigger')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L143,.L776-.L143
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Wdg_17_Scu_SafetyOffMode')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L145,.L777-.L145
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Fls_17_Pmu_Init')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L147,.L778-.L147
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Fls_17_Pmu_Init')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L149,.L779-.L149
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Fls_17_Pmu_Erase')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L151,.L780-.L151
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Fls_17_Pmu_Erase')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L153,.L781-.L153
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Fls_17_Pmu_Write')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L155,.L782-.L155
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Fls_17_Pmu_Write')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L157,.L783-.L157
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Fls_17_Pmu_Main')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L159,.L784-.L159
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Fls_17_Pmu_Main')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L161,.L785-.L161
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Fls_17_Pmu_ResumeErase')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L163,.L786-.L163
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Fls_17_Pmu_ResumeErase')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L165,.L787-.L165
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_FlsLoader_Erase')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L167,.L788-.L167
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_FlsLoader_Write')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L169,.L789-.L169
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_FlsLoader_lLock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L171,.L790-.L171
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_FlsLoader_lLock')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L173,.L791-.L173
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_FlsLoader_Write')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L175,.L792-.L175
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_FlsLoader_Erase')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L177,.L793-.L177
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Eth_17_EthMac_Transmit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L179,.L794-.L179
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Eth_17_EthMac_Transmit')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L181,.L795-.L181
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L183,.L796-.L183
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Eth_17_EthMac_ProvideTxBuffer')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L185,.L797-.L185
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Eth_17_EthMac_SetControllerMode')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L187,.L798-.L187
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Eth_17_EthMac_SetControllerMode')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L189,.L799-.L189
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L191,.L800-.L191
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Eth_17_EthMac_TxRxIrqHandler')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L193,.L801-.L193
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Pwm_17_Gtm_StartChannel')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L195,.L802-.L195
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Pwm_17_Gtm_StartChannel')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L197,.L803-.L197
	.sdecl	'.debug_frame',debug,cluster('SchM_Enter_Pwm_17_Gtm_SyncDuty')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L199,.L804-.L199
	.sdecl	'.debug_frame',debug,cluster('SchM_Exit_Pwm_17_Gtm_SyncDuty')
	.sect	'.debug_frame'
	.word	12
	.word	.L1210,.L201,.L805-.L201

; ..\mcal_src\SchM.c	   586  }
; ..\mcal_src\SchM.c	   587  
; ..\mcal_src\SchM.c	   588  
; ..\mcal_src\SchM.c	   589  
; ..\mcal_src\SchM.c	   590  

	; Module end
