	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc16552a -c99 --dep-file=mcal_src\\.Gtm_Platform.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=mcal_src\\Gtm_Platform.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o mcal_src\\Gtm_Platform.src ..\\mcal_src\\Gtm_Platform.c"
	.compiler_name		"ctc"
	.name	"Gtm_Platform"

	
$TC16X
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Gtm_lTimConfigure')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Gtm_lTimConfigure

; ..\mcal_src\Gtm_Platform.c	     1  /******************************************************************************
; ..\mcal_src\Gtm_Platform.c	     2  **                                                                           **
; ..\mcal_src\Gtm_Platform.c	     3  ** Copyright (C) Infineon Technologies (2013)                                **
; ..\mcal_src\Gtm_Platform.c	     4  **                                                                           **
; ..\mcal_src\Gtm_Platform.c	     5  ** All rights reserved.                                                      **
; ..\mcal_src\Gtm_Platform.c	     6  **                                                                           **
; ..\mcal_src\Gtm_Platform.c	     7  ** This document contains proprietary information belonging to Infineon      **
; ..\mcal_src\Gtm_Platform.c	     8  ** Technologies. Passing on and copying of this document, and communication  **
; ..\mcal_src\Gtm_Platform.c	     9  ** of its contents is not permitted without prior written authorization.     **
; ..\mcal_src\Gtm_Platform.c	    10  **                                                                           **
; ..\mcal_src\Gtm_Platform.c	    11  *******************************************************************************
; ..\mcal_src\Gtm_Platform.c	    12  **                                                                           **
; ..\mcal_src\Gtm_Platform.c	    13  **  $FILENAME   : Gtm_Platform.c $                                           **
; ..\mcal_src\Gtm_Platform.c	    14  **                                                                           **
; ..\mcal_src\Gtm_Platform.c	    15  **  $CC VERSION : \main\dev_tc23x\16 $                                       **
; ..\mcal_src\Gtm_Platform.c	    16  **                                                                           **
; ..\mcal_src\Gtm_Platform.c	    17  **  $DATE       : 2016-03-28 $                                               **
; ..\mcal_src\Gtm_Platform.c	    18  **                                                                           **
; ..\mcal_src\Gtm_Platform.c	    19  **  AUTHOR      : DL-AUTOSAR-Engineering                                     **
; ..\mcal_src\Gtm_Platform.c	    20  **                                                                           **
; ..\mcal_src\Gtm_Platform.c	    21  **  VENDOR      : Infineon Technologies                                      **
; ..\mcal_src\Gtm_Platform.c	    22  **                                                                           **
; ..\mcal_src\Gtm_Platform.c	    23  **  DESCRIPTION : This file contains                                         **
; ..\mcal_src\Gtm_Platform.c	    24  **                functionality of <> driver.                                **
; ..\mcal_src\Gtm_Platform.c	    25  **                                                                           **
; ..\mcal_src\Gtm_Platform.c	    26  **  MAY BE CHANGED BY USER [yes/no]: No                                      **
; ..\mcal_src\Gtm_Platform.c	    27  **                                                                           **
; ..\mcal_src\Gtm_Platform.c	    28  ******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	    29  
; ..\mcal_src\Gtm_Platform.c	    30  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	    31  **                      Includes                                              **
; ..\mcal_src\Gtm_Platform.c	    32  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	    33  /* Own header file, this includes own configuration file also */
; ..\mcal_src\Gtm_Platform.c	    34  #include "Gtm.h"
; ..\mcal_src\Gtm_Platform.c	    35  #include "Gtm_Local.h"
; ..\mcal_src\Gtm_Platform.c	    36  /* Include Irq definitions for IRQ */
; ..\mcal_src\Gtm_Platform.c	    37  #include "IfxSrc_reg.h"
; ..\mcal_src\Gtm_Platform.c	    38  
; ..\mcal_src\Gtm_Platform.c	    39  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	    40  **                      Private Macro Definitions                             **
; ..\mcal_src\Gtm_Platform.c	    41  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	    42  #define GTM_GET_MODE_VAL_16 (0xC000UL)
; ..\mcal_src\Gtm_Platform.c	    43  /* Bits to shift to get the mode information to LSB for a 16 bit variable */
; ..\mcal_src\Gtm_Platform.c	    44  #define GTM_SHIFT_TO_LSB_16 (14U)
; ..\mcal_src\Gtm_Platform.c	    45  /* Get the IRQ Enable Information from a 16 bit variable */
; ..\mcal_src\Gtm_Platform.c	    46  #define GTM_GET_IRQ_VAL_16 (0x1FFFUL)
; ..\mcal_src\Gtm_Platform.c	    47  
; ..\mcal_src\Gtm_Platform.c	    48  #define GTM_GET_MODE_VAL_8_U (0xC0U)
; ..\mcal_src\Gtm_Platform.c	    49  
; ..\mcal_src\Gtm_Platform.c	    50  #define GTM_ADC_CONNECT_GET_LOWER_NIBBLE (0x0FUL)
; ..\mcal_src\Gtm_Platform.c	    51  #define GTM_ADC_CONNECT_GET_UPPER_NIBBLE (0xF0UL)
; ..\mcal_src\Gtm_Platform.c	    52  #define GTM_BITS_PER_ADC_CONNECTION (4U)
; ..\mcal_src\Gtm_Platform.c	    53  #define GTM_BITS_IN_U32 (32U)
; ..\mcal_src\Gtm_Platform.c	    54  #define GTM_CLEAR_INTERRUPT (3UL)
; ..\mcal_src\Gtm_Platform.c	    55  #define GTM_TOM_ATOM_CLK_POS (12U)
; ..\mcal_src\Gtm_Platform.c	    56  #define GTM_CLEAR_STICKY_BITS (0x52000000U)
; ..\mcal_src\Gtm_Platform.c	    57  #define GTM_TOM_NO_OF_SRC_REG (8U)
; ..\mcal_src\Gtm_Platform.c	    58  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	    59  **                      Private Type Definitions                              **
; ..\mcal_src\Gtm_Platform.c	    60  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	    61  /*Memory Map of the GTM Code*/
; ..\mcal_src\Gtm_Platform.c	    62  #define GTM_START_SEC_CODE
; ..\mcal_src\Gtm_Platform.c	    63  #include "MemMap.h"
; ..\mcal_src\Gtm_Platform.c	    64  
; ..\mcal_src\Gtm_Platform.c	    65  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	    66  **                      Private Function Declarations                         **
; ..\mcal_src\Gtm_Platform.c	    67  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	    68  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	    69  **                      Global Constant Definitions                           **
; ..\mcal_src\Gtm_Platform.c	    70  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	    71  
; ..\mcal_src\Gtm_Platform.c	    72  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	    73  **                      Global Variable Definitions                           **
; ..\mcal_src\Gtm_Platform.c	    74  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	    75  
; ..\mcal_src\Gtm_Platform.c	    76  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	    77  **                      Private Constant Definitions                          **
; ..\mcal_src\Gtm_Platform.c	    78  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	    79  
; ..\mcal_src\Gtm_Platform.c	    80  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	    81  **                      Private Variable Definitions                          **
; ..\mcal_src\Gtm_Platform.c	    82  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	    83  
; ..\mcal_src\Gtm_Platform.c	    84  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	    85  **                      Global Function Definitions                           **
; ..\mcal_src\Gtm_Platform.c	    86  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	    87  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	    88  ** Syntax : void Gtm_lTimConfigure(void)                                      **
; ..\mcal_src\Gtm_Platform.c	    89  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	    90  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm_Platform.c	    91  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	    92  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm_Platform.c	    93  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	    94  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm_Platform.c	    95  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	    96  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm_Platform.c	    97  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	    98  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm_Platform.c	    99  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   100  ** Return value:     None                                                     **
; ..\mcal_src\Gtm_Platform.c	   101  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   102  ** Description :     Function to initialize TIM module                        **
; ..\mcal_src\Gtm_Platform.c	   103  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	   104  void Gtm_lTimConfigure(void)
; Function Gtm_lTimConfigure
.L26:
Gtm_lTimConfigure:	.type	func

; ..\mcal_src\Gtm_Platform.c	   105  {
; ..\mcal_src\Gtm_Platform.c	   106    const Gtm_TimConfigType *TimConfigPtr;         /* Pointer to TIM Config     */
; ..\mcal_src\Gtm_Platform.c	   107    Ifx_GTM_TIM_CH_TYPE *TimChannelRegPtr;       /* Pointer to TIM Channel    */
; ..\mcal_src\Gtm_Platform.c	   108    Ifx_GTM_TIM_IN_SRC_RESET_TYPE *TimInScrRegPtr;
; ..\mcal_src\Gtm_Platform.c	   109                                                /* Pointer to TIM IN_SRC reg    */
; ..\mcal_src\Gtm_Platform.c	   110  
; ..\mcal_src\Gtm_Platform.c	   111    uint8 TimCnt;             /* Variable to TIM Channel Initialization Count   */
; ..\mcal_src\Gtm_Platform.c	   112    uint8 MinorCnt;           /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm_Platform.c	   113    uint8 ChannelNo;          /* Variable to hold Channel Number                */
; ..\mcal_src\Gtm_Platform.c	   114    uint8 TimMode;            /* Variable to hold the TIM Mode                  */
; ..\mcal_src\Gtm_Platform.c	   115  
; ..\mcal_src\Gtm_Platform.c	   116    /* Count to maintain track of the index in TIM Channel Config */
; ..\mcal_src\Gtm_Platform.c	   117    TimCnt = GTM_ZERO_U;
	mov	d2,#0
.L193:

; ..\mcal_src\Gtm_Platform.c	   118    /* MajorCnt -Count to maintain an index to the GtmTimUsage Array            */
; ..\mcal_src\Gtm_Platform.c	   119    /* MinorCnt -Count to maintain an index to the channels in GtmTimUsage Array*/
; ..\mcal_src\Gtm_Platform.c	   120    for(MinorCnt = GTM_ZERO_U; MinorCnt < GTM_BITS_IN_U8; MinorCnt++)
	mov	d3,d2
	mov.a	a5,#7
.L2:

; ..\mcal_src\Gtm_Platform.c	   121    {
; ..\mcal_src\Gtm_Platform.c	   122      /* MISRA Rule Violation 17.4 Pointer arithmetic other than array indexing 
; ..\mcal_src\Gtm_Platform.c	   123                   used. This cannot be avoided because of Post Build structure */
; ..\mcal_src\Gtm_Platform.c	   124        /* Derive Module Number and Channel Number from Loop Count information  */
; ..\mcal_src\Gtm_Platform.c	   125        ChannelNo = MinorCnt % GTM_CHANNELS_PER_TIM_MODULE;
; ..\mcal_src\Gtm_Platform.c	   126  
; ..\mcal_src\Gtm_Platform.c	   127      /* Check if the channel is configured */
; ..\mcal_src\Gtm_Platform.c	   128      if((Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTimUsage[0U] &
	fcall	.cocofun_5
.L194:

; ..\mcal_src\Gtm_Platform.c	   129         (GTM_BIT_SET << MinorCnt)) > GTM_ZERO_UL)
	mov	d1,#1
	ld.bu	d0,[a15]20
.L313:
	sh	d1,d1,d3
.L314:
	and	d0,d1
.L315:
	jeq	d0,#0,.L3
.L316:

; ..\mcal_src\Gtm_Platform.c	   130      {
; ..\mcal_src\Gtm_Platform.c	   131        /*IFX_MISRA_RULE_17_04_STATUS= Pointer arithmetic used due to 
; ..\mcal_src\Gtm_Platform.c	   132       PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Gtm_Platform.c	   133        TimConfigPtr =&(Gtm_kConfigPtr->GtmModuleConfigPtr->\ 
; ..\mcal_src\Gtm_Platform.c	   134                                                       GtmTimConfigPtr[TimCnt]);
	mul	d15,d2,#24
	ld.a	a15,[a15]24
.L317:

; ..\mcal_src\Gtm_Platform.c	   135  
; ..\mcal_src\Gtm_Platform.c	   136        TimCnt++;
; ..\mcal_src\Gtm_Platform.c	   137  
; ..\mcal_src\Gtm_Platform.c	   138        /* Get the Pointer to the Channel Register Structure */
; ..\mcal_src\Gtm_Platform.c	   139        /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm_Platform.c	   140           Permitted for special function registers.*/
; ..\mcal_src\Gtm_Platform.c	   141        TimChannelRegPtr = &(((*(Ifx_GTM_TIMx*)(void *)(MODULE_GTM.TIM)).
; ..\mcal_src\Gtm_Platform.c	   142                                               CH_TIM[0U].CH[ChannelNo]));
; ..\mcal_src\Gtm_Platform.c	   143        /*IFX_MISRA_RULE_11_05_STATUS= volatile in terms of pointer access. 
; ..\mcal_src\Gtm_Platform.c	   144           Permitted for special function registers.*/
; ..\mcal_src\Gtm_Platform.c	   145        TimInScrRegPtr = &(((*(Ifx_GTM_TIMx*)(void *)(MODULE_GTM.TIM)).
; ..\mcal_src\Gtm_Platform.c	   146                                            IN_SRC_RST[0U].IN_SRC_RESET));
; ..\mcal_src\Gtm_Platform.c	   147  
; ..\mcal_src\Gtm_Platform.c	   148        /* Configure all Channel paramters if the Channel usage is Complex    */
; ..\mcal_src\Gtm_Platform.c	   149        if(TimConfigPtr->TimUsage == GTM_DRIVER_COMPLEX)
; ..\mcal_src\Gtm_Platform.c	   150        {
; ..\mcal_src\Gtm_Platform.c	   151          /* Mask the TIM Enable status before writing the control register */
; ..\mcal_src\Gtm_Platform.c	   152          GTM_SFR_INIT_USER_MODE_WRITE32((TimChannelRegPtr->CH_CTRL.U), \ 
; ..\mcal_src\Gtm_Platform.c	   153                                 (TimConfigPtr->TimCtrlValue & ~(GTM_BIT_SET)));
; ..\mcal_src\Gtm_Platform.c	   154  
; ..\mcal_src\Gtm_Platform.c	   155          /* Configure Filter related parameters only if the Filter is
; ..\mcal_src\Gtm_Platform.c	   156             configured */
; ..\mcal_src\Gtm_Platform.c	   157          if(TimConfigPtr->GtmTimFltPtr!= NULL_PTR)
; ..\mcal_src\Gtm_Platform.c	   158          {
; ..\mcal_src\Gtm_Platform.c	   159            GTM_SFR_INIT_USER_MODE_WRITE32((TimChannelRegPtr->CH_FLT_RE.U), \ 
; ..\mcal_src\Gtm_Platform.c	   160                              TimConfigPtr->GtmTimFltPtr->TimRisingEdgeFilter);
; ..\mcal_src\Gtm_Platform.c	   161            GTM_SFR_INIT_USER_MODE_WRITE32((TimChannelRegPtr->CH_FLT_FE.U), \ 
; ..\mcal_src\Gtm_Platform.c	   162                             TimConfigPtr->GtmTimFltPtr->TimFallingEdgeFilter);
; ..\mcal_src\Gtm_Platform.c	   163          }
; ..\mcal_src\Gtm_Platform.c	   164          GTM_SFR_INIT_USER_MODE_WRITE32((TimChannelRegPtr->CH_TDUV.U), \ 
; ..\mcal_src\Gtm_Platform.c	   165                                TimConfigPtr->TimTduValue);
; ..\mcal_src\Gtm_Platform.c	   166          GTM_SFR_INIT_USER_MODE_WRITE32((TimChannelRegPtr->CH_IRQ_MODE.U), \ 
; ..\mcal_src\Gtm_Platform.c	   167                 (((uint32)TimConfigPtr->TimIrqEn & GTM_GET_MODE_VAL_8_U) >>
; ..\mcal_src\Gtm_Platform.c	   168                                                          GTM_SHIFT_TO_LSB_8));
; ..\mcal_src\Gtm_Platform.c	   169  
; ..\mcal_src\Gtm_Platform.c	   170          /* Clear all Pending Interrupts */
; ..\mcal_src\Gtm_Platform.c	   171          GTM_SFR_INIT_USER_MODE_WRITE32((TimChannelRegPtr->CH_IRQ_NOTIFY.U),\ 
; ..\mcal_src\Gtm_Platform.c	   172                     GTM_CLEAR_TIM_INTERRUPT);
; ..\mcal_src\Gtm_Platform.c	   173          GTM_SFR_INIT_USER_MODE_WRITE32((TimChannelRegPtr->CH_IRQ_EN.U), \ 
; ..\mcal_src\Gtm_Platform.c	   174                         ((uint32)TimConfigPtr->TimIrqEn & GTM_GET_IRQ_VAL_8));
; ..\mcal_src\Gtm_Platform.c	   175          /*Setting TIM EIRQ values*/
; ..\mcal_src\Gtm_Platform.c	   176          GTM_SFR_INIT_USER_MODE_WRITE32((TimChannelRegPtr->CH_EIRQ_EN.U)  , \ 
; ..\mcal_src\Gtm_Platform.c	   177                ((uint32)TimConfigPtr->TimErrIrqEn & GTM_TIM_GET_EIRQ_VAL_32));
; ..\mcal_src\Gtm_Platform.c	   178  
; ..\mcal_src\Gtm_Platform.c	   179          /*Setting TIM IN_SRC values*/
; ..\mcal_src\Gtm_Platform.c	   180          GTM_SFR_INIT_USER_MODE_WRITE32((TimInScrRegPtr->IN_SRC.U), \ 
; ..\mcal_src\Gtm_Platform.c	   181                 ((uint32)TimConfigPtr->TimInSrcSel & GTM_TIM_GET_INSRC_VAL_32));
; ..\mcal_src\Gtm_Platform.c	   182  
; ..\mcal_src\Gtm_Platform.c	   183          TimMode = \ 
; ..\mcal_src\Gtm_Platform.c	   184          (uint8)((TimConfigPtr->TimCtrlValue & GTM_TIM_GET_MODE) >> GTM_ONE_U);
; ..\mcal_src\Gtm_Platform.c	   185  
; ..\mcal_src\Gtm_Platform.c	   186          /* CNTS register is writable only in TIPM mode */
; ..\mcal_src\Gtm_Platform.c	   187          if(TimMode == GTM_TIM_MODE_TIPM)
; ..\mcal_src\Gtm_Platform.c	   188          {
; ..\mcal_src\Gtm_Platform.c	   189            GTM_SFR_INIT_USER_MODE_WRITE32((TimChannelRegPtr->CH_CNTS.U), \ 
; ..\mcal_src\Gtm_Platform.c	   190                                  TimConfigPtr->TimCntsValue);
; ..\mcal_src\Gtm_Platform.c	   191          }
; ..\mcal_src\Gtm_Platform.c	   192  
; ..\mcal_src\Gtm_Platform.c	   193          /* Update the TIM Enable bit if enabled */
; ..\mcal_src\Gtm_Platform.c	   194          GTM_SFR_INIT_USER_MODE_MODIFY32((TimChannelRegPtr->CH_CTRL.U),\ 
; ..\mcal_src\Gtm_Platform.c	   195                 GTM_TIM_CH_CTRL_CLR_MASK,\ 
; ..\mcal_src\Gtm_Platform.c	   196                 (TimConfigPtr->TimCtrlValue & GTM_TIM_ENABLE_MASK))
; ..\mcal_src\Gtm_Platform.c	   197  
; ..\mcal_src\Gtm_Platform.c	   198         /* Update the TIM External Control register if EXT_CAP_EN is enabled */
; ..\mcal_src\Gtm_Platform.c	   199          GTM_SFR_INIT_USER_MODE_MODIFY32((TimChannelRegPtr->CH_ECTRL.U),\ 
; ..\mcal_src\Gtm_Platform.c	   200                   GTM_SEVEN_U,\ 
; ..\mcal_src\Gtm_Platform.c	   201                   (TimConfigPtr->TimExtCapSrc & GTM_TIM_EXT_CAP_ENABLE_MASK))
; ..\mcal_src\Gtm_Platform.c	   202  
; ..\mcal_src\Gtm_Platform.c	   203  
; ..\mcal_src\Gtm_Platform.c	   204          /* Enable SRN */
; ..\mcal_src\Gtm_Platform.c	   205          GTM_SFR_INIT_MODIFY32((MODULE_SRC.GTM.GTM[0].TIM[0U][ChannelNo].U),\ 
	add	d2,#1
	movh.a	a4,#61444
.L159:
	mov	d1,#3327
	lea	a4,[a4]@los(0xf0039780)
.L160:
	addsc.a	a2,a15,d15,#0
.L195:
	sha	d15,d3,#7
	movh.a	a15,#61456
.L161:
	addsc.a	a4,a4,d3,#2
.L162:
	lea	a15,[a15]@los(0xf0101000)
.L318:
	addsc.a	a15,a15,d15,#0
.L196:
	ld.bu	d15,[a2]
.L319:
	extr.u	d2,d2,#0,#8
.L163:
	addih	d1,d1,#32543
.L164:
	mov	d0,#63
.L320:
	jne	d15,#0,.L4
.L321:
	ld.w	d15,[a2]4
	insert	d15,d15,#0,#0,#1
	st.w	[a15]36,d15
.L322:
	ld.a	a6,[a2]8
.L323:
	jz.a	a6,.L5
.L324:
	ld.w	d15,[a6]
	st.w	[a15]28,d15
.L325:
	ld.a	a6,[a2]8
	ld.w	d15,[a6]4
	st.w	[a15]32,d15
.L5:
	ld.w	d15,[a2]16
	st.w	[a15]24,d15
.L326:
	movh.a	a6,#61456
.L327:
	ld.bu	d15,[a2]1
	and	d15,#192
	sh	d15,#-6
	st.w	[a15]56,d15
.L328:
	st.w	[a15]44,d0
.L329:
	ld.bu	d15,[a2]1
	and	d15,#63
	st.w	[a15]48,d15
.L330:
	ld.bu	d15,[a2]2
	and	d15,#63
	st.w	[a15]60,d15
.L331:
	ld.w	d15,[a2]20
	st.w	[a6]@los(0xf0101078),d15
.L332:
	ld.w	d15,[a2]4
.L333:
	and	d15,#14
.L334:
	jne	d15,#6,.L6
.L335:
	ld.w	d15,[a2]12
	st.w	[a15]16,d15
.L6:
	mov.u	d0,#65503
	ld.w	d15,[a15]36
	addih	d0,d0,#65527
	and	d0,d15
	ld.w	d15,[a2]4
	and	d15,#1
.L197:
	or	d0,d15
	st.w	[a15]36,d0
.L167:
	ld.w	d15,[a15]40
	and	d0,d15,#7
	ld.bu	d15,[a2]3
.L198:
	and	d15,#7
.L199:
	or	d0,d15
	st.w	[a15]40,d0
.L165:
	ld.w	d15,[a4]
	and	d1,d15
.L336:
	insert	d15,d1,#1,#10,#1
	j	.L7
.L4:

; ..\mcal_src\Gtm_Platform.c	   206              GTM_SRC_CLEAR_MASK,(unsigned_int)(GTM_BIT_SET << GTM_BIT_SRE))
; ..\mcal_src\Gtm_Platform.c	   207  
; ..\mcal_src\Gtm_Platform.c	   208  
; ..\mcal_src\Gtm_Platform.c	   209        }
; ..\mcal_src\Gtm_Platform.c	   210        /* If the channel is configured for ICU, then enable the SRN          */
; ..\mcal_src\Gtm_Platform.c	   211        else
; ..\mcal_src\Gtm_Platform.c	   212        {
; ..\mcal_src\Gtm_Platform.c	   213          GTM_SFR_INIT_USER_MODE_WRITE32((TimChannelRegPtr->CH_IRQ_NOTIFY.U), \ 
	st.w	[a15]44,d0
.L170:

; ..\mcal_src\Gtm_Platform.c	   214                                   GTM_CLEAR_TIM_INTERRUPT);
; ..\mcal_src\Gtm_Platform.c	   215  
; ..\mcal_src\Gtm_Platform.c	   216          /* Enable SRN */
; ..\mcal_src\Gtm_Platform.c	   217          GTM_SFR_INIT_MODIFY32((MODULE_SRC.GTM.GTM[0].TIM[0U][ChannelNo].U),\ 
	ld.w	d15,[a4]
	and	d15,d1
	insert	d15,d15,#1,#10,#1
.L7:
	st.w	[a4],d15
.L3:
	add	d3,#1
	loop	a5,.L2
.L337:

; ..\mcal_src\Gtm_Platform.c	   218              GTM_SRC_CLEAR_MASK,(unsigned_int)(GTM_BIT_SET << GTM_BIT_SRE))
; ..\mcal_src\Gtm_Platform.c	   219        }
; ..\mcal_src\Gtm_Platform.c	   220      }
; ..\mcal_src\Gtm_Platform.c	   221    }
; ..\mcal_src\Gtm_Platform.c	   222  
; ..\mcal_src\Gtm_Platform.c	   223  }
	ret
.L150:
	
__Gtm_lTimConfigure_function_end:
	.size	Gtm_lTimConfigure,__Gtm_lTimConfigure_function_end-Gtm_lTimConfigure
.L68:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_5')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_5
.L28:
.cocofun_5:	.type	func
; Function body .cocofun_5, coco_iter:1
	movh.a	a15,#@his(Gtm_kConfigPtr)
	ld.a	a15,[a15]@los(Gtm_kConfigPtr)
.L370:
	ld.a	a15,[a15]8
.L371:
	fret
.L98:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Gtm_lAdcConnectionsConfig')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Gtm_lAdcConnectionsConfig

; ..\mcal_src\Gtm_Platform.c	   224  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	   225  ** Syntax : void Gtm_lAdcConnectionsConfig(void)                              **
; ..\mcal_src\Gtm_Platform.c	   226  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   227  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm_Platform.c	   228  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   229  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm_Platform.c	   230  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   231  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm_Platform.c	   232  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   233  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm_Platform.c	   234  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   235  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm_Platform.c	   236  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   237  ** Return value:     None                                                     **
; ..\mcal_src\Gtm_Platform.c	   238  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   239  ** Description :     Function to initialize ADC Connections                   **
; ..\mcal_src\Gtm_Platform.c	   240  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	   241   void Gtm_lAdcConnectionsConfig(void)
; Function Gtm_lAdcConnectionsConfig
.L30:
Gtm_lAdcConnectionsConfig:	.type	func

; ..\mcal_src\Gtm_Platform.c	   242  {
; ..\mcal_src\Gtm_Platform.c	   243    uint32 RegTemp1;          /* Temporary variable to store the register value */
; ..\mcal_src\Gtm_Platform.c	   244    uint32 RegTemp2;          /* Temporary variable to store the register value */
; ..\mcal_src\Gtm_Platform.c	   245    uint8 Count;
; ..\mcal_src\Gtm_Platform.c	   246  
; ..\mcal_src\Gtm_Platform.c	   247    /* Check if Adc Connections is configured */
; ..\mcal_src\Gtm_Platform.c	   248    if(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmAdcConnectionsPtr != NULL_PTR)
	fcall	.cocofun_5
.L254:
	ld.a	a15,[a15]64
.L255:
	jz.a	a15,.L8
.L256:

; ..\mcal_src\Gtm_Platform.c	   249    {
; ..\mcal_src\Gtm_Platform.c	   250      RegTemp1 = GTM_ZERO_UL;
	mov	d0,#0
.L200:

; ..\mcal_src\Gtm_Platform.c	   251      RegTemp2 = GTM_ZERO_UL;
	mov	d1,d0
.L201:

; ..\mcal_src\Gtm_Platform.c	   252  
; ..\mcal_src\Gtm_Platform.c	   253      /* MISRA Rule Violation 17.4
; ..\mcal_src\Gtm_Platform.c	   254         Pointer arithmetic other than array indexing used
; ..\mcal_src\Gtm_Platform.c	   255         This cannot be avoided because of Post Build structure */
; ..\mcal_src\Gtm_Platform.c	   256  
; ..\mcal_src\Gtm_Platform.c	   257      for(Count=GTM_ZERO_U; Count < GTM_ADC_CONN_PER_REGISTER; Count++)
	mov	d2,d0
	mov.a	a2,#3
.L9:

; ..\mcal_src\Gtm_Platform.c	   258      {
; ..\mcal_src\Gtm_Platform.c	   259        /*IFX_MISRA_RULE_17_04_STATUS= Pointer arithmetic used due to 
; ..\mcal_src\Gtm_Platform.c	   260       PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Gtm_Platform.c	   261        RegTemp1 |=                                                              \ 
; ..\mcal_src\Gtm_Platform.c	   262         ((uint32)Gtm_kConfigPtr->GtmModuleConfigPtr->GtmAdcConnectionsPtr[Count]\ 
	ld.bu	d15,[a15+]
.L257:

; ..\mcal_src\Gtm_Platform.c	   263                                     & GTM_ADC_CONNECT_GET_LOWER_NIBBLE) <<      \ 
; ..\mcal_src\Gtm_Platform.c	   264                                      (GTM_BITS_PER_ADC_CONNECTION * Count);
	sh	d3,d2,#2
.L258:
	and	d4,d15,#15
.L259:

; ..\mcal_src\Gtm_Platform.c	   265        /*IFX_MISRA_RULE_17_04_STATUS= Pointer arithmetic used due to 
; ..\mcal_src\Gtm_Platform.c	   266       PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Gtm_Platform.c	   267        RegTemp2 |=                                                              \ 
; ..\mcal_src\Gtm_Platform.c	   268        (((uint32)Gtm_kConfigPtr->GtmModuleConfigPtr->GtmAdcConnectionsPtr[Count]\ 
; ..\mcal_src\Gtm_Platform.c	   269              & GTM_ADC_CONNECT_GET_UPPER_NIBBLE) >> GTM_BITS_PER_ADC_CONNECTION)\ 
	and	d15,#240
.L260:
	sh	d15,#-4
.L261:
	sh	d4,d4,d3
.L262:

; ..\mcal_src\Gtm_Platform.c	   270              << (GTM_BITS_PER_ADC_CONNECTION * Count);
	sh	d15,d15,d3
.L263:
	or	d0,d4
.L264:
	or	d1,d15
.L265:
	add	d2,#1
	loop	a2,.L9
.L266:

; ..\mcal_src\Gtm_Platform.c	   271  
; ..\mcal_src\Gtm_Platform.c	   272      }
; ..\mcal_src\Gtm_Platform.c	   273      GTM_SFR_INIT_USER_MODE_WRITE32((GTM_ADCTRIG0OUT0.U),(uint32)RegTemp1);
	movh.a	a15,#61466
	st.w	[a15]@los(0xf019fdb0),d0
.L267:

; ..\mcal_src\Gtm_Platform.c	   274      GTM_SFR_INIT_USER_MODE_WRITE32((GTM_ADCTRIG1OUT0.U),(uint32)RegTemp2);
	st.w	[a15]@los(0xf019fdb8),d1
.L8:

; ..\mcal_src\Gtm_Platform.c	   275    }
; ..\mcal_src\Gtm_Platform.c	   276  }
	ret
.L99:
	
__Gtm_lAdcConnectionsConfig_function_end:
	.size	Gtm_lAdcConnectionsConfig,__Gtm_lAdcConnectionsConfig_function_end-Gtm_lAdcConnectionsConfig
.L53:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Gtm_lResetGtmSRCReg')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Gtm_lResetGtmSRCReg

; ..\mcal_src\Gtm_Platform.c	   277  
; ..\mcal_src\Gtm_Platform.c	   278  #if (GTM_DEINIT_API_ENABLE == STD_ON)
; ..\mcal_src\Gtm_Platform.c	   279  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	   280  ** Traceability : [cover parentID=                    ]                       **
; ..\mcal_src\Gtm_Platform.c	   281  ** Syntax :  void Gtm_lResetGtmSRCReg (void)                                  **
; ..\mcal_src\Gtm_Platform.c	   282  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   283  ** [/cover]                                                                   **
; ..\mcal_src\Gtm_Platform.c	   284  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   285  ** Service ID:       None                                                     **
; ..\mcal_src\Gtm_Platform.c	   286  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   287  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm_Platform.c	   288  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   289  ** Reentrancy:       Non Reentrant                                            **
; ..\mcal_src\Gtm_Platform.c	   290  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   291  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm_Platform.c	   292  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   293  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm_Platform.c	   294  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   295  ** Return value:     None                                                     **
; ..\mcal_src\Gtm_Platform.c	   296  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   297  ** Description :  This service shall de-initialize GTM registers that are not **
; ..\mcal_src\Gtm_Platform.c	   298  ** de-initaliazed by GTM kernel reset                                         **
; ..\mcal_src\Gtm_Platform.c	   299  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   300  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	   301  void Gtm_lResetGtmSRCReg(void)
; Function Gtm_lResetGtmSRCReg
.L32:
Gtm_lResetGtmSRCReg:	.type	func

; ..\mcal_src\Gtm_Platform.c	   302  {
; ..\mcal_src\Gtm_Platform.c	   303    uint8 ModuleNo;
; ..\mcal_src\Gtm_Platform.c	   304    uint8 ChannelNo;
; ..\mcal_src\Gtm_Platform.c	   305    /* De-init AEI SRC register*/
; ..\mcal_src\Gtm_Platform.c	   306    /* Clear Sticky bits of SRC register */
; ..\mcal_src\Gtm_Platform.c	   307    GTM_SFR_DEINIT_MODIFY32((SRC_GTMAEIIRQ.U),GTM_SRC_CLEAR_MASK,\ 
	mov	d3,#3327
	movh.a	a15,#61444
	lea	a15,[a15]@los(0xf0039600)
	addih	d3,d3,#32543
	ld.w	d15,[a15]
	movh	d2,#20992
	and	d15,d3
.L202:
	or	d15,d2
	st.w	[a15],d15
.L175:

; ..\mcal_src\Gtm_Platform.c	   308      GTM_CLEAR_STICKY_BITS)
; ..\mcal_src\Gtm_Platform.c	   309  
; ..\mcal_src\Gtm_Platform.c	   310    /* Disable SRE  */
; ..\mcal_src\Gtm_Platform.c	   311    GTM_SFR_DEINIT_MODIFY32((SRC_GTMAEIIRQ.U),GTM_SRC_SRE_CLEAR_MASK,\ 
	mov	d4,#2303
	ld.w	d15,[a15]
.L203:
	addih	d4,d4,#32543
.L204:
	and	d15,d4
	st.w	[a15],d15
.L177:

; ..\mcal_src\Gtm_Platform.c	   312           GTM_ZERO_U)
; ..\mcal_src\Gtm_Platform.c	   313    /* De-init TIM SRC registers */
; ..\mcal_src\Gtm_Platform.c	   314  
; ..\mcal_src\Gtm_Platform.c	   315    for(ChannelNo=GTM_ZERO_U; ChannelNo < GTM_CHANNELS_PER_TIM_MODULE;\ 
	mov	d15,#0
	mov.a	a15,#7

; ..\mcal_src\Gtm_Platform.c	   316                                                                  ChannelNo++)
.L10:

; ..\mcal_src\Gtm_Platform.c	   317    {
; ..\mcal_src\Gtm_Platform.c	   318      /* Clear Sticky bits of SRC register */
; ..\mcal_src\Gtm_Platform.c	   319      GTM_SFR_DEINIT_MODIFY32((MODULE_SRC.GTM.GTM[0U].TIM[0U][ChannelNo].U),\ 
	movh.a	a2,#61444
	lea	a2,[a2]@los(0xf0039780)
	addsc.a	a2,a2,d15,#2
.L180:
	add	d15,#1
.L181:
	ld.w	d0,[a2]
	and	d0,d3
.L205:
	or	d0,d2
	st.w	[a2],d0
.L182:

; ..\mcal_src\Gtm_Platform.c	   320         GTM_SRC_CLEAR_MASK,GTM_CLEAR_STICKY_BITS)
; ..\mcal_src\Gtm_Platform.c	   321      /* Disable SRE  */
; ..\mcal_src\Gtm_Platform.c	   322  
; ..\mcal_src\Gtm_Platform.c	   323      GTM_SFR_DEINIT_MODIFY32((MODULE_SRC.GTM.GTM[0U].TIM[0U][ChannelNo].U),\ 
	ld.w	d0,[a2]
.L206:
	and	d0,d4
	st.w	[a2],d0
	loop	a15,.L10
.L184:

; ..\mcal_src\Gtm_Platform.c	   324           GTM_SRC_SRE_CLEAR_MASK,GTM_ZERO_U)
; ..\mcal_src\Gtm_Platform.c	   325  
; ..\mcal_src\Gtm_Platform.c	   326    }
; ..\mcal_src\Gtm_Platform.c	   327  
; ..\mcal_src\Gtm_Platform.c	   328    /* De-init TOM SRC registers */
; ..\mcal_src\Gtm_Platform.c	   329    for(ModuleNo=GTM_ZERO_U; ModuleNo < GTM_NO_OF_TOM_MODULES; ModuleNo++)
	mov	d0,#0
	mov.a	a15,#1
.L11:

; ..\mcal_src\Gtm_Platform.c	   330    {
; ..\mcal_src\Gtm_Platform.c	   331      for(ChannelNo=GTM_ZERO_U; ChannelNo < GTM_TOM_NO_OF_SRC_REG;\ 
	mov	d1,#0
	mov.a	a2,#7

; ..\mcal_src\Gtm_Platform.c	   332                                                                   ChannelNo++)
.L12:

; ..\mcal_src\Gtm_Platform.c	   333      {
; ..\mcal_src\Gtm_Platform.c	   334        /* Clear Sticky bits of SRC register */
; ..\mcal_src\Gtm_Platform.c	   335       GTM_SFR_DEINIT_MODIFY32(\ 
	sha	d15,d0,#5
	movh.a	a4,#61444
	lea	a4,[a4]@los(0xf0039b80)
	addsc.a	a4,a4,d15,#0
	addsc.a	a4,a4,d1,#2
.L187:
	add	d1,#1
.L188:
	ld.w	d15,[a4]
	and	d15,d3
.L207:
	or	d15,d2
	st.w	[a4],d15
.L189:

; ..\mcal_src\Gtm_Platform.c	   336           (MODULE_SRC.GTM.GTM[0U].TOM[ModuleNo][ChannelNo].U),\ 
; ..\mcal_src\Gtm_Platform.c	   337           GTM_SRC_CLEAR_MASK,\ 
; ..\mcal_src\Gtm_Platform.c	   338           GTM_CLEAR_STICKY_BITS)
; ..\mcal_src\Gtm_Platform.c	   339        /* Disable SRE  */
; ..\mcal_src\Gtm_Platform.c	   340       GTM_SFR_DEINIT_MODIFY32(\ 
	ld.w	d15,[a4]
.L208:
	and	d15,d4
	st.w	[a4],d15
	loop	a2,.L12
.L191:
	add	d0,#1
	loop	a15,.L11
.L342:

; ..\mcal_src\Gtm_Platform.c	   341           (MODULE_SRC.GTM.GTM[0U].TOM[ModuleNo][ChannelNo].U),\ 
; ..\mcal_src\Gtm_Platform.c	   342           GTM_SRC_SRE_CLEAR_MASK,
; ..\mcal_src\Gtm_Platform.c	   343           GTM_ZERO_U)
; ..\mcal_src\Gtm_Platform.c	   344      }
; ..\mcal_src\Gtm_Platform.c	   345    }
; ..\mcal_src\Gtm_Platform.c	   346  }
	ret
.L172:
	
__Gtm_lResetGtmSRCReg_function_end:
	.size	Gtm_lResetGtmSRCReg,__Gtm_lResetGtmSRCReg_function_end-Gtm_lResetGtmSRCReg
.L73:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Gtm_lTomComplexConfig')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Gtm_lTomComplexConfig

; ..\mcal_src\Gtm_Platform.c	   347  /* End of Gtm_lResetGtmSRCReg() */
; ..\mcal_src\Gtm_Platform.c	   348  #endif
; ..\mcal_src\Gtm_Platform.c	   349  
; ..\mcal_src\Gtm_Platform.c	   350  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	   351  ** Syntax : void Gtm_lTomComplexConfig(void)                                  **
; ..\mcal_src\Gtm_Platform.c	   352  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   353  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm_Platform.c	   354  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   355  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm_Platform.c	   356  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   357  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm_Platform.c	   358  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   359  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm_Platform.c	   360  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   361  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm_Platform.c	   362  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   363  ** Return value:     None                                                     **
; ..\mcal_src\Gtm_Platform.c	   364  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   365  ** Description :     Function to initialize Complex Configuration of TOM      **
; ..\mcal_src\Gtm_Platform.c	   366  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	   367  void Gtm_lTomComplexConfig(void)
; Function Gtm_lTomComplexConfig
.L34:
Gtm_lTomComplexConfig:	.type	func

; ..\mcal_src\Gtm_Platform.c	   368  {
; ..\mcal_src\Gtm_Platform.c	   369    const Gtm_TomConfigType *TomChannelConfigPtr;  /* Pointer to TOM Channel    */
; ..\mcal_src\Gtm_Platform.c	   370    Ifx_GTM_TOM_CH_TYPE *TomChannelRegPtr;       /* Ptr to TOM Channel Reg    */
; ..\mcal_src\Gtm_Platform.c	   371    uint8 TomCnt;             /* Variable to TOM Channel Initialization Count   */
; ..\mcal_src\Gtm_Platform.c	   372    uint8 MajorCnt;           /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm_Platform.c	   373    uint8 MinorCnt;           /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm_Platform.c	   374    uint8 ModuleNo;           /* Variable to hold Module Number                 */
; ..\mcal_src\Gtm_Platform.c	   375    uint8 ChannelNo;          /* Variable to hold Channel Number                */
; ..\mcal_src\Gtm_Platform.c	   376    volatile uint32 MajorCountLoopLimit = (((GTM_MAX_TOM_CHANNELS - GTM_ONE_U)/\ 
; ..\mcal_src\Gtm_Platform.c	   377                                                     GTM_BITS_IN_U32) +GTM_ONE_U);
	mov	d15,#1
	sub.a	a10,#8
.L209:
	st.w	[a10],d15
.L272:

; ..\mcal_src\Gtm_Platform.c	   378    /* Count to maintain track of the index in TOM Channel Config  */
; ..\mcal_src\Gtm_Platform.c	   379    TomCnt = GTM_ZERO_U;
	mov	d1,#0
.L211:

; ..\mcal_src\Gtm_Platform.c	   380  
; ..\mcal_src\Gtm_Platform.c	   381    /* MajorCnt -Count to maintain an index to the GtmTomUsage Array            */
; ..\mcal_src\Gtm_Platform.c	   382    /* MinorCnt -Count to maintain an index to the channels in GtmTomUsage Array*/
; ..\mcal_src\Gtm_Platform.c	   383    for(MajorCnt = GTM_ZERO_U; MajorCnt < MajorCountLoopLimit; MajorCnt++)
	ld.w	d0,[a10]
.L273:
	mov	d3,d1
.L212:
	jeq	d0,#0,.L13
.L14:

; ..\mcal_src\Gtm_Platform.c	   384    {
; ..\mcal_src\Gtm_Platform.c	   385      for(MinorCnt = GTM_ZERO_U;
	mov	d2,#0
.L213:

; ..\mcal_src\Gtm_Platform.c	   386         (((MajorCnt * GTM_BITS_IN_U32)+ MinorCnt) < GTM_MAX_TOM_CHANNELS);
	sh	d4,d3,#5
.L274:
	mov	d15,#32
.L275:
	jge.u	d4,d15,.L15

; ..\mcal_src\Gtm_Platform.c	   387         MinorCnt++)
.L16:

; ..\mcal_src\Gtm_Platform.c	   388      {
; ..\mcal_src\Gtm_Platform.c	   389        /* Check if the channel is configured */
; ..\mcal_src\Gtm_Platform.c	   390        if((Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTomUsage[MajorCnt] &          \ 
	fcall	.cocofun_1
.L210:
	jeq	d0,#0,.L17
.L276:

; ..\mcal_src\Gtm_Platform.c	   391          (GTM_BIT_SET << MinorCnt)) > GTM_ZERO_UL)
; ..\mcal_src\Gtm_Platform.c	   392        {
; ..\mcal_src\Gtm_Platform.c	   393          /* MISRA Rule Violation 17.4
; ..\mcal_src\Gtm_Platform.c	   394             Pointer arithmetic other than array indexing used
; ..\mcal_src\Gtm_Platform.c	   395             This cannot be avoided because of Post Build structure */
; ..\mcal_src\Gtm_Platform.c	   396  
; ..\mcal_src\Gtm_Platform.c	   397          /* Get the Pointer to the Channel Configuration Register */
; ..\mcal_src\Gtm_Platform.c	   398          /*IFX_MISRA_RULE_17_04_STATUS= Pointer arithmetic used due to 
; ..\mcal_src\Gtm_Platform.c	   399       PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Gtm_Platform.c	   400          TomChannelConfigPtr =
; ..\mcal_src\Gtm_Platform.c	   401                   &(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTomConfigPtr[TomCnt]);
	fcall	.cocofun_3
.L218:

; ..\mcal_src\Gtm_Platform.c	   402  
; ..\mcal_src\Gtm_Platform.c	   403          TomCnt++;
; ..\mcal_src\Gtm_Platform.c	   404          /* Initialize the channels if the channel is configured as Complex*/
; ..\mcal_src\Gtm_Platform.c	   405          if(TomChannelConfigPtr->TomUsage == GTM_DRIVER_COMPLEX)
	jne	d15,#0,.L18
.L277:

; ..\mcal_src\Gtm_Platform.c	   406          {
; ..\mcal_src\Gtm_Platform.c	   407            /* Extract Module Number and Channel Number from the Loop Count     */
; ..\mcal_src\Gtm_Platform.c	   408            ModuleNo = ((MajorCnt * GTM_BITS_IN_U32) + MinorCnt)/
; ..\mcal_src\Gtm_Platform.c	   409                         GTM_CHANNELS_PER_TOM_MODULE;
; ..\mcal_src\Gtm_Platform.c	   410            ChannelNo = MinorCnt % GTM_CHANNELS_PER_TOM_MODULE;
; ..\mcal_src\Gtm_Platform.c	   411  
; ..\mcal_src\Gtm_Platform.c	   412            /* MISRA Rule Violation 11.4 and 1.2
; ..\mcal_src\Gtm_Platform.c	   413               Unusual pointer cast (incompatible indirect types)
; ..\mcal_src\Gtm_Platform.c	   414               cast from pointer to pointer
; ..\mcal_src\Gtm_Platform.c	   415               Casting of TOM to Gtm_TomChannelRegType is done to change the base
; ..\mcal_src\Gtm_Platform.c	   416               type of TOM as the type defined in SFR file is not flexible to
; ..\mcal_src\Gtm_Platform.c	   417               provide an arrayed approach for accessing the TOM channels. */
; ..\mcal_src\Gtm_Platform.c	   418            /*Get the reference to the TOM channel pointer reg*/
; ..\mcal_src\Gtm_Platform.c	   419            /*IFX_MISRA_RULE_11_05_STATUS= Volatile in terms of pointer access. 
; ..\mcal_src\Gtm_Platform.c	   420           Permitted for special function registers.*/
; ..\mcal_src\Gtm_Platform.c	   421            TomChannelRegPtr = &((((*(Ifx_GTM_TOMx*)(void *)(MODULE_GTM.TOM)).   \ 
	add	d15,d4,d2
	movh.a	a15,#61457
.L278:
	sh	d0,d15,#-4
	lea	a15,[a15]@los(0xf0108000)
.L219:

; ..\mcal_src\Gtm_Platform.c	   422                                              TOM_CH[ModuleNo])).CH[ChannelNo]);
; ..\mcal_src\Gtm_Platform.c	   423  
; ..\mcal_src\Gtm_Platform.c	   424            /* Initialize the Channel parameters */
; ..\mcal_src\Gtm_Platform.c	   425            GTM_SFR_INIT_WRITE32((TomChannelRegPtr->CN0.U),\ 
	sha	d15,d0,#11
	ld.a	a4,[a2]8
.L279:
	addsc.a	a15,a15,d15,#0
.L280:
	and	d6,d2,#15
.L220:
	sha	d15,d6,#6
.L281:
	addsc.a	a15,a15,d15,#0
.L221:
	ld.hu	d15,[a4]2
	st.w	[a15]20,d15
.L119:

; ..\mcal_src\Gtm_Platform.c	   426                       TomChannelConfigPtr->GtmTomChannelCfgPtr->GtmTomCn0Value);
; ..\mcal_src\Gtm_Platform.c	   427            GTM_SFR_INIT_WRITE32((TomChannelRegPtr->CM0.U),\ 
; ..\mcal_src\Gtm_Platform.c	   428                       TomChannelConfigPtr->GtmTomChannelCfgPtr->GtmTomCm0Value);
; ..\mcal_src\Gtm_Platform.c	   429            GTM_SFR_INIT_WRITE32((TomChannelRegPtr->CM1.U),\ 
; ..\mcal_src\Gtm_Platform.c	   430                       TomChannelConfigPtr->GtmTomChannelCfgPtr->GtmTomCm1Value);
; ..\mcal_src\Gtm_Platform.c	   431            GTM_SFR_INIT_WRITE32((TomChannelRegPtr->SR0.U),\ 
; ..\mcal_src\Gtm_Platform.c	   432                       TomChannelConfigPtr->GtmTomChannelCfgPtr->GtmTomSr0Value);
; ..\mcal_src\Gtm_Platform.c	   433            GTM_SFR_INIT_WRITE32((TomChannelRegPtr->SR1.U),\ 
; ..\mcal_src\Gtm_Platform.c	   434                       TomChannelConfigPtr->GtmTomChannelCfgPtr->GtmTomSr1Value);
; ..\mcal_src\Gtm_Platform.c	   435  
; ..\mcal_src\Gtm_Platform.c	   436            GTM_SFR_INIT_WRITE32((TomChannelRegPtr->IRQ_MODE.U),\ 
; ..\mcal_src\Gtm_Platform.c	   437                    (uint32)TomChannelConfigPtr->GtmTomIrqMode);
; ..\mcal_src\Gtm_Platform.c	   438            GTM_SFR_INIT_WRITE32((TomChannelRegPtr->CTRL.U), \ 
; ..\mcal_src\Gtm_Platform.c	   439                         TomChannelConfigPtr->GtmTomControlWord);
; ..\mcal_src\Gtm_Platform.c	   440  
; ..\mcal_src\Gtm_Platform.c	   441            /* Clear the Pending Interrupts */
; ..\mcal_src\Gtm_Platform.c	   442            GTM_SFR_INIT_WRITE32((TomChannelRegPtr->IRQ_NOTIFY.U),\ 
; ..\mcal_src\Gtm_Platform.c	   443                          GTM_CLEAR_INTERRUPT);
; ..\mcal_src\Gtm_Platform.c	   444            GTM_SFR_INIT_WRITE32((TomChannelRegPtr->IRQ_EN.U),\ 
; ..\mcal_src\Gtm_Platform.c	   445                 (uint32)TomChannelConfigPtr->GtmTomChannelCfgPtr->GtmTomIrqEn);
; ..\mcal_src\Gtm_Platform.c	   446  
; ..\mcal_src\Gtm_Platform.c	   447            /* Enable SRN */
; ..\mcal_src\Gtm_Platform.c	   448            GTM_SFR_INIT_MODIFY32(\ 
	sha	d0,#5
.L120:
	ld.a	a4,[a2]8
.L121:
	sh	d6,#-1
.L122:
	ld.hu	d15,[a4]4
	st.w	[a15]12,d15
.L282:
	ld.a	a4,[a2]8
	ld.hu	d15,[a4]6
	st.w	[a15]16,d15
.L283:
	ld.a	a4,[a2]8
	ld.hu	d15,[a4]8
	st.w	[a15]4,d15
.L284:
	ld.a	a4,[a2]8
	ld.hu	d15,[a4]10
	st.w	[a15]8,d15
.L285:
	ld.bu	d15,[a2]1
	st.w	[a15]40,d15
.L286:
	mov	d15,#3
.L287:
	ld.w	d5,[a2]2
	st.w	[a15],d5
.L288:
	st.w	[a15]28,d15
.L289:
	ld.a	a2,[a2]8
	ld.bu	d15,[a2]
	st.w	[a15]32,d15
.L123:
	movh.a	a15,#61444
.L222:
	lea	a15,[a15]@los(0xf0039b80)
	addsc.a	a15,a15,d0,#0
	addsc.a	a15,a15,d6,#2
	fcall	.cocofun_2
.L18:
.L17:
	fcall	.cocofun_4
.L290:
	jlt.u	d0,d15,.L16
.L15:
	add	d3,#1
.L226:
	extr.u	d3,d3,#0,#8
	ld.w	d15,[a10]
.L227:
	jlt.u	d3,d15,.L14
.L13:

; ..\mcal_src\Gtm_Platform.c	   449               (MODULE_SRC.GTM.GTM[0].TOM[ModuleNo][ChannelNo/2U].U),\ 
; ..\mcal_src\Gtm_Platform.c	   450               GTM_SRC_CLEAR_MASK,\ 
; ..\mcal_src\Gtm_Platform.c	   451               (unsigned_int)(GTM_BIT_SET << GTM_BIT_SRE))
; ..\mcal_src\Gtm_Platform.c	   452               
; ..\mcal_src\Gtm_Platform.c	   453          }
; ..\mcal_src\Gtm_Platform.c	   454        }
; ..\mcal_src\Gtm_Platform.c	   455      }
; ..\mcal_src\Gtm_Platform.c	   456    }
; ..\mcal_src\Gtm_Platform.c	   457  }
	ret
.L105:
	
__Gtm_lTomComplexConfig_function_end:
	.size	Gtm_lTomComplexConfig,__Gtm_lTomComplexConfig_function_end-Gtm_lTomComplexConfig
.L58:
	; End of function
	
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_4')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_4
.L36:
.cocofun_4:	.type	func
; Function body .cocofun_4, coco_iter:0
	add	d2,#1
.L224:
	extr.u	d2,d2,#0,#8
.L225:
	mov	d15,#32
.L365:
	add	d0,d4,d2
	fret
.L93:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_3')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_3
.L38:
.cocofun_3:	.type	func
; Function body .cocofun_3, coco_iter:0
	mul	d15,d1,#12
	ld.a	a15,[a15]48
.L359:
	add	d1,#1
.L215:
	extr.u	d1,d1,#0,#8
.L216:
	addsc.a	a2,a15,d15,#0
.L217:
	ld.bu	d15,[a2]
.L360:
	fret
.L88:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_2')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_2
.L40:
.cocofun_2:	.type	func
; Function body .cocofun_2, coco_iter:0
	mov	d0,#3327
	ld.w	d15,[a15]
.L241:
	addih	d0,d0,#32543
	and	d15,d0
.L242:
	insert	d15,d15,#1,#10,#1
	st.w	[a15],d15
.L223:
	fret
.L83:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('.cocofun_1')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
; Function .cocofun_1
.L42:
.cocofun_1:	.type	func
; Function body .cocofun_1, coco_iter:0
	fcall	.cocofun_5
.L214:
	addsc.a	a2,a15,d3,#2
.L347:
	mov	d0,#1
.L348:
	ld.w	d15,[a2]44
.L349:
	sh	d0,d0,d2
.L350:
	and	d0,d15
	fret
.L78:
	; End of function
	.sdecl	'.text.CPU0.Private.DEFAULT_CODE_ROM',code,cluster('Gtm_lTomClockSetting')
	.sect	'.text.CPU0.Private.DEFAULT_CODE_ROM'
	.align	2
	
	.global	Gtm_lTomClockSetting

; ..\mcal_src\Gtm_Platform.c	   458  /*******************************************************************************
; ..\mcal_src\Gtm_Platform.c	   459  ** Syntax : void Gtm_lTomClockSetting(void)                                   **
; ..\mcal_src\Gtm_Platform.c	   460  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   461  ** Service ID:       none                                                     **
; ..\mcal_src\Gtm_Platform.c	   462  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   463  ** Sync/Async:       Synchronous                                              **
; ..\mcal_src\Gtm_Platform.c	   464  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   465  ** Reentrancy:       non-reentrant                                            **
; ..\mcal_src\Gtm_Platform.c	   466  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   467  ** Parameters (in):  None                                                     **
; ..\mcal_src\Gtm_Platform.c	   468  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   469  ** Parameters (out): None                                                     **
; ..\mcal_src\Gtm_Platform.c	   470  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   471  ** Return value:     None                                                     **
; ..\mcal_src\Gtm_Platform.c	   472  **                                                                            **
; ..\mcal_src\Gtm_Platform.c	   473  ** Description :     Function to initialize TOM Clock information             **
; ..\mcal_src\Gtm_Platform.c	   474  *******************************************************************************/
; ..\mcal_src\Gtm_Platform.c	   475  void Gtm_lTomClockSetting(void)
; Function Gtm_lTomClockSetting
.L44:
Gtm_lTomClockSetting:	.type	func

; ..\mcal_src\Gtm_Platform.c	   476  {
; ..\mcal_src\Gtm_Platform.c	   477    const Gtm_TomConfigType *TomChannelConfigPtr;  /* Pointer to TOM Channel    */
; ..\mcal_src\Gtm_Platform.c	   478    Ifx_GTM_TOM_CH_TYPE *TomChannelRegPtr;         /* Ptr to TOM Channel Reg    */
; ..\mcal_src\Gtm_Platform.c	   479    Ifx_GTM_TOM_TGC_TYPE *TomTgcRegPtr;            /* Pointer to TOM TGC Reg    */
; ..\mcal_src\Gtm_Platform.c	   480    uint8 TomCnt;             /* Variable to TOM Channel Initialization Count   */
; ..\mcal_src\Gtm_Platform.c	   481    uint8 MajorCnt;           /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm_Platform.c	   482    uint8 MinorCnt;           /* Variable to maintain Loop Count                */
; ..\mcal_src\Gtm_Platform.c	   483    uint8 ModuleNo;           /* Variable to hold Module Number                 */
; ..\mcal_src\Gtm_Platform.c	   484    uint8 ChannelNo;          /* Variable to hold Channel Number                */
; ..\mcal_src\Gtm_Platform.c	   485    uint8 TgcNumber;          /* Variable to hold TGC Number                    */
; ..\mcal_src\Gtm_Platform.c	   486    uint32 RegVal;
; ..\mcal_src\Gtm_Platform.c	   487    volatile uint32 MajorCountLoopLimit = (((GTM_MAX_TOM_CHANNELS - GTM_ONE_U)/\ 
; ..\mcal_src\Gtm_Platform.c	   488                                                    GTM_BITS_IN_U32) + GTM_ONE_U);
	mov	d15,#1
	sub.a	a10,#8
.L228:
	st.w	[a10],d15
.L295:

; ..\mcal_src\Gtm_Platform.c	   489  
; ..\mcal_src\Gtm_Platform.c	   490  
; ..\mcal_src\Gtm_Platform.c	   491    /* Clock Setting for GPT and PWM Modules */
; ..\mcal_src\Gtm_Platform.c	   492    /* Count to maintain track of the index in TOM Channel Config   */
; ..\mcal_src\Gtm_Platform.c	   493    TomCnt = GTM_ZERO_U;
	mov	d1,#0
.L232:

; ..\mcal_src\Gtm_Platform.c	   494    /* MajorCnt -Count to maintain an index to the GtmTomUsage Array            */
; ..\mcal_src\Gtm_Platform.c	   495    /* MinorCnt -Count to maintain an index to the channels in GtmTomUsage Array*/
; ..\mcal_src\Gtm_Platform.c	   496    for(MajorCnt = GTM_ZERO_U; MajorCnt < MajorCountLoopLimit; MajorCnt++)
	ld.w	d0,[a10]
.L296:
	mov	d3,d1
.L229:
	jeq	d0,#0,.L19
.L20:

; ..\mcal_src\Gtm_Platform.c	   497    {
; ..\mcal_src\Gtm_Platform.c	   498      for(MinorCnt = GTM_ZERO_U;
	mov	d2,#0
.L230:

; ..\mcal_src\Gtm_Platform.c	   499         (((MajorCnt * GTM_BITS_IN_U32)+ MinorCnt) < GTM_MAX_TOM_CHANNELS);
	sh	d4,d3,#5
.L297:
	mov	d15,#32
.L298:
	jge.u	d4,d15,.L21

; ..\mcal_src\Gtm_Platform.c	   500         MinorCnt++)
.L22:

; ..\mcal_src\Gtm_Platform.c	   501      {
; ..\mcal_src\Gtm_Platform.c	   502        /* Check if the TOM channel is configured */
; ..\mcal_src\Gtm_Platform.c	   503        if((Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTomUsage[MajorCnt] &          \ 
	fcall	.cocofun_1
.L299:
	jeq	d0,#0,.L23
.L300:

; ..\mcal_src\Gtm_Platform.c	   504                                         (GTM_BIT_SET << MinorCnt)) > GTM_ZERO_UL)
; ..\mcal_src\Gtm_Platform.c	   505        {
; ..\mcal_src\Gtm_Platform.c	   506          /* MISRA Rule Violation 17.4
; ..\mcal_src\Gtm_Platform.c	   507             Pointer arithmetic other than array indexing used
; ..\mcal_src\Gtm_Platform.c	   508             This cannot be avoided because of Post Build structure */
; ..\mcal_src\Gtm_Platform.c	   509  
; ..\mcal_src\Gtm_Platform.c	   510          /* Get reference to the Channel configuration */
; ..\mcal_src\Gtm_Platform.c	   511          /*IFX_MISRA_RULE_17_04_STATUS= Pointer arithmetic used due to 
; ..\mcal_src\Gtm_Platform.c	   512       PBConfigStructure and is within allowed range.*/
; ..\mcal_src\Gtm_Platform.c	   513          TomChannelConfigPtr =                                                  \ 
; ..\mcal_src\Gtm_Platform.c	   514                   &(Gtm_kConfigPtr->GtmModuleConfigPtr->GtmTomConfigPtr[TomCnt]);
	fcall	.cocofun_3
.L231:

; ..\mcal_src\Gtm_Platform.c	   515  
; ..\mcal_src\Gtm_Platform.c	   516          TomCnt++;
; ..\mcal_src\Gtm_Platform.c	   517          /* Check if the channel is configured for GPT or PWM */
; ..\mcal_src\Gtm_Platform.c	   518         if(TomChannelConfigPtr->TomUsage != GTM_DRIVER_COMPLEX)
	jeq	d15,#0,.L24
.L301:

; ..\mcal_src\Gtm_Platform.c	   519          {
; ..\mcal_src\Gtm_Platform.c	   520            /* Extract module number and channel number from the Loop
; ..\mcal_src\Gtm_Platform.c	   521               Count information */
; ..\mcal_src\Gtm_Platform.c	   522            ModuleNo = ((MajorCnt * GTM_BITS_IN_U32) + MinorCnt)/
; ..\mcal_src\Gtm_Platform.c	   523                                                      GTM_CHANNELS_PER_TOM_MODULE;
; ..\mcal_src\Gtm_Platform.c	   524            ChannelNo = MinorCnt % GTM_CHANNELS_PER_TOM_MODULE;
; ..\mcal_src\Gtm_Platform.c	   525            TgcNumber =                                                          \ 
; ..\mcal_src\Gtm_Platform.c	   526                   (MinorCnt/GTM_TOM_CHANNELS_PER_TGC) % GTM_NO_OF_TGC_PER_MODULE;
; ..\mcal_src\Gtm_Platform.c	   527  
; ..\mcal_src\Gtm_Platform.c	   528            /* MISRA Rule Violation 11.4 and 1.2
; ..\mcal_src\Gtm_Platform.c	   529               Unusual pointer cast (incompatible indirect types)
; ..\mcal_src\Gtm_Platform.c	   530               cast from pointer to pointer
; ..\mcal_src\Gtm_Platform.c	   531               Casting of TOM to Gtm_TomChannelRegType is done to change the base
; ..\mcal_src\Gtm_Platform.c	   532               type of TOM as the type defined in SFR file is not flexible to
; ..\mcal_src\Gtm_Platform.c	   533               provide an arrayed approach for accessing the TOM channels. */
; ..\mcal_src\Gtm_Platform.c	   534  
; ..\mcal_src\Gtm_Platform.c	   535            /* Get reference to the Channel Register */
; ..\mcal_src\Gtm_Platform.c	   536            /*IFX_MISRA_RULE_11_05_STATUS= Volatile in terms of pointer access. 
; ..\mcal_src\Gtm_Platform.c	   537           Permitted for special function registers.*/
; ..\mcal_src\Gtm_Platform.c	   538            TomChannelRegPtr = &((((*(Ifx_GTM_TOMx*)(void *)(MODULE_GTM.TOM)).   \ 
	add	d15,d4,d2
	movh.a	a15,#61457
.L302:
	sh	d8,d15,#-4
	lea	a15,[a15]@los(0xf0108000)
.L233:

; ..\mcal_src\Gtm_Platform.c	   539                                               TOM_CH[ModuleNo])).CH[ChannelNo]);
	sha	d10,d8,#11
.L303:
	addsc.a	a15,a15,d10,#0
.L304:
	sh	d15,d2,#-3
.L305:
	and	d9,d2,#15
.L234:
	and	d7,d15,#1
.L235:
	sha	d15,d9,#6
.L306:
	addsc.a	a15,a15,d15,#0
.L139:

; ..\mcal_src\Gtm_Platform.c	   540  
; ..\mcal_src\Gtm_Platform.c	   541  
; ..\mcal_src\Gtm_Platform.c	   542            /*Set the CTRL value*/
; ..\mcal_src\Gtm_Platform.c	   543            GTM_SFR_INIT_USER_MODE_MODIFY32((TomChannelRegPtr->CTRL.U),\ 
	mov	d5,#2048
	ld.w	d0,[a15]
	addih	d5,d5,#3568
	and	d0,d5
	ld.w	d5,[a2]2
	mov	d6,#28672
	and	d5,d6
.L237:
	or	d0,d5
	st.w	[a15],d0
.L140:

; ..\mcal_src\Gtm_Platform.c	   544              GTM_TOM_CH_CTRL_CLK_CLR_MASK,\ 
; ..\mcal_src\Gtm_Platform.c	   545            ((uint32)(TomChannelConfigPtr->GtmTomControlWord) & \ 
; ..\mcal_src\Gtm_Platform.c	   546               GTM_TOM_CH_CTRL_CLK_MASK))
; ..\mcal_src\Gtm_Platform.c	   547  
; ..\mcal_src\Gtm_Platform.c	   548  
; ..\mcal_src\Gtm_Platform.c	   549            /*Set the IRQ_MODE value*/
; ..\mcal_src\Gtm_Platform.c	   550  
; ..\mcal_src\Gtm_Platform.c	   551            GTM_SFR_INIT_USER_MODE_WRITE32((TomChannelRegPtr->IRQ_MODE.U),\ 
; ..\mcal_src\Gtm_Platform.c	   552                                TomChannelConfigPtr->GtmTomIrqMode);
; ..\mcal_src\Gtm_Platform.c	   553            /*Get the TGC reg pointer reference*/
; ..\mcal_src\Gtm_Platform.c	   554            /*IFX_MISRA_RULE_11_05_STATUS= Volatile in terms of pointer access. 
; ..\mcal_src\Gtm_Platform.c	   555           Permitted for special function registers.*/
; ..\mcal_src\Gtm_Platform.c	   556            TomTgcRegPtr = &(((*(Ifx_GTM_TOMx*)(void *)(MODULE_GTM.TOM)).
; ..\mcal_src\Gtm_Platform.c	   557                                  TOM_TGC[ModuleNo].TGC[TgcNumber]));
; ..\mcal_src\Gtm_Platform.c	   558            /*Set the FUPD_CTRL value*/
; ..\mcal_src\Gtm_Platform.c	   559            RegVal = (uint32)(GTM_TOM_ENABLE << (GTM_TOM_BITS_PER_CHAN * \ 
	mov	d0,#2
.L238:
	ld.bu	d15,[a2]1
	st.w	[a15]40,d15
.L307:
	movh.a	a15,#61457
.L236:
	sha	d15,d7,#9
	lea	a15,[a15]@los(0xf0108030)
	addsc.a	a15,a15,d10,#0
.L143:

; ..\mcal_src\Gtm_Platform.c	   560                           (ChannelNo % GTM_TOM_CHANNELS_PER_TGC)));
; ..\mcal_src\Gtm_Platform.c	   561            GTM_SFR_INIT_USER_MODE_MODIFY32((TomTgcRegPtr->FUPD_CTRL.U),\ 
; ..\mcal_src\Gtm_Platform.c	   562                         GTM_WHOLE_REG,RegVal)
; ..\mcal_src\Gtm_Platform.c	   563            /* Enable SRN */
; ..\mcal_src\Gtm_Platform.c	   564            GTM_SFR_INIT_MODIFY32((\ 
	sha	d8,#5
.L144:
	addsc.a	a15,a15,d15,#0
.L239:
	and	d15,d9,#7
	sh	d15,#1
.L308:
	sh	d0,d0,d15
	ld.w	d15,[a15]8
.L145:
	sh	d9,#-1
.L146:
	or	d15,d0
	st.w	[a15]8,d15
.L147:
	movh.a	a15,#61444
.L240:
	lea	a15,[a15]@los(0xf0039b80)
	addsc.a	a15,a15,d8,#0
	addsc.a	a15,a15,d9,#2
	fcall	.cocofun_2
.L24:
.L23:
	fcall	.cocofun_4
.L243:
	jlt.u	d0,d15,.L22
.L21:
	add	d3,#1
.L244:
	extr.u	d3,d3,#0,#8
	ld.w	d15,[a10]
.L245:
	jlt.u	d3,d15,.L20
.L19:

; ..\mcal_src\Gtm_Platform.c	   565                 MODULE_SRC.GTM.GTM[0].TOM[ModuleNo][ChannelNo/2U].U),\ 
; ..\mcal_src\Gtm_Platform.c	   566                 GTM_SRC_CLEAR_MASK,\ 
; ..\mcal_src\Gtm_Platform.c	   567                 (unsigned_int)(GTM_BIT_SET << GTM_BIT_SRE))
; ..\mcal_src\Gtm_Platform.c	   568          }
; ..\mcal_src\Gtm_Platform.c	   569        }
; ..\mcal_src\Gtm_Platform.c	   570      }
; ..\mcal_src\Gtm_Platform.c	   571    }
; ..\mcal_src\Gtm_Platform.c	   572  }
	ret
.L125:
	
__Gtm_lTomClockSetting_function_end:
	.size	Gtm_lTomClockSetting,__Gtm_lTomClockSetting_function_end-Gtm_lTomClockSetting
.L63:
	; End of function
	
	.calls	'Gtm_lTimConfigure','.cocofun_5'
	.calls	'Gtm_lAdcConnectionsConfig','.cocofun_5'
	.calls	'Gtm_lTomComplexConfig','.cocofun_1'
	.calls	'Gtm_lTomComplexConfig','.cocofun_3'
	.calls	'Gtm_lTomComplexConfig','.cocofun_2'
	.calls	'Gtm_lTomComplexConfig','.cocofun_4'
	.calls	'.cocofun_1','.cocofun_5'
	.calls	'Gtm_lTomClockSetting','.cocofun_1'
	.calls	'Gtm_lTomClockSetting','.cocofun_3'
	.calls	'Gtm_lTomClockSetting','.cocofun_2'
	.calls	'Gtm_lTomClockSetting','.cocofun_4'
	.calls	'Gtm_lTimConfigure','',0
	.calls	'.cocofun_5','',0
	.calls	'Gtm_lAdcConnectionsConfig','',0
	.calls	'Gtm_lResetGtmSRCReg','',0
	.calls	'Gtm_lTomComplexConfig','',8
	.calls	'.cocofun_4','',0
	.calls	'.cocofun_3','',0
	.calls	'.cocofun_2','',0
	.calls	'.cocofun_1','',0
	.extern	Gtm_kConfigPtr
	.calls	'Gtm_lTomClockSetting','',8
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L46:
	.word	42851
	.half	3
	.word	.L47
	.byte	4
.L45:
	.byte	1
	.byte	'..\\mcal_src\\Gtm_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L48
.L100:
	.byte	2
	.byte	'unsigned long int',0,4,7
.L103:
	.byte	2
	.byte	'unsigned char',0,1,8,3,1,211,6,9,12,4
	.byte	'TomUsage',0,1
	.word	202
	.byte	2,35,0,4
	.byte	'GtmTomIrqMode',0,1
	.word	202
	.byte	2,35,1,4
	.byte	'GtmTomControlWord',0,4
	.word	181
	.byte	2,35,2,3,1,199,6,9,12,4
	.byte	'GtmTomIrqEn',0,1
	.word	202
	.byte	2,35,0,2
	.byte	'unsigned short int',0,2,7,4
	.byte	'GtmTomCn0Value',0,2
	.word	320
	.byte	2,35,2,4
	.byte	'GtmTomCm0Value',0,2
	.word	320
	.byte	2,35,4,4
	.byte	'GtmTomCm1Value',0,2
	.word	320
	.byte	2,35,6,4
	.byte	'GtmTomSr0Value',0,2
	.word	320
	.byte	2,35,8,4
	.byte	'GtmTomSr1Value',0,2
	.word	320
	.byte	2,35,10,0,5
	.word	293
	.byte	6
	.word	463
	.byte	4
	.byte	'GtmTomChannelCfgPtr',0,4
	.word	468
	.byte	2,35,8,0,5
	.word	219
.L107:
	.byte	6
	.word	503
	.byte	3,1,130,4,20,64,7,2,148,15,9,4,2
	.byte	'unsigned int',0,4,7,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,2
	.byte	'int',0,4,5,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_CH_CTRL_Bits',0,2,203,7,16,4,2
	.byte	'unsigned int',0,4,7,9
	.byte	'reserved_0',0,4
	.word	602
	.byte	11,21,2,35,0,9
	.byte	'SL',0,4
	.word	602
	.byte	1,20,2,35,0,9
	.byte	'CLK_SRC_SR',0,4
	.word	602
	.byte	3,17,2,35,0,9
	.byte	'reserved_15',0,4
	.word	602
	.byte	5,12,2,35,0,9
	.byte	'RST_CCU0',0,4
	.word	602
	.byte	1,11,2,35,0,9
	.byte	'OSM_TRIG',0,4
	.word	602
	.byte	1,10,2,35,0,9
	.byte	'EXT_TRIG',0,4
	.word	602
	.byte	1,9,2,35,0,9
	.byte	'EXTTRIGOUT',0,4
	.word	602
	.byte	1,8,2,35,0,9
	.byte	'TRIGOUT',0,4
	.word	602
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	602
	.byte	1,6,2,35,0,9
	.byte	'OSM',0,4
	.word	602
	.byte	1,5,2,35,0,9
	.byte	'BITREV',0,4
	.word	602
	.byte	1,4,2,35,0,9
	.byte	'reserved_28',0,4
	.word	602
	.byte	4,0,2,35,0,0,4
	.byte	'B',0,4
	.word	570
	.byte	2,35,0,0,4
	.byte	'CTRL',0,4
	.word	519
	.byte	2,35,0,7,2,188,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_CH_SR0_Bits',0,2,252,7,16,4,9
	.byte	'SR0',0,4
	.word	602
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,4
	.byte	'B',0,4
	.word	934
	.byte	2,35,0,0,4
	.byte	'SR0',0,4
	.word	906
	.byte	2,35,4,7,2,196,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_CH_SR1_Bits',0,2,131,8,16,4,9
	.byte	'SR1',0,4
	.word	602
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,4
	.byte	'B',0,4
	.word	1057
	.byte	2,35,0,0,4
	.byte	'SR1',0,4
	.word	1029
	.byte	2,35,8,7,2,252,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_CH_CM0_Bits',0,2,182,7,16,4,9
	.byte	'CM0',0,4
	.word	602
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,4
	.byte	'B',0,4
	.word	1180
	.byte	2,35,0,0,4
	.byte	'CM0',0,4
	.word	1152
	.byte	2,35,12,7,2,132,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_CH_CM1_Bits',0,2,189,7,16,4,9
	.byte	'CM1',0,4
	.word	602
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,4
	.byte	'B',0,4
	.word	1303
	.byte	2,35,0,0,4
	.byte	'CM1',0,4
	.word	1275
	.byte	2,35,16,7,2,140,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_CH_CN0_Bits',0,2,196,7,16,4,9
	.byte	'CN0',0,4
	.word	602
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,4
	.byte	'B',0,4
	.word	1426
	.byte	2,35,0,0,4
	.byte	'CN0',0,4
	.word	1398
	.byte	2,35,20,7,2,204,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_CH_STAT_Bits',0,2,138,8,16,4,9
	.byte	'OL',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	602
	.byte	31,0,2,35,0,0,4
	.byte	'B',0,4
	.word	1549
	.byte	2,35,0,0,4
	.byte	'STAT',0,4
	.word	1521
	.byte	2,35,24,7,2,180,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,2,244,7,16,4,9
	.byte	'CCU0TC',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'CCU1TC',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	602
	.byte	30,0,2,35,0,0,4
	.byte	'B',0,4
	.word	1672
	.byte	2,35,0,0,4
	.byte	'IRQ_NOTIFY',0,4
	.word	1644
	.byte	2,35,28,7,2,156,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,2,221,7,16,4,9
	.byte	'CCU0TC_IRQ_EN',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'CCU1TC_IRQ_EN',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	602
	.byte	30,0,2,35,0,0,4
	.byte	'B',0,4
	.word	1829
	.byte	2,35,0,0,4
	.byte	'IRQ_EN',0,4
	.word	1801
	.byte	2,35,32,7,2,164,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,2,229,7,16,4,9
	.byte	'TRG_CCU0TC0',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'TRG_CCU1TC0',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	602
	.byte	30,0,2,35,0,0,4
	.byte	'B',0,4
	.word	1992
	.byte	2,35,0,0,4
	.byte	'IRQ_FORCINT',0,4
	.word	1964
	.byte	2,35,36,7,2,172,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,2,237,7,16,4,9
	.byte	'IRQ_MODE',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	602
	.byte	30,0,2,35,0,0,4
	.byte	'B',0,4
	.word	2161
	.byte	2,35,0,0,4
	.byte	'IRQ_MODE',0,4
	.word	2133
	.byte	2,35,40,10,20
	.word	202
	.byte	11,19,0,4
	.byte	'reserved_2C',0,20
	.word	2270
	.byte	2,35,44,0,12
	.word	513
.L109:
	.byte	6
	.word	2301
.L116:
	.byte	12
	.word	181
	.byte	3,1,157,4,20,128,4,7,2,244,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,2,203,8,16,4,9
	.byte	'HOST_TRIG',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	602
	.byte	7,24,2,35,0,9
	.byte	'RST_CH0',0,4
	.word	602
	.byte	1,23,2,35,0,9
	.byte	'RST_CH1',0,4
	.word	602
	.byte	1,22,2,35,0,9
	.byte	'RST_CH2',0,4
	.word	602
	.byte	1,21,2,35,0,9
	.byte	'RST_CH3',0,4
	.word	602
	.byte	1,20,2,35,0,9
	.byte	'RST_CH4',0,4
	.word	602
	.byte	1,19,2,35,0,9
	.byte	'RST_CH5',0,4
	.word	602
	.byte	1,18,2,35,0,9
	.byte	'RST_CH6',0,4
	.word	602
	.byte	1,17,2,35,0,9
	.byte	'RST_CH7',0,4
	.word	602
	.byte	1,16,2,35,0,9
	.byte	'UPEN_CTRL0',0,4
	.word	602
	.byte	2,14,2,35,0,9
	.byte	'UPEN_CTRL1',0,4
	.word	602
	.byte	2,12,2,35,0,9
	.byte	'UPEN_CTRL2',0,4
	.word	602
	.byte	2,10,2,35,0,9
	.byte	'UPEN_CTRL3',0,4
	.word	602
	.byte	2,8,2,35,0,9
	.byte	'UPEN_CTRL4',0,4
	.word	602
	.byte	2,6,2,35,0,9
	.byte	'UPEN_CTRL5',0,4
	.word	602
	.byte	2,4,2,35,0,9
	.byte	'UPEN_CTRL6',0,4
	.word	602
	.byte	2,2,2,35,0,9
	.byte	'UPEN_CTRL7',0,4
	.word	602
	.byte	2,0,2,35,0,0,4
	.byte	'B',0,4
	.word	2351
	.byte	2,35,0,0,4
	.byte	'GLB_CTRL',0,4
	.word	2323
	.byte	2,35,0,7,2,212,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,2,145,8,16,4,9
	.byte	'ACT_TB',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'TB_TRIG',0,4
	.word	602
	.byte	1,7,2,35,0,9
	.byte	'TBU_SEL',0,4
	.word	602
	.byte	2,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	602
	.byte	5,0,2,35,0,0,4
	.byte	'B',0,4
	.word	2819
	.byte	2,35,0,0,4
	.byte	'ACT_TB',0,4
	.word	2791
	.byte	2,35,4,7,2,236,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,2,182,8,16,4,9
	.byte	'FUPD_CTRL0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'FUPD_CTRL1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'FUPD_CTRL2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'FUPD_CTRL3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'FUPD_CTRL4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'FUPD_CTRL5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'FUPD_CTRL6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'FUPD_CTRL7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'RSTCN0_CH0',0,4
	.word	602
	.byte	2,14,2,35,0,9
	.byte	'RSTCN0_CH1',0,4
	.word	602
	.byte	2,12,2,35,0,9
	.byte	'RSTCN0_CH2',0,4
	.word	602
	.byte	2,10,2,35,0,9
	.byte	'RSTCN0_CH3',0,4
	.word	602
	.byte	2,8,2,35,0,9
	.byte	'RSTCN0_CH4',0,4
	.word	602
	.byte	2,6,2,35,0,9
	.byte	'RSTCN0_CH5',0,4
	.word	602
	.byte	2,4,2,35,0,9
	.byte	'RSTCN0_CH6',0,4
	.word	602
	.byte	2,2,2,35,0,9
	.byte	'RSTCN0_CH7',0,4
	.word	602
	.byte	2,0,2,35,0,0,4
	.byte	'B',0,4
	.word	2991
	.byte	2,35,0,0,4
	.byte	'FUPD_CTRL',0,4
	.word	2963
	.byte	2,35,8,7,2,252,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,2,226,8,16,4,9
	.byte	'INT_TRIG0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'INT_TRIG1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'INT_TRIG2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'INT_TRIG3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'INT_TRIG4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'INT_TRIG5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'INT_TRIG6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'INT_TRIG7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,4
	.byte	'B',0,4
	.word	3442
	.byte	2,35,0,0,4
	.byte	'INT_TRIG',0,4
	.word	3414
	.byte	2,35,12,10,48
	.word	202
	.byte	11,47,0,4
	.byte	'reserved_tgc0',0,48
	.word	3702
	.byte	2,35,16,7,2,220,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,2,154,8,16,4,9
	.byte	'ENDIS_CTRL0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_CTRL1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_CTRL2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'ENDIS_CTRL3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'ENDIS_CTRL4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'ENDIS_CTRL5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'ENDIS_CTRL6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'ENDIS_CTRL7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,4
	.byte	'B',0,4
	.word	3762
	.byte	2,35,0,0,4
	.byte	'ENDIS_CTRL',0,4
	.word	3734
	.byte	2,35,64,7,2,228,15,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,2,168,8,16,4,9
	.byte	'ENDIS_STAT0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_STAT1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_STAT2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'ENDIS_STAT3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'ENDIS_STAT4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'ENDIS_STAT5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'ENDIS_STAT6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'ENDIS_STAT7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,4
	.byte	'B',0,4
	.word	4070
	.byte	2,35,0,0,4
	.byte	'ENDIS_STAT',0,4
	.word	4042
	.byte	2,35,68,7,2,132,16,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,2,240,8,16,4,9
	.byte	'OUTEN_CTRL0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'OUTEN_CTRL1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'OUTEN_CTRL2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'OUTEN_CTRL3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'OUTEN_CTRL4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'OUTEN_CTRL5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'OUTEN_CTRL6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'OUTEN_CTRL7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,4
	.byte	'B',0,4
	.word	4378
	.byte	2,35,0,0,4
	.byte	'OUTEN_CTRL',0,4
	.word	4350
	.byte	2,35,72,7,2,140,16,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,2,254,8,16,4,9
	.byte	'OUTEN_STAT0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'OUTEN_STAT1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'OUTEN_STAT2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'OUTEN_STAT3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'OUTEN_STAT4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'OUTEN_STAT5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'OUTEN_STAT6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'OUTEN_STAT7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,4
	.byte	'B',0,4
	.word	4686
	.byte	2,35,0,0,4
	.byte	'OUTEN_STAT',0,4
	.word	4658
	.byte	2,35,76,10,176,3
	.word	202
	.byte	11,175,3,0,4
	.byte	'reserved_tgc1',0,176,3
	.word	4966
	.byte	2,35,80,0,12
	.word	2316
.L128:
	.byte	6
	.word	5002
.L137:
	.byte	12
	.word	181
	.byte	3,1,138,6,11,24,4
	.byte	'TimUsage',0,1
	.word	202
	.byte	2,35,0,4
	.byte	'TimIrqEn',0,1
	.word	202
	.byte	2,35,1,4
	.byte	'TimErrIrqEn',0,1
	.word	202
	.byte	2,35,2,4
	.byte	'TimExtCapSrc',0,1
	.word	202
	.byte	2,35,3,4
	.byte	'TimCtrlValue',0,4
	.word	181
	.byte	2,35,4,3,1,129,6,9,8,4
	.byte	'TimRisingEdgeFilter',0,4
	.word	181
	.byte	2,35,0,4
	.byte	'TimFallingEdgeFilter',0,4
	.word	181
	.byte	2,35,4,0,5
	.word	5124
	.byte	6
	.word	5190
	.byte	4
	.byte	'GtmTimFltPtr',0,4
	.word	5195
	.byte	2,35,8,4
	.byte	'TimCntsValue',0,4
	.word	181
	.byte	2,35,12,4
	.byte	'TimTduValue',0,4
	.word	181
	.byte	2,35,16,4
	.byte	'TimInSrcSel',0,4
	.word	181
	.byte	2,35,20,0,5
	.word	5017
.L152:
	.byte	6
	.word	5287
	.byte	3,1,212,4,20,128,1,7,2,164,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_GPR0_Bits',0,2,193,6,16,4,9
	.byte	'GPR0',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'ECNT',0,4
	.word	602
	.byte	8,0,2,35,0,0,4
	.byte	'B',0,4
	.word	5332
	.byte	2,35,0,0,4
	.byte	'CH_GPR0',0,4
	.word	5304
	.byte	2,35,0,7,2,172,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_GPR1_Bits',0,2,200,6,16,4,9
	.byte	'GPR1',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'ECNT',0,4
	.word	602
	.byte	8,0,2,35,0,0,4
	.byte	'B',0,4
	.word	5454
	.byte	2,35,0,0,4
	.byte	'CH_GPR1',0,4
	.word	5426
	.byte	2,35,4,7,2,228,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_CNT_Bits',0,2,238,5,16,4,9
	.byte	'CNT',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	8,0,2,35,0,0,4
	.byte	'B',0,4
	.word	5576
	.byte	2,35,0,0,4
	.byte	'CH_CNT',0,4
	.word	5548
	.byte	2,35,8,7,2,252,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_ECNT_Bits',0,2,153,6,16,4,9
	.byte	'ECNT',0,4
	.word	602
	.byte	16,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,4
	.byte	'B',0,4
	.word	5702
	.byte	2,35,0,0,4
	.byte	'CH_ECNT',0,4
	.word	5674
	.byte	2,35,12,7,2,236,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_CNTS_Bits',0,2,245,5,16,4,9
	.byte	'CNTS',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'ECNT',0,4
	.word	602
	.byte	8,0,2,35,0,0,4
	.byte	'B',0,4
	.word	5831
	.byte	2,35,0,0,4
	.byte	'CH_CNTS',0,4
	.word	5803
	.byte	2,35,16,7,2,212,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_TDUC_Bits',0,2,250,6,16,4,9
	.byte	'TO_CNT',0,4
	.word	602
	.byte	8,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	602
	.byte	24,0,2,35,0,0,4
	.byte	'B',0,4
	.word	5953
	.byte	2,35,0,0,4
	.byte	'CH_TDUC',0,4
	.word	5925
	.byte	2,35,20,7,2,220,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_TDUV_Bits',0,2,129,7,16,4,9
	.byte	'TOV',0,4
	.word	602
	.byte	8,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	602
	.byte	20,4,2,35,0,9
	.byte	'TCS',0,4
	.word	602
	.byte	3,1,2,35,0,9
	.byte	'reserved_31',0,4
	.word	602
	.byte	1,0,2,35,0,0,4
	.byte	'B',0,4
	.word	6083
	.byte	2,35,0,0,4
	.byte	'CH_TDUV',0,4
	.word	6055
	.byte	2,35,24,7,2,156,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_FLT_RE_Bits',0,2,186,6,16,4,9
	.byte	'FLT_RE',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	8,0,2,35,0,0,4
	.byte	'B',0,4
	.word	6248
	.byte	2,35,0,0,4
	.byte	'CH_FLT_RE',0,4
	.word	6220
	.byte	2,35,28,7,2,148,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_FLT_FE_Bits',0,2,179,6,16,4,9
	.byte	'FLT_FE',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	8,0,2,35,0,0,4
	.byte	'B',0,4
	.word	6383
	.byte	2,35,0,0,4
	.byte	'CH_FLT_FE',0,4
	.word	6355
	.byte	2,35,32,7,2,244,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_CTRL_Bits',0,2,252,5,16,4,9
	.byte	'TIM_EN',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'TIM_MODE',0,4
	.word	602
	.byte	3,28,2,35,0,9
	.byte	'OSM',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'CICTRL',0,4
	.word	602
	.byte	1,25,2,35,0,9
	.byte	'TBU0x_SEL',0,4
	.word	602
	.byte	1,24,2,35,0,9
	.byte	'GPR0_SEL',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'GPR1_SEL',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'CNTS_SEL',0,4
	.word	602
	.byte	1,19,2,35,0,9
	.byte	'DSL',0,4
	.word	602
	.byte	1,18,2,35,0,9
	.byte	'ISL',0,4
	.word	602
	.byte	1,17,2,35,0,9
	.byte	'ECNT_RESET',0,4
	.word	602
	.byte	1,16,2,35,0,9
	.byte	'FLT_EN',0,4
	.word	602
	.byte	1,15,2,35,0,9
	.byte	'FLT_CNT_FRQ',0,4
	.word	602
	.byte	2,13,2,35,0,9
	.byte	'EXT_CAP_EN',0,4
	.word	602
	.byte	1,12,2,35,0,9
	.byte	'FLT_MODE_RE',0,4
	.word	602
	.byte	1,11,2,35,0,9
	.byte	'FLT_CTR_RE',0,4
	.word	602
	.byte	1,10,2,35,0,9
	.byte	'FLT_MODE_FE',0,4
	.word	602
	.byte	1,9,2,35,0,9
	.byte	'FLT_CTR_FE',0,4
	.word	602
	.byte	1,8,2,35,0,9
	.byte	'CLK_SEL',0,4
	.word	602
	.byte	3,5,2,35,0,9
	.byte	'FR_ECNT_OFL',0,4
	.word	602
	.byte	1,4,2,35,0,9
	.byte	'EGPR0_SEL',0,4
	.word	602
	.byte	1,3,2,35,0,9
	.byte	'EGPR1_SEL',0,4
	.word	602
	.byte	1,2,2,35,0,9
	.byte	'TOCTRL',0,4
	.word	602
	.byte	2,0,2,35,0,0,4
	.byte	'B',0,4
	.word	6518
	.byte	2,35,0,0,4
	.byte	'CH_CTRL',0,4
	.word	6490
	.byte	2,35,36,7,2,132,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_ECTRL_Bits',0,2,160,6,16,4,9
	.byte	'EXT_CAP_SRC',0,4
	.word	602
	.byte	3,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	602
	.byte	29,0,2,35,0,0,4
	.byte	'B',0,4
	.word	7089
	.byte	2,35,0,0,4
	.byte	'CH_ECTRL',0,4
	.word	7061
	.byte	2,35,40,7,2,204,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,2,238,6,16,4,9
	.byte	'NEWVAL',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'ECNTOFL',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'CNTOFL',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'GPRzOFL',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'TODET',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'GLITCHDET',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	602
	.byte	26,0,2,35,0,0,4
	.byte	'B',0,4
	.word	7226
	.byte	2,35,0,0,4
	.byte	'CH_IRQ_NOTIFY',0,4
	.word	7198
	.byte	2,35,44,7,2,180,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,2,207,6,16,4,9
	.byte	'NEWVAL_IRQ_EN',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'ECNTOFL_IRQ_EN',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'CNTOFL_IRQ_EN',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'GPRzOFL_IRQ_EN',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'TODET_IRQ_EN',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'GLITCHDET_IRQ_EN',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	602
	.byte	26,0,2,35,0,0,4
	.byte	'B',0,4
	.word	7462
	.byte	2,35,0,0,4
	.byte	'CH_IRQ_EN',0,4
	.word	7434
	.byte	2,35,48,7,2,188,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,2,219,6,16,4,9
	.byte	'TRG_NEWVAL',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'TRG_ECNTOFL',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'TRG_CNTOFL',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'TRG_GPRzOFL',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'TRG_TODET',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'TRG_GLITCHDET',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	602
	.byte	26,0,2,35,0,0,4
	.byte	'B',0,4
	.word	7732
	.byte	2,35,0,0,4
	.byte	'CH_IRQ_FORCINT',0,4
	.word	7704
	.byte	2,35,52,7,2,196,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,2,231,6,16,4,9
	.byte	'IRQ_MODE',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	602
	.byte	30,0,2,35,0,0,4
	.byte	'B',0,4
	.word	7994
	.byte	2,35,0,0,4
	.byte	'CH_IRQ_MODE',0,4
	.word	7966
	.byte	2,35,56,7,2,140,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,8
	.byte	'_Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,2,167,6,16,4,9
	.byte	'NEWVAL_EIRQ_EN',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'ECNTOFL_EIRQ_EN',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'CNTOFL_EIRQ_EN',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'GPRzOFL_EIRQ_EN',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'TODET_EIRQ_EN',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'GLITCHDET_EIRQ_EN',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	602
	.byte	26,0,2,35,0,0,4
	.byte	'B',0,4
	.word	8134
	.byte	2,35,0,0,4
	.byte	'CH_EIRQ_EN',0,4
	.word	8106
	.byte	2,35,60,10,64
	.word	202
	.byte	11,63,0,4
	.byte	'reserved_40',0,64
	.word	8384
	.byte	2,35,64,0,12
	.word	5297
.L154:
	.byte	6
	.word	8415
	.byte	13
	.byte	'void',0,6
	.word	8425
	.byte	14
	.byte	'__prof_adm',0,3,1,1
	.word	8431
	.byte	15,1,6
	.word	8455
	.byte	14
	.byte	'__codeptr',0,3,1,1
	.word	8457
	.byte	14
	.byte	'uint8',0,4,90,29
	.word	202
	.byte	14
	.byte	'uint16',0,4,92,29
	.word	320
	.byte	14
	.byte	'uint32',0,4,94,29
	.word	181
	.byte	14
	.byte	'boolean',0,4,105,29
	.word	202
	.byte	8
	.byte	'_Ifx_GTM_ACCEN0_Bits',0,2,45,16,4,9
	.byte	'EN0',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'EN1',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'EN2',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'EN3',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'EN4',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'EN5',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'EN6',0,4
	.word	602
	.byte	1,25,2,35,0,9
	.byte	'EN7',0,4
	.word	602
	.byte	1,24,2,35,0,9
	.byte	'EN8',0,4
	.word	602
	.byte	1,23,2,35,0,9
	.byte	'EN9',0,4
	.word	602
	.byte	1,22,2,35,0,9
	.byte	'EN10',0,4
	.word	602
	.byte	1,21,2,35,0,9
	.byte	'EN11',0,4
	.word	602
	.byte	1,20,2,35,0,9
	.byte	'EN12',0,4
	.word	602
	.byte	1,19,2,35,0,9
	.byte	'EN13',0,4
	.word	602
	.byte	1,18,2,35,0,9
	.byte	'EN14',0,4
	.word	602
	.byte	1,17,2,35,0,9
	.byte	'EN15',0,4
	.word	602
	.byte	1,16,2,35,0,9
	.byte	'EN16',0,4
	.word	602
	.byte	1,15,2,35,0,9
	.byte	'EN17',0,4
	.word	602
	.byte	1,14,2,35,0,9
	.byte	'EN18',0,4
	.word	602
	.byte	1,13,2,35,0,9
	.byte	'EN19',0,4
	.word	602
	.byte	1,12,2,35,0,9
	.byte	'EN20',0,4
	.word	602
	.byte	1,11,2,35,0,9
	.byte	'EN21',0,4
	.word	602
	.byte	1,10,2,35,0,9
	.byte	'EN22',0,4
	.word	602
	.byte	1,9,2,35,0,9
	.byte	'EN23',0,4
	.word	602
	.byte	1,8,2,35,0,9
	.byte	'EN24',0,4
	.word	602
	.byte	1,7,2,35,0,9
	.byte	'EN25',0,4
	.word	602
	.byte	1,6,2,35,0,9
	.byte	'EN26',0,4
	.word	602
	.byte	1,5,2,35,0,9
	.byte	'EN27',0,4
	.word	602
	.byte	1,4,2,35,0,9
	.byte	'EN28',0,4
	.word	602
	.byte	1,3,2,35,0,9
	.byte	'EN29',0,4
	.word	602
	.byte	1,2,2,35,0,9
	.byte	'EN30',0,4
	.word	602
	.byte	1,1,2,35,0,9
	.byte	'EN31',0,4
	.word	602
	.byte	1,0,2,35,0,0,14
	.byte	'Ifx_GTM_ACCEN0_Bits',0,2,79,3
	.word	8540
	.byte	8
	.byte	'_Ifx_GTM_ACCEN1_Bits',0,2,82,16,4,9
	.byte	'reserved_0',0,4
	.word	602
	.byte	32,0,2,35,0,0,14
	.byte	'Ifx_GTM_ACCEN1_Bits',0,2,85,3
	.word	9097
	.byte	8
	.byte	'_Ifx_GTM_ADCTRIG0OUT0_Bits',0,2,88,16,4,9
	.byte	'SEL0',0,4
	.word	602
	.byte	4,28,2,35,0,9
	.byte	'SEL1',0,4
	.word	602
	.byte	4,24,2,35,0,9
	.byte	'SEL2',0,4
	.word	602
	.byte	4,20,2,35,0,9
	.byte	'SEL3',0,4
	.word	602
	.byte	4,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,14
	.byte	'Ifx_GTM_ADCTRIG0OUT0_Bits',0,2,95,3
	.word	9174
	.byte	8
	.byte	'_Ifx_GTM_ADCTRIG1OUT0_Bits',0,2,98,16,4,9
	.byte	'SEL0',0,4
	.word	602
	.byte	4,28,2,35,0,9
	.byte	'SEL1',0,4
	.word	602
	.byte	4,24,2,35,0,9
	.byte	'SEL2',0,4
	.word	602
	.byte	4,20,2,35,0,9
	.byte	'SEL3',0,4
	.word	602
	.byte	4,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,14
	.byte	'Ifx_GTM_ADCTRIG1OUT0_Bits',0,2,105,3
	.word	9328
	.byte	8
	.byte	'_Ifx_GTM_AEI_ADDR_XPT_Bits',0,2,108,16,4,9
	.byte	'TO_ADDR',0,4
	.word	602
	.byte	20,12,2,35,0,9
	.byte	'TO_W1R0',0,4
	.word	602
	.byte	1,11,2,35,0,9
	.byte	'reserved_21',0,4
	.word	602
	.byte	11,0,2,35,0,0,14
	.byte	'Ifx_GTM_AEI_ADDR_XPT_Bits',0,2,113,3
	.word	9482
	.byte	8
	.byte	'_Ifx_GTM_BRIDGE_MODE_Bits',0,2,116,16,4,9
	.byte	'BRG_MODE',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'MSK_WR_RSP',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	602
	.byte	6,24,2,35,0,9
	.byte	'MODE_UP_PGR',0,4
	.word	602
	.byte	1,23,2,35,0,9
	.byte	'BUFF_OVL',0,4
	.word	602
	.byte	1,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'SYNC_INPUT_REG',0,4
	.word	602
	.byte	1,19,2,35,0,9
	.byte	'reserved_13',0,4
	.word	602
	.byte	3,16,2,35,0,9
	.byte	'BRG_RST',0,4
	.word	602
	.byte	1,15,2,35,0,9
	.byte	'reserved_17',0,4
	.word	602
	.byte	7,8,2,35,0,9
	.byte	'BUFF_DPT',0,4
	.word	602
	.byte	8,0,2,35,0,0,14
	.byte	'Ifx_GTM_BRIDGE_MODE_Bits',0,2,129,1,3
	.word	9610
	.byte	8
	.byte	'_Ifx_GTM_BRIDGE_PTR1_Bits',0,2,132,1,16,4,9
	.byte	'NEW_TRAN_PTR',0,4
	.word	602
	.byte	5,27,2,35,0,9
	.byte	'FIRST_RSP_PTR',0,4
	.word	602
	.byte	5,22,2,35,0,9
	.byte	'TRAN_IN_PGR',0,4
	.word	602
	.byte	5,17,2,35,0,9
	.byte	'ABT_TRAN_PGR',0,4
	.word	602
	.byte	5,12,2,35,0,9
	.byte	'FBC',0,4
	.word	602
	.byte	6,6,2,35,0,9
	.byte	'RSP_TRAN_RDY',0,4
	.word	602
	.byte	6,0,2,35,0,0,14
	.byte	'Ifx_GTM_BRIDGE_PTR1_Bits',0,2,140,1,3
	.word	9917
	.byte	8
	.byte	'_Ifx_GTM_BRIDGE_PTR2_Bits',0,2,143,1,16,4,9
	.byte	'TRAN_IN_PGR2',0,4
	.word	602
	.byte	5,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	602
	.byte	27,0,2,35,0,0,14
	.byte	'Ifx_GTM_BRIDGE_PTR2_Bits',0,2,147,1,3
	.word	10119
	.byte	8
	.byte	'_Ifx_GTM_CLC_Bits',0,2,150,1,16,4,9
	.byte	'DISR',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'DISS',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'EDIS',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	602
	.byte	28,0,2,35,0,0,14
	.byte	'Ifx_GTM_CLC_Bits',0,2,157,1,3
	.word	10232
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,2,160,1,16,4,9
	.byte	'CLK_CNT',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	8,0,2,35,0,0,14
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL_Bits',0,2,164,1,3
	.word	10375
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,2,167,1,16,4,9
	.byte	'CLK_CNT',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'CLK6_SEL',0,4
	.word	602
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	602
	.byte	7,0,2,35,0,0,14
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL_Bits',0,2,172,1,3
	.word	10492
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,2,175,1,16,4,9
	.byte	'CLK_CNT',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'CLK7_SEL',0,4
	.word	602
	.byte	1,7,2,35,0,9
	.byte	'reserved_25',0,4
	.word	602
	.byte	7,0,2,35,0,0,14
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL_Bits',0,2,180,1,3
	.word	10627
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK_EN_Bits',0,2,183,1,16,4,9
	.byte	'EN_CLK0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'EN_CLK1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'EN_CLK2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'EN_CLK3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'EN_CLK4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'EN_CLK5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'EN_CLK6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'EN_CLK7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'EN_ECLK0',0,4
	.word	602
	.byte	2,14,2,35,0,9
	.byte	'EN_ECLK1',0,4
	.word	602
	.byte	2,12,2,35,0,9
	.byte	'EN_ECLK2',0,4
	.word	602
	.byte	2,10,2,35,0,9
	.byte	'EN_FXCLK',0,4
	.word	602
	.byte	2,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	8,0,2,35,0,0,14
	.byte	'Ifx_GTM_CMU_CLK_EN_Bits',0,2,198,1,3
	.word	10762
	.byte	8
	.byte	'_Ifx_GTM_CMU_ECLK_DEN_Bits',0,2,201,1,16,4,9
	.byte	'ECLK_DEN',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	8,0,2,35,0,0,14
	.byte	'Ifx_GTM_CMU_ECLK_DEN_Bits',0,2,205,1,3
	.word	11082
	.byte	8
	.byte	'_Ifx_GTM_CMU_ECLK_NUM_Bits',0,2,208,1,16,4,9
	.byte	'ECLK_NUM',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	8,0,2,35,0,0,14
	.byte	'Ifx_GTM_CMU_ECLK_NUM_Bits',0,2,212,1,3
	.word	11194
	.byte	8
	.byte	'_Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,2,215,1,16,4,9
	.byte	'FXCLK_SEL',0,4
	.word	602
	.byte	4,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	602
	.byte	28,0,2,35,0,0,14
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL_Bits',0,2,219,1,3
	.word	11306
	.byte	8
	.byte	'_Ifx_GTM_CMU_GCLK_DEN_Bits',0,2,222,1,16,4,9
	.byte	'GCLK_DEN',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	8,0,2,35,0,0,14
	.byte	'Ifx_GTM_CMU_GCLK_DEN_Bits',0,2,226,1,3
	.word	11422
	.byte	8
	.byte	'_Ifx_GTM_CMU_GCLK_NUM_Bits',0,2,229,1,16,4,9
	.byte	'GCLK_NUM',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	8,0,2,35,0,0,14
	.byte	'Ifx_GTM_CMU_GCLK_NUM_Bits',0,2,233,1,3
	.word	11534
	.byte	8
	.byte	'_Ifx_GTM_CTRL_Bits',0,2,236,1,16,4,9
	.byte	'RF_PROT',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'TO_MODE',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'TO_VAL',0,4
	.word	602
	.byte	5,23,2,35,0,9
	.byte	'reserved_9',0,4
	.word	602
	.byte	23,0,2,35,0,0,14
	.byte	'Ifx_GTM_CTRL_Bits',0,2,243,1,3
	.word	11646
	.byte	8
	.byte	'_Ifx_GTM_DTM_CH_CTRL1_Bits',0,2,246,1,16,4,9
	.byte	'O1SEL_0',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	602
	.byte	2,29,2,35,0,9
	.byte	'SWAP_0',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'O1F_0',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'O1SEL_1',0,4
	.word	602
	.byte	1,23,2,35,0,9
	.byte	'I1SEL_1',0,4
	.word	602
	.byte	1,22,2,35,0,9
	.byte	'SH_EN_1',0,4
	.word	602
	.byte	1,21,2,35,0,9
	.byte	'SWAP_1',0,4
	.word	602
	.byte	1,20,2,35,0,9
	.byte	'O1F_1',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'reserved_14',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'O1SEL_2',0,4
	.word	602
	.byte	1,15,2,35,0,9
	.byte	'I1SEL_2',0,4
	.word	602
	.byte	1,14,2,35,0,9
	.byte	'SH_EN_2',0,4
	.word	602
	.byte	1,13,2,35,0,9
	.byte	'SWAP_2',0,4
	.word	602
	.byte	1,12,2,35,0,9
	.byte	'O1F_2',0,4
	.word	602
	.byte	2,10,2,35,0,9
	.byte	'reserved_22',0,4
	.word	602
	.byte	2,8,2,35,0,9
	.byte	'O1SEL_3',0,4
	.word	602
	.byte	1,7,2,35,0,9
	.byte	'I1SEL_3',0,4
	.word	602
	.byte	1,6,2,35,0,9
	.byte	'SH_EN_3',0,4
	.word	602
	.byte	1,5,2,35,0,9
	.byte	'SWAP_3',0,4
	.word	602
	.byte	1,4,2,35,0,9
	.byte	'O1F_3',0,4
	.word	602
	.byte	2,2,2,35,0,9
	.byte	'reserved_30',0,4
	.word	602
	.byte	2,0,2,35,0,0,14
	.byte	'Ifx_GTM_DTM_CH_CTRL1_Bits',0,2,143,2,3
	.word	11799
	.byte	8
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_Bits',0,2,146,2,16,4,9
	.byte	'POL0_0',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'OC0_0',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'SL0_0',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'DT0_0',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'POL1_0',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'OC1_0',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'SL1_0',0,4
	.word	602
	.byte	1,25,2,35,0,9
	.byte	'DT1_0',0,4
	.word	602
	.byte	1,24,2,35,0,9
	.byte	'POL0_1',0,4
	.word	602
	.byte	1,23,2,35,0,9
	.byte	'OC0_1',0,4
	.word	602
	.byte	1,22,2,35,0,9
	.byte	'SL0_1',0,4
	.word	602
	.byte	1,21,2,35,0,9
	.byte	'DT0_1',0,4
	.word	602
	.byte	1,20,2,35,0,9
	.byte	'POL1_1',0,4
	.word	602
	.byte	1,19,2,35,0,9
	.byte	'OC1_1',0,4
	.word	602
	.byte	1,18,2,35,0,9
	.byte	'SL1_1',0,4
	.word	602
	.byte	1,17,2,35,0,9
	.byte	'DT1_1',0,4
	.word	602
	.byte	1,16,2,35,0,9
	.byte	'POL0_2',0,4
	.word	602
	.byte	1,15,2,35,0,9
	.byte	'OC0_2',0,4
	.word	602
	.byte	1,14,2,35,0,9
	.byte	'SL0_2',0,4
	.word	602
	.byte	1,13,2,35,0,9
	.byte	'DT0_2',0,4
	.word	602
	.byte	1,12,2,35,0,9
	.byte	'POL1_2',0,4
	.word	602
	.byte	1,11,2,35,0,9
	.byte	'OC1_2',0,4
	.word	602
	.byte	1,10,2,35,0,9
	.byte	'SL1_2',0,4
	.word	602
	.byte	1,9,2,35,0,9
	.byte	'DT1_2',0,4
	.word	602
	.byte	1,8,2,35,0,9
	.byte	'POL0_3',0,4
	.word	602
	.byte	1,7,2,35,0,9
	.byte	'OC0_3',0,4
	.word	602
	.byte	1,6,2,35,0,9
	.byte	'SL0_3',0,4
	.word	602
	.byte	1,5,2,35,0,9
	.byte	'DT0_3',0,4
	.word	602
	.byte	1,4,2,35,0,9
	.byte	'POL1_3',0,4
	.word	602
	.byte	1,3,2,35,0,9
	.byte	'OC1_3',0,4
	.word	602
	.byte	1,2,2,35,0,9
	.byte	'SL1_3',0,4
	.word	602
	.byte	1,1,2,35,0,9
	.byte	'DT1_3',0,4
	.word	602
	.byte	1,0,2,35,0,0,14
	.byte	'Ifx_GTM_DTM_CH_CTRL2_Bits',0,2,180,2,3
	.word	12311
	.byte	8
	.byte	'_Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,2,183,2,16,4,9
	.byte	'POL0_0_SR',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'OC0_0_SR',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'SL0_0_SR',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'DT0_0_SR',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'POL1_0_SR',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'OC1_0_SR',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'SL1_0_SR',0,4
	.word	602
	.byte	1,25,2,35,0,9
	.byte	'DT1_0_SR',0,4
	.word	602
	.byte	1,24,2,35,0,9
	.byte	'POL0_1_SR',0,4
	.word	602
	.byte	1,23,2,35,0,9
	.byte	'OC0_1_SR',0,4
	.word	602
	.byte	1,22,2,35,0,9
	.byte	'SL0_1_SR',0,4
	.word	602
	.byte	1,21,2,35,0,9
	.byte	'DT0_1_SR',0,4
	.word	602
	.byte	1,20,2,35,0,9
	.byte	'POL1_1_SR',0,4
	.word	602
	.byte	1,19,2,35,0,9
	.byte	'OC1_1_SR',0,4
	.word	602
	.byte	1,18,2,35,0,9
	.byte	'SL1_1_SR',0,4
	.word	602
	.byte	1,17,2,35,0,9
	.byte	'DT1_1_SR',0,4
	.word	602
	.byte	1,16,2,35,0,9
	.byte	'POL0_2_SR',0,4
	.word	602
	.byte	1,15,2,35,0,9
	.byte	'OC0_2_SR',0,4
	.word	602
	.byte	1,14,2,35,0,9
	.byte	'SL0_2_SR',0,4
	.word	602
	.byte	1,13,2,35,0,9
	.byte	'DT0_2_SR',0,4
	.word	602
	.byte	1,12,2,35,0,9
	.byte	'POL1_2_SR',0,4
	.word	602
	.byte	1,11,2,35,0,9
	.byte	'OC1_2_SR',0,4
	.word	602
	.byte	1,10,2,35,0,9
	.byte	'SL1_2_SR',0,4
	.word	602
	.byte	1,9,2,35,0,9
	.byte	'DT1_2_SR',0,4
	.word	602
	.byte	1,8,2,35,0,9
	.byte	'POL0_3_SR',0,4
	.word	602
	.byte	1,7,2,35,0,9
	.byte	'OC0_3_SR',0,4
	.word	602
	.byte	1,6,2,35,0,9
	.byte	'SL0_3_SR',0,4
	.word	602
	.byte	1,5,2,35,0,9
	.byte	'DT0_3_SR',0,4
	.word	602
	.byte	1,4,2,35,0,9
	.byte	'POL1_3_SR',0,4
	.word	602
	.byte	1,3,2,35,0,9
	.byte	'OC1_3_SR',0,4
	.word	602
	.byte	1,2,2,35,0,9
	.byte	'SL1_3_SR',0,4
	.word	602
	.byte	1,1,2,35,0,9
	.byte	'DT1_3_SR',0,4
	.word	602
	.byte	1,0,2,35,0,0,14
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR_Bits',0,2,217,2,3
	.word	12932
	.byte	8
	.byte	'_Ifx_GTM_DTM_CTRL_Bits',0,2,220,2,16,4,9
	.byte	'CLK_SEL',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'UPD_MODE',0,4
	.word	602
	.byte	3,25,2,35,0,9
	.byte	'reserved_7',0,4
	.word	602
	.byte	25,0,2,35,0,0,14
	.byte	'Ifx_GTM_DTM_CTRL_Bits',0,2,226,2,3
	.word	13655
	.byte	8
	.byte	'_Ifx_GTM_DTM_DTV_CH_Bits',0,2,229,2,16,4,9
	.byte	'RELRISE',0,4
	.word	602
	.byte	10,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	602
	.byte	6,16,2,35,0,9
	.byte	'RELFALL',0,4
	.word	602
	.byte	10,6,2,35,0,9
	.byte	'reserved_26',0,4
	.word	602
	.byte	6,0,2,35,0,0,14
	.byte	'Ifx_GTM_DTM_DTV_CH_Bits',0,2,235,2,3
	.word	13799
	.byte	8
	.byte	'_Ifx_GTM_DTM_PS_CTRL_Bits',0,2,238,2,16,4,9
	.byte	'RELBLK',0,4
	.word	602
	.byte	10,22,2,35,0,9
	.byte	'reserved_10',0,4
	.word	602
	.byte	6,16,2,35,0,9
	.byte	'PSU_IN_SEL',0,4
	.word	602
	.byte	1,15,2,35,0,9
	.byte	'IN_POL',0,4
	.word	602
	.byte	1,14,2,35,0,9
	.byte	'reserved_18',0,4
	.word	602
	.byte	2,12,2,35,0,9
	.byte	'SHIFT_SEL',0,4
	.word	602
	.byte	2,10,2,35,0,9
	.byte	'reserved_22',0,4
	.word	602
	.byte	10,0,2,35,0,0,14
	.byte	'Ifx_GTM_DTM_PS_CTRL_Bits',0,2,247,2,3
	.word	13948
	.byte	8
	.byte	'_Ifx_GTM_EIRQ_EN_Bits',0,2,250,2,16,4,9
	.byte	'AEI_TO_XPT_EIRQ_EN',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'AEI_USP_ADDR_EIRQ_EN',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'AEI_IM_ADDR_EIRQ_EN',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'AEI_USP_BE_EIRQ_EN',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	602
	.byte	28,0,2,35,0,0,14
	.byte	'Ifx_GTM_EIRQ_EN_Bits',0,2,129,3,3
	.word	14163
	.byte	8
	.byte	'_Ifx_GTM_HW_CONF_Bits',0,2,132,3,16,4,9
	.byte	'GRSTEN',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'BRIDGE_MODE_RST',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'AEI_IN',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	602
	.byte	5,24,2,35,0,9
	.byte	'TOM_OUT_RST',0,4
	.word	602
	.byte	1,23,2,35,0,9
	.byte	'TOM_TRIG_CHAIN',0,4
	.word	602
	.byte	3,20,2,35,0,9
	.byte	'reserved_12',0,4
	.word	602
	.byte	4,16,2,35,0,9
	.byte	'IRQ_MODE_LEVEL',0,4
	.word	602
	.byte	1,15,2,35,0,9
	.byte	'IRQ_MODE_PULSE',0,4
	.word	602
	.byte	1,14,2,35,0,9
	.byte	'IRQ_MODE_PULSE_NOTIFY',0,4
	.word	602
	.byte	1,13,2,35,0,9
	.byte	'IRQ_MODE_SINGLE_PULSE',0,4
	.word	602
	.byte	1,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	602
	.byte	12,0,2,35,0,0,14
	.byte	'Ifx_GTM_HW_CONF_Bits',0,2,146,3,3
	.word	14367
	.byte	8
	.byte	'_Ifx_GTM_ICM_IRQG_0_Bits',0,2,149,3,16,4,9
	.byte	'reserved_0',0,4
	.word	602
	.byte	4,28,2,35,0,9
	.byte	'AEI_IRQ',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	602
	.byte	27,0,2,35,0,0,14
	.byte	'Ifx_GTM_ICM_IRQG_0_Bits',0,2,154,3,3
	.word	14724
	.byte	8
	.byte	'_Ifx_GTM_ICM_IRQG_2_Bits',0,2,157,3,16,4,9
	.byte	'TIM0_CH0_IRQ',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'TIM0_CH1_IRQ',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'TIM0_CH2_IRQ',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'TIM0_CH3_IRQ',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'TIM0_CH4_IRQ',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'TIM0_CH5_IRQ',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'TIM0_CH6_IRQ',0,4
	.word	602
	.byte	1,25,2,35,0,9
	.byte	'TIM0_CH7_IRQ',0,4
	.word	602
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	602
	.byte	24,0,2,35,0,0,14
	.byte	'Ifx_GTM_ICM_IRQG_2_Bits',0,2,168,3,3
	.word	14852
	.byte	8
	.byte	'_Ifx_GTM_ICM_IRQG_6_Bits',0,2,171,3,16,4,9
	.byte	'TOM0_CH0_IRQ',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'TOM0_CH1_IRQ',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'TOM0_CH2_IRQ',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'TOM0_CH3_IRQ',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'TOM0_CH4_IRQ',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'TOM0_CH5_IRQ',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'TOM0_CH6_IRQ',0,4
	.word	602
	.byte	1,25,2,35,0,9
	.byte	'TOM0_CH7_IRQ',0,4
	.word	602
	.byte	1,24,2,35,0,9
	.byte	'TOM0_CH8_IRQ',0,4
	.word	602
	.byte	1,23,2,35,0,9
	.byte	'TOM0_CH9_IRQ',0,4
	.word	602
	.byte	1,22,2,35,0,9
	.byte	'TOM0_CH10_IRQ',0,4
	.word	602
	.byte	1,21,2,35,0,9
	.byte	'TOM0_CH11_IRQ',0,4
	.word	602
	.byte	1,20,2,35,0,9
	.byte	'TOM0_CH12_IRQ',0,4
	.word	602
	.byte	1,19,2,35,0,9
	.byte	'TOM0_CH13_IRQ',0,4
	.word	602
	.byte	1,18,2,35,0,9
	.byte	'TOM0_CH14_IRQ',0,4
	.word	602
	.byte	1,17,2,35,0,9
	.byte	'TOM0_CH15_IRQ',0,4
	.word	602
	.byte	1,16,2,35,0,9
	.byte	'TOM1_CH0_IRQ',0,4
	.word	602
	.byte	1,15,2,35,0,9
	.byte	'TOM1_CH1_IRQ',0,4
	.word	602
	.byte	1,14,2,35,0,9
	.byte	'TOM1_CH2_IRQ',0,4
	.word	602
	.byte	1,13,2,35,0,9
	.byte	'TOM1_CH3_IRQ',0,4
	.word	602
	.byte	1,12,2,35,0,9
	.byte	'TOM1_CH4_IRQ',0,4
	.word	602
	.byte	1,11,2,35,0,9
	.byte	'TOM1_CH5_IRQ',0,4
	.word	602
	.byte	1,10,2,35,0,9
	.byte	'TOM1_CH6_IRQ',0,4
	.word	602
	.byte	1,9,2,35,0,9
	.byte	'TOM1_CH7_IRQ',0,4
	.word	602
	.byte	1,8,2,35,0,9
	.byte	'TOM1_CH8_IRQ',0,4
	.word	602
	.byte	1,7,2,35,0,9
	.byte	'TOM1_CH9_IRQ',0,4
	.word	602
	.byte	1,6,2,35,0,9
	.byte	'TOM1_CH10_IRQ',0,4
	.word	602
	.byte	1,5,2,35,0,9
	.byte	'TOM1_CH11_IRQ',0,4
	.word	602
	.byte	1,4,2,35,0,9
	.byte	'TOM1_CH12_IRQ',0,4
	.word	602
	.byte	1,3,2,35,0,9
	.byte	'TOM1_CH13_IRQ',0,4
	.word	602
	.byte	1,2,2,35,0,9
	.byte	'TOM1_CH14_IRQ',0,4
	.word	602
	.byte	1,1,2,35,0,9
	.byte	'TOM1_CH15_IRQ',0,4
	.word	602
	.byte	1,0,2,35,0,0,14
	.byte	'Ifx_GTM_ICM_IRQG_6_Bits',0,2,205,3,3
	.word	15131
	.byte	8
	.byte	'_Ifx_GTM_ICM_IRQG_CEI1_Bits',0,2,208,3,16,4,9
	.byte	'TIM0_CH0_EIRQ',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'TIM0_CH1_EIRQ',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'TIM0_CH2_EIRQ',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'TIM0_CH3_EIRQ',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'TIM0_CH4_EIRQ',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'TIM0_CH5_EIRQ',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'TIM0_CH6_EIRQ',0,4
	.word	602
	.byte	1,25,2,35,0,9
	.byte	'TIM0_CH7_EIRQ',0,4
	.word	602
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	602
	.byte	24,0,2,35,0,0,14
	.byte	'Ifx_GTM_ICM_IRQG_CEI1_Bits',0,2,219,3,3
	.word	15976
	.byte	8
	.byte	'_Ifx_GTM_ICM_IRQG_MEI_Bits',0,2,222,3,16,4,9
	.byte	'GTM_EIRQ',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	602
	.byte	3,28,2,35,0,9
	.byte	'TIM0_EIRQ',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'reserved_5',0,4
	.word	602
	.byte	27,0,2,35,0,0,14
	.byte	'Ifx_GTM_ICM_IRQG_MEI_Bits',0,2,228,3,3
	.word	16269
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,2,231,3,16,4,9
	.byte	'SEL0',0,4
	.word	602
	.byte	4,28,2,35,0,9
	.byte	'SEL1',0,4
	.word	602
	.byte	4,24,2,35,0,9
	.byte	'SEL2',0,4
	.word	602
	.byte	4,20,2,35,0,9
	.byte	'SEL3',0,4
	.word	602
	.byte	4,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,14
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL_Bits',0,2,238,3,3
	.word	16423
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,2,241,3,16,4,9
	.byte	'SEL0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'SEL1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'SEL2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'SEL3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'SEL4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'SEL5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'SEL6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'SEL7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'SEL8',0,4
	.word	602
	.byte	2,14,2,35,0,9
	.byte	'SEL9',0,4
	.word	602
	.byte	2,12,2,35,0,9
	.byte	'SEL10',0,4
	.word	602
	.byte	2,10,2,35,0,9
	.byte	'SEL11',0,4
	.word	602
	.byte	2,8,2,35,0,9
	.byte	'SEL12',0,4
	.word	602
	.byte	2,6,2,35,0,9
	.byte	'SEL13',0,4
	.word	602
	.byte	2,4,2,35,0,9
	.byte	'SEL14',0,4
	.word	602
	.byte	2,2,2,35,0,9
	.byte	'SEL15',0,4
	.word	602
	.byte	2,0,2,35,0,0,14
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL_Bits',0,2,131,4,3
	.word	16593
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,2,134,4,16,4,9
	.byte	'CH0SEL',0,4
	.word	602
	.byte	4,28,2,35,0,9
	.byte	'CH1SEL',0,4
	.word	602
	.byte	4,24,2,35,0,9
	.byte	'CH2SEL',0,4
	.word	602
	.byte	4,20,2,35,0,9
	.byte	'CH3SEL',0,4
	.word	602
	.byte	4,16,2,35,0,9
	.byte	'CH4SEL',0,4
	.word	602
	.byte	4,12,2,35,0,9
	.byte	'CH5SEL',0,4
	.word	602
	.byte	4,8,2,35,0,9
	.byte	'CH6SEL',0,4
	.word	602
	.byte	4,4,2,35,0,9
	.byte	'CH7SEL',0,4
	.word	602
	.byte	4,0,2,35,0,0,14
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL_Bits',0,2,144,4,3
	.word	16934
	.byte	8
	.byte	'_Ifx_GTM_IRQ_EN_Bits',0,2,147,4,16,4,9
	.byte	'AEI_TO_XPT_IRQ_EN',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'AEI_USP_ADDR_IRQ_EN',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'AEI_IM_ADDR_IRQ_EN',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'AEI_USP_BE_IRQ_EN',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	602
	.byte	28,0,2,35,0,0,14
	.byte	'Ifx_GTM_IRQ_EN_Bits',0,2,154,4,3
	.word	17159
	.byte	8
	.byte	'_Ifx_GTM_IRQ_FORCINT_Bits',0,2,157,4,16,4,9
	.byte	'TRG_AEI_TO_XPT',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'TRG_AEI_USP_ADDR',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'TRG_AEI_IM_ADDR',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'TRG_AEI_USP_BE',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	602
	.byte	28,0,2,35,0,0,14
	.byte	'Ifx_GTM_IRQ_FORCINT_Bits',0,2,164,4,3
	.word	17357
	.byte	8
	.byte	'_Ifx_GTM_IRQ_MODE_Bits',0,2,167,4,16,4,9
	.byte	'IRQ_MODE',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	602
	.byte	30,0,2,35,0,0,14
	.byte	'Ifx_GTM_IRQ_MODE_Bits',0,2,171,4,3
	.word	17553
	.byte	8
	.byte	'_Ifx_GTM_IRQ_NOTIFY_Bits',0,2,174,4,16,4,9
	.byte	'AEI_TO_XPT',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'AEI_USP_ADDR',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'AEI_IM_ADDR',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'AEI_USP_BE',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	602
	.byte	28,0,2,35,0,0,14
	.byte	'Ifx_GTM_IRQ_NOTIFY_Bits',0,2,181,4,3
	.word	17656
	.byte	8
	.byte	'_Ifx_GTM_KRST0_Bits',0,2,184,4,16,4,9
	.byte	'RST',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'RSTSTAT',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	602
	.byte	30,0,2,35,0,0,14
	.byte	'Ifx_GTM_KRST0_Bits',0,2,189,4,3
	.word	17834
	.byte	8
	.byte	'_Ifx_GTM_KRST1_Bits',0,2,192,4,16,4,9
	.byte	'RST',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	602
	.byte	31,0,2,35,0,0,14
	.byte	'Ifx_GTM_KRST1_Bits',0,2,196,4,3
	.word	17945
	.byte	8
	.byte	'_Ifx_GTM_KRSTCLR_Bits',0,2,199,4,16,4,9
	.byte	'CLR',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	602
	.byte	31,0,2,35,0,0,14
	.byte	'Ifx_GTM_KRSTCLR_Bits',0,2,203,4,3
	.word	18037
	.byte	8
	.byte	'_Ifx_GTM_OCS_Bits',0,2,206,4,16,4,9
	.byte	'reserved_0',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'SUS',0,4
	.word	602
	.byte	4,4,2,35,0,9
	.byte	'SUS_P',0,4
	.word	602
	.byte	1,3,2,35,0,9
	.byte	'SUSSTA',0,4
	.word	602
	.byte	1,2,2,35,0,9
	.byte	'reserved_30',0,4
	.word	602
	.byte	2,0,2,35,0,0,14
	.byte	'Ifx_GTM_OCS_Bits',0,2,213,4,3
	.word	18133
	.byte	8
	.byte	'_Ifx_GTM_ODA_Bits',0,2,216,4,16,4,9
	.byte	'DDREN',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'DREN',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'reserved_2',0,4
	.word	602
	.byte	30,0,2,35,0,0,14
	.byte	'Ifx_GTM_ODA_Bits',0,2,221,4,3
	.word	18279
	.byte	8
	.byte	'_Ifx_GTM_OTBU0T_Bits',0,2,224,4,16,4,9
	.byte	'CV',0,4
	.word	602
	.byte	27,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	602
	.byte	1,4,2,35,0,9
	.byte	'CM',0,4
	.word	602
	.byte	2,2,2,35,0,9
	.byte	'reserved_30',0,4
	.word	602
	.byte	2,0,2,35,0,0,14
	.byte	'Ifx_GTM_OTBU0T_Bits',0,2,230,4,3
	.word	18385
	.byte	8
	.byte	'_Ifx_GTM_OTBU1T_Bits',0,2,233,4,16,4,9
	.byte	'CV',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	4,4,2,35,0,9
	.byte	'EN',0,4
	.word	602
	.byte	1,3,2,35,0,9
	.byte	'reserved_29',0,4
	.word	602
	.byte	3,0,2,35,0,0,14
	.byte	'Ifx_GTM_OTBU1T_Bits',0,2,239,4,3
	.word	18516
	.byte	8
	.byte	'_Ifx_GTM_OTBU2T_Bits',0,2,242,4,16,4,9
	.byte	'CV',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	4,4,2,35,0,9
	.byte	'EN',0,4
	.word	602
	.byte	1,3,2,35,0,9
	.byte	'reserved_29',0,4
	.word	602
	.byte	3,0,2,35,0,0,14
	.byte	'Ifx_GTM_OTBU2T_Bits',0,2,248,4,3
	.word	18647
	.byte	8
	.byte	'_Ifx_GTM_OTSC0_Bits',0,2,251,4,16,4,9
	.byte	'B0LMT',0,4
	.word	602
	.byte	3,29,2,35,0,9
	.byte	'reserved_3',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'B0LMI',0,4
	.word	602
	.byte	4,24,2,35,0,9
	.byte	'B0HMT',0,4
	.word	602
	.byte	3,21,2,35,0,9
	.byte	'reserved_11',0,4
	.word	602
	.byte	1,20,2,35,0,9
	.byte	'B0HMI',0,4
	.word	602
	.byte	4,16,2,35,0,9
	.byte	'B1LMT',0,4
	.word	602
	.byte	3,13,2,35,0,9
	.byte	'reserved_19',0,4
	.word	602
	.byte	1,12,2,35,0,9
	.byte	'B1LMI',0,4
	.word	602
	.byte	4,8,2,35,0,9
	.byte	'B1HMT',0,4
	.word	602
	.byte	3,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	602
	.byte	1,4,2,35,0,9
	.byte	'B1HMI',0,4
	.word	602
	.byte	4,0,2,35,0,0,14
	.byte	'Ifx_GTM_OTSC0_Bits',0,2,137,5,3
	.word	18778
	.byte	8
	.byte	'_Ifx_GTM_OTSS_Bits',0,2,140,5,16,4,9
	.byte	'OTGB0',0,4
	.word	602
	.byte	4,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	602
	.byte	4,24,2,35,0,9
	.byte	'OTGB1',0,4
	.word	602
	.byte	4,20,2,35,0,9
	.byte	'reserved_12',0,4
	.word	602
	.byte	4,16,2,35,0,9
	.byte	'OTGB2',0,4
	.word	602
	.byte	4,12,2,35,0,9
	.byte	'reserved_20',0,4
	.word	602
	.byte	12,0,2,35,0,0,14
	.byte	'Ifx_GTM_OTSS_Bits',0,2,148,5,3
	.word	19060
	.byte	8
	.byte	'_Ifx_GTM_REV_Bits',0,2,151,5,16,4,9
	.byte	'STEP',0,4
	.word	602
	.byte	8,24,2,35,0,9
	.byte	'NO',0,4
	.word	602
	.byte	4,20,2,35,0,9
	.byte	'MINOR',0,4
	.word	602
	.byte	4,16,2,35,0,9
	.byte	'MAJOR',0,4
	.word	602
	.byte	4,12,2,35,0,9
	.byte	'DEV_CODE0',0,4
	.word	602
	.byte	4,8,2,35,0,9
	.byte	'DEV_CODE1',0,4
	.word	602
	.byte	4,4,2,35,0,9
	.byte	'DEV_CODE2',0,4
	.word	602
	.byte	4,0,2,35,0,0,14
	.byte	'Ifx_GTM_REV_Bits',0,2,160,5,3
	.word	19232
	.byte	8
	.byte	'_Ifx_GTM_RST_Bits',0,2,163,5,16,4,9
	.byte	'RST',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	602
	.byte	31,0,2,35,0,0,14
	.byte	'Ifx_GTM_RST_Bits',0,2,167,5,3
	.word	19410
	.byte	8
	.byte	'_Ifx_GTM_TBU_CH0_BASE_Bits',0,2,170,5,16,4,9
	.byte	'BASE',0,4
	.word	602
	.byte	27,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	602
	.byte	5,0,2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CH0_BASE_Bits',0,2,174,5,3
	.word	19498
	.byte	8
	.byte	'_Ifx_GTM_TBU_CH0_CTRL_Bits',0,2,177,5,16,4,9
	.byte	'LOW_RES',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'CH_CLK_SRC',0,4
	.word	602
	.byte	3,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	602
	.byte	28,0,2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CH0_CTRL_Bits',0,2,182,5,3
	.word	19606
	.byte	8
	.byte	'_Ifx_GTM_TBU_CH1_BASE_Bits',0,2,185,5,16,4,9
	.byte	'BASE',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	8,0,2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CH1_BASE_Bits',0,2,189,5,3
	.word	19738
	.byte	8
	.byte	'_Ifx_GTM_TBU_CH1_CTRL_Bits',0,2,192,5,16,4,9
	.byte	'CH_MODE',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'CH_CLK_SRC',0,4
	.word	602
	.byte	3,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	602
	.byte	28,0,2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CH1_CTRL_Bits',0,2,197,5,3
	.word	19846
	.byte	8
	.byte	'_Ifx_GTM_TBU_CH2_BASE_Bits',0,2,200,5,16,4,9
	.byte	'BASE',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	8,0,2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CH2_BASE_Bits',0,2,204,5,3
	.word	19978
	.byte	8
	.byte	'_Ifx_GTM_TBU_CH2_CTRL_Bits',0,2,207,5,16,4,9
	.byte	'CH_MODE',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'CH_CLK_SRC',0,4
	.word	602
	.byte	3,28,2,35,0,9
	.byte	'reserved_4',0,4
	.word	602
	.byte	28,0,2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CH2_CTRL_Bits',0,2,212,5,3
	.word	20086
	.byte	8
	.byte	'_Ifx_GTM_TBU_CHEN_Bits',0,2,215,5,16,4,9
	.byte	'ENDIS_CH0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_CH1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_CH2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'reserved_6',0,4
	.word	602
	.byte	26,0,2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CHEN_Bits',0,2,221,5,3
	.word	20218
	.byte	8
	.byte	'_Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,2,224,5,16,4,9
	.byte	'SRC_CH0',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'SRC_CH1',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'SRC_CH2',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'SRC_CH3',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'SRC_CH4',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'SRC_CH5',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'SRC_CH6',0,4
	.word	602
	.byte	1,25,2,35,0,9
	.byte	'SRC_CH7',0,4
	.word	602
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	602
	.byte	24,0,2,35,0,0,14
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC_Bits',0,2,235,5,3
	.word	20364
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_CNT_Bits',0,2,242,5,3
	.word	5576
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_CNTS_Bits',0,2,249,5,3
	.word	5831
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_CTRL_Bits',0,2,150,6,3
	.word	6518
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_ECNT_Bits',0,2,157,6,3
	.word	5702
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_ECTRL_Bits',0,2,164,6,3
	.word	7089
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN_Bits',0,2,176,6,3
	.word	8134
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_FLT_FE_Bits',0,2,183,6,3
	.word	6383
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_FLT_RE_Bits',0,2,190,6,3
	.word	6248
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_GPR0_Bits',0,2,197,6,3
	.word	5332
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_GPR1_Bits',0,2,204,6,3
	.word	5454
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN_Bits',0,2,216,6,3
	.word	7462
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT_Bits',0,2,228,6,3
	.word	7732
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE_Bits',0,2,235,6,3
	.word	7994
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY_Bits',0,2,247,6,3
	.word	7226
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_TDUC_Bits',0,2,254,6,3
	.word	5953
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_TDUV_Bits',0,2,135,7,3
	.word	6083
	.byte	8
	.byte	'_Ifx_GTM_TIM_IN_SRC_Bits',0,2,138,7,16,4,9
	.byte	'VAL_0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'MODE_0',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'VAL_1',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'MODE_1',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'VAL_2',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'MODE_2',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'VAL_3',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'MODE_3',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'VAL_4',0,4
	.word	602
	.byte	2,14,2,35,0,9
	.byte	'MODE_4',0,4
	.word	602
	.byte	2,12,2,35,0,9
	.byte	'VAL_5',0,4
	.word	602
	.byte	2,10,2,35,0,9
	.byte	'MODE_5',0,4
	.word	602
	.byte	2,8,2,35,0,9
	.byte	'VAL_6',0,4
	.word	602
	.byte	2,6,2,35,0,9
	.byte	'MODE_6',0,4
	.word	602
	.byte	2,4,2,35,0,9
	.byte	'VAL_7',0,4
	.word	602
	.byte	2,2,2,35,0,9
	.byte	'MODE_7',0,4
	.word	602
	.byte	2,0,2,35,0,0,14
	.byte	'Ifx_GTM_TIM_IN_SRC_Bits',0,2,156,7,3
	.word	21181
	.byte	8
	.byte	'_Ifx_GTM_TIM_INP_VAL_Bits',0,2,159,7,16,4,9
	.byte	'F_OUT',0,4
	.word	602
	.byte	8,24,2,35,0,9
	.byte	'F_IN',0,4
	.word	602
	.byte	8,16,2,35,0,9
	.byte	'TIM_IN',0,4
	.word	602
	.byte	8,8,2,35,0,9
	.byte	'reserved_24',0,4
	.word	602
	.byte	8,0,2,35,0,0,14
	.byte	'Ifx_GTM_TIM_INP_VAL_Bits',0,2,165,7,3
	.word	21526
	.byte	8
	.byte	'_Ifx_GTM_TIM_RST_Bits',0,2,168,7,16,4,9
	.byte	'RST_CH0',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'RST_CH1',0,4
	.word	602
	.byte	1,30,2,35,0,9
	.byte	'RST_CH2',0,4
	.word	602
	.byte	1,29,2,35,0,9
	.byte	'RST_CH3',0,4
	.word	602
	.byte	1,28,2,35,0,9
	.byte	'RST_CH4',0,4
	.word	602
	.byte	1,27,2,35,0,9
	.byte	'RST_CH5',0,4
	.word	602
	.byte	1,26,2,35,0,9
	.byte	'RST_CH6',0,4
	.word	602
	.byte	1,25,2,35,0,9
	.byte	'RST_CH7',0,4
	.word	602
	.byte	1,24,2,35,0,9
	.byte	'reserved_8',0,4
	.word	602
	.byte	24,0,2,35,0,0,14
	.byte	'Ifx_GTM_TIM_RST_Bits',0,2,179,7,3
	.word	21667
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_CM0_Bits',0,2,186,7,3
	.word	1180
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_CM1_Bits',0,2,193,7,3
	.word	1303
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_CN0_Bits',0,2,200,7,3
	.word	1426
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_CTRL_Bits',0,2,218,7,3
	.word	570
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN_Bits',0,2,226,7,3
	.word	1829
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT_Bits',0,2,234,7,3
	.word	1992
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE_Bits',0,2,241,7,3
	.word	2161
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY_Bits',0,2,249,7,3
	.word	1672
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_SR0_Bits',0,2,128,8,3
	.word	934
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_SR1_Bits',0,2,135,8,3
	.word	1057
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_STAT_Bits',0,2,142,8,3
	.word	1549
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB_Bits',0,2,151,8,3
	.word	2819
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL_Bits',0,2,165,8,3
	.word	3762
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT_Bits',0,2,179,8,3
	.word	4070
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL_Bits',0,2,200,8,3
	.word	2991
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL_Bits',0,2,223,8,3
	.word	2351
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG_Bits',0,2,237,8,3
	.word	3442
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL_Bits',0,2,251,8,3
	.word	4378
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT_Bits',0,2,137,9,3
	.word	4686
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,2,140,9,16,4,9
	.byte	'ACT_TB',0,4
	.word	602
	.byte	24,8,2,35,0,9
	.byte	'TB_TRIG',0,4
	.word	602
	.byte	1,7,2,35,0,9
	.byte	'TBU_SEL',0,4
	.word	602
	.byte	2,5,2,35,0,9
	.byte	'reserved_27',0,4
	.word	602
	.byte	5,0,2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB_Bits',0,2,146,9,3
	.word	22615
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,2,149,9,16,4,9
	.byte	'ENDIS_CTRL0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_CTRL1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_CTRL2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'ENDIS_CTRL3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'ENDIS_CTRL4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'ENDIS_CTRL5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'ENDIS_CTRL6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'ENDIS_CTRL7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL_Bits',0,2,160,9,3
	.word	22769
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,2,163,9,16,4,9
	.byte	'ENDIS_STAT0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'ENDIS_STAT1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'ENDIS_STAT2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'ENDIS_STAT3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'ENDIS_STAT4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'ENDIS_STAT5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'ENDIS_STAT6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'ENDIS_STAT7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT_Bits',0,2,174,9,3
	.word	23059
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,2,177,9,16,4,9
	.byte	'FUPD_CTRL0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'FUPD_CTRL1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'FUPD_CTRL2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'FUPD_CTRL3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'FUPD_CTRL4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'FUPD_CTRL5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'FUPD_CTRL6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'FUPD_CTRL7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'RSTCN0_CH0',0,4
	.word	602
	.byte	2,14,2,35,0,9
	.byte	'RSTCN0_CH1',0,4
	.word	602
	.byte	2,12,2,35,0,9
	.byte	'RSTCN0_CH2',0,4
	.word	602
	.byte	2,10,2,35,0,9
	.byte	'RSTCN0_CH3',0,4
	.word	602
	.byte	2,8,2,35,0,9
	.byte	'RSTCN0_CH4',0,4
	.word	602
	.byte	2,6,2,35,0,9
	.byte	'RSTCN0_CH5',0,4
	.word	602
	.byte	2,4,2,35,0,9
	.byte	'RSTCN0_CH6',0,4
	.word	602
	.byte	2,2,2,35,0,9
	.byte	'RSTCN0_CH7',0,4
	.word	602
	.byte	2,0,2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL_Bits',0,2,195,9,3
	.word	23349
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,2,198,9,16,4,9
	.byte	'HOST_TRIG',0,4
	.word	602
	.byte	1,31,2,35,0,9
	.byte	'reserved_1',0,4
	.word	602
	.byte	7,24,2,35,0,9
	.byte	'RST_CH0',0,4
	.word	602
	.byte	1,23,2,35,0,9
	.byte	'RST_CH1',0,4
	.word	602
	.byte	1,22,2,35,0,9
	.byte	'RST_CH2',0,4
	.word	602
	.byte	1,21,2,35,0,9
	.byte	'RST_CH3',0,4
	.word	602
	.byte	1,20,2,35,0,9
	.byte	'RST_CH4',0,4
	.word	602
	.byte	1,19,2,35,0,9
	.byte	'RST_CH5',0,4
	.word	602
	.byte	1,18,2,35,0,9
	.byte	'RST_CH6',0,4
	.word	602
	.byte	1,17,2,35,0,9
	.byte	'RST_CH7',0,4
	.word	602
	.byte	1,16,2,35,0,9
	.byte	'UPEN_CTRL0',0,4
	.word	602
	.byte	2,14,2,35,0,9
	.byte	'UPEN_CTRL1',0,4
	.word	602
	.byte	2,12,2,35,0,9
	.byte	'UPEN_CTRL2',0,4
	.word	602
	.byte	2,10,2,35,0,9
	.byte	'UPEN_CTRL3',0,4
	.word	602
	.byte	2,8,2,35,0,9
	.byte	'UPEN_CTRL4',0,4
	.word	602
	.byte	2,6,2,35,0,9
	.byte	'UPEN_CTRL5',0,4
	.word	602
	.byte	2,4,2,35,0,9
	.byte	'UPEN_CTRL6',0,4
	.word	602
	.byte	2,2,2,35,0,9
	.byte	'UPEN_CTRL7',0,4
	.word	602
	.byte	2,0,2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL_Bits',0,2,218,9,3
	.word	23782
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,2,221,9,16,4,9
	.byte	'INT_TRIG0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'INT_TRIG1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'INT_TRIG2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'INT_TRIG3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'INT_TRIG4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'INT_TRIG5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'INT_TRIG6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'INT_TRIG7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG_Bits',0,2,232,9,3
	.word	24232
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,2,235,9,16,4,9
	.byte	'OUTEN_CTRL0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'OUTEN_CTRL1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'OUTEN_CTRL2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'OUTEN_CTRL3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'OUTEN_CTRL4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'OUTEN_CTRL5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'OUTEN_CTRL6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'OUTEN_CTRL7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL_Bits',0,2,246,9,3
	.word	24502
	.byte	8
	.byte	'_Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,2,249,9,16,4,9
	.byte	'OUTEN_STAT0',0,4
	.word	602
	.byte	2,30,2,35,0,9
	.byte	'OUTEN_STAT1',0,4
	.word	602
	.byte	2,28,2,35,0,9
	.byte	'OUTEN_STAT2',0,4
	.word	602
	.byte	2,26,2,35,0,9
	.byte	'OUTEN_STAT3',0,4
	.word	602
	.byte	2,24,2,35,0,9
	.byte	'OUTEN_STAT4',0,4
	.word	602
	.byte	2,22,2,35,0,9
	.byte	'OUTEN_STAT5',0,4
	.word	602
	.byte	2,20,2,35,0,9
	.byte	'OUTEN_STAT6',0,4
	.word	602
	.byte	2,18,2,35,0,9
	.byte	'OUTEN_STAT7',0,4
	.word	602
	.byte	2,16,2,35,0,9
	.byte	'reserved_16',0,4
	.word	602
	.byte	16,0,2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT_Bits',0,2,132,10,3
	.word	24792
	.byte	7,2,140,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	8540
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_ACCEN0',0,2,145,10,3
	.word	25082
	.byte	7,2,148,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	9097
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_ACCEN1',0,2,153,10,3
	.word	25146
	.byte	7,2,156,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	9174
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_ADCTRIG0OUT0',0,2,161,10,3
	.word	25210
	.byte	7,2,164,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	9328
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_ADCTRIG1OUT0',0,2,169,10,3
	.word	25280
	.byte	7,2,172,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	9482
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_AEI_ADDR_XPT',0,2,177,10,3
	.word	25350
	.byte	7,2,180,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	9610
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_BRIDGE_MODE',0,2,185,10,3
	.word	25420
	.byte	7,2,188,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	9917
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_BRIDGE_PTR1',0,2,193,10,3
	.word	25489
	.byte	7,2,196,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	10119
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_BRIDGE_PTR2',0,2,201,10,3
	.word	25558
	.byte	7,2,204,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	10232
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_CLC',0,2,209,10,3
	.word	25627
	.byte	7,2,212,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	10375
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_CMU_CLK0_5_CTRL',0,2,217,10,3
	.word	25688
	.byte	7,2,220,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	10492
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_CMU_CLK_6_CTRL',0,2,225,10,3
	.word	25761
	.byte	7,2,228,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	10627
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_CMU_CLK_7_CTRL',0,2,233,10,3
	.word	25833
	.byte	7,2,236,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	10762
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_CMU_CLK_EN',0,2,241,10,3
	.word	25905
	.byte	7,2,244,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	11082
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_CMU_ECLK_DEN',0,2,249,10,3
	.word	25973
	.byte	7,2,252,10,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	11194
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_CMU_ECLK_NUM',0,2,129,11,3
	.word	26043
	.byte	7,2,132,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	11306
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_CMU_FXCLK_CTRL',0,2,137,11,3
	.word	26113
	.byte	7,2,140,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	11422
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_CMU_GCLK_DEN',0,2,145,11,3
	.word	26185
	.byte	7,2,148,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	11534
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_CMU_GCLK_NUM',0,2,153,11,3
	.word	26255
	.byte	7,2,156,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	11646
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_CTRL',0,2,161,11,3
	.word	26325
	.byte	7,2,164,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	11799
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_DTM_CH_CTRL1',0,2,169,11,3
	.word	26387
	.byte	7,2,172,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	12311
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_DTM_CH_CTRL2',0,2,177,11,3
	.word	26457
	.byte	7,2,180,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	12932
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_DTM_CH_CTRL2_SR',0,2,185,11,3
	.word	26527
	.byte	7,2,188,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	13655
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_DTM_CTRL',0,2,193,11,3
	.word	26600
	.byte	7,2,196,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	13799
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_DTM_DTV_CH',0,2,201,11,3
	.word	26666
	.byte	7,2,204,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	13948
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_DTM_PS_CTRL',0,2,209,11,3
	.word	26734
	.byte	7,2,212,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	14163
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_EIRQ_EN',0,2,217,11,3
	.word	26803
	.byte	7,2,220,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	14367
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_HW_CONF',0,2,225,11,3
	.word	26868
	.byte	7,2,228,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	14724
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_ICM_IRQG_0',0,2,233,11,3
	.word	26933
	.byte	7,2,236,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	14852
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_ICM_IRQG_2',0,2,241,11,3
	.word	27001
	.byte	7,2,244,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	15131
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_ICM_IRQG_6',0,2,249,11,3
	.word	27069
	.byte	7,2,252,11,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	15976
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_ICM_IRQG_CEI1',0,2,129,12,3
	.word	27137
	.byte	7,2,132,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	16269
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_ICM_IRQG_MEI',0,2,137,12,3
	.word	27208
	.byte	7,2,140,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	16423
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_INOUTSEL_CAN_OUTSEL',0,2,145,12,3
	.word	27278
	.byte	7,2,148,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	16593
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_INOUTSEL_T_OUTSEL',0,2,153,12,3
	.word	27355
	.byte	7,2,156,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	16934
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_INOUTSEL_TIM_INSEL',0,2,161,12,3
	.word	27430
	.byte	7,2,164,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	17159
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_IRQ_EN',0,2,169,12,3
	.word	27506
	.byte	7,2,172,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	17357
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_IRQ_FORCINT',0,2,177,12,3
	.word	27570
	.byte	7,2,180,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	17553
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_IRQ_MODE',0,2,185,12,3
	.word	27639
	.byte	7,2,188,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	17656
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_IRQ_NOTIFY',0,2,193,12,3
	.word	27705
	.byte	7,2,196,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	17834
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_KRST0',0,2,201,12,3
	.word	27773
	.byte	7,2,204,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	17945
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_KRST1',0,2,209,12,3
	.word	27836
	.byte	7,2,212,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	18037
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_KRSTCLR',0,2,217,12,3
	.word	27899
	.byte	7,2,220,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	18133
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_OCS',0,2,225,12,3
	.word	27964
	.byte	7,2,228,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	18279
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_ODA',0,2,233,12,3
	.word	28025
	.byte	7,2,236,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	18385
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_OTBU0T',0,2,241,12,3
	.word	28086
	.byte	7,2,244,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	18516
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_OTBU1T',0,2,249,12,3
	.word	28150
	.byte	7,2,252,12,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	18647
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_OTBU2T',0,2,129,13,3
	.word	28214
	.byte	7,2,132,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	18778
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_OTSC0',0,2,137,13,3
	.word	28278
	.byte	7,2,140,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	19060
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_OTSS',0,2,145,13,3
	.word	28341
	.byte	7,2,148,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	19232
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_REV',0,2,153,13,3
	.word	28403
	.byte	7,2,156,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	19410
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_RST',0,2,161,13,3
	.word	28464
	.byte	7,2,164,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	19498
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CH0_BASE',0,2,169,13,3
	.word	28525
	.byte	7,2,172,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	19606
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CH0_CTRL',0,2,177,13,3
	.word	28595
	.byte	7,2,180,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	19738
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CH1_BASE',0,2,185,13,3
	.word	28665
	.byte	7,2,188,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	19846
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CH1_CTRL',0,2,193,13,3
	.word	28735
	.byte	7,2,196,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	19978
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CH2_BASE',0,2,201,13,3
	.word	28805
	.byte	7,2,204,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	20086
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CH2_CTRL',0,2,209,13,3
	.word	28875
	.byte	7,2,212,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	20218
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TBU_CHEN',0,2,217,13,3
	.word	28945
	.byte	7,2,220,13,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	20364
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TIM_AUX_IN_SRC',0,2,225,13,3
	.word	29011
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_CNT',0,2,233,13,3
	.word	5548
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_CNTS',0,2,241,13,3
	.word	5803
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_CTRL',0,2,249,13,3
	.word	6490
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_ECNT',0,2,129,14,3
	.word	5674
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_ECTRL',0,2,137,14,3
	.word	7061
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_EIRQ_EN',0,2,145,14,3
	.word	8106
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_FLT_FE',0,2,153,14,3
	.word	6355
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_FLT_RE',0,2,161,14,3
	.word	6220
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_GPR0',0,2,169,14,3
	.word	5304
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_GPR1',0,2,177,14,3
	.word	5426
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_IRQ_EN',0,2,185,14,3
	.word	7434
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_IRQ_FORCINT',0,2,193,14,3
	.word	7704
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_IRQ_MODE',0,2,201,14,3
	.word	7966
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_IRQ_NOTIFY',0,2,209,14,3
	.word	7198
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_TDUC',0,2,217,14,3
	.word	5925
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_TDUV',0,2,225,14,3
	.word	6055
	.byte	7,2,228,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	21181
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TIM_IN_SRC',0,2,233,14,3
	.word	29573
	.byte	7,2,236,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	21526
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TIM_INP_VAL',0,2,241,14,3
	.word	29641
	.byte	7,2,244,14,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	21667
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TIM_RST',0,2,249,14,3
	.word	29710
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_CM0',0,2,129,15,3
	.word	1152
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_CM1',0,2,137,15,3
	.word	1275
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_CN0',0,2,145,15,3
	.word	1398
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_CTRL',0,2,153,15,3
	.word	519
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_IRQ_EN',0,2,161,15,3
	.word	1801
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_IRQ_FORCINT',0,2,169,15,3
	.word	1964
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_IRQ_MODE',0,2,177,15,3
	.word	2133
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_IRQ_NOTIFY',0,2,185,15,3
	.word	1644
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_SR0',0,2,193,15,3
	.word	906
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_SR1',0,2,201,15,3
	.word	1029
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_STAT',0,2,209,15,3
	.word	1521
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_ACT_TB',0,2,217,15,3
	.word	2791
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_CTRL',0,2,225,15,3
	.word	3734
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_ENDIS_STAT',0,2,233,15,3
	.word	4042
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_FUPD_CTRL',0,2,241,15,3
	.word	2963
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_GLB_CTRL',0,2,249,15,3
	.word	2323
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_INT_TRIG',0,2,129,16,3
	.word	3414
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_CTRL',0,2,137,16,3
	.word	4350
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC0_OUTEN_STAT',0,2,145,16,3
	.word	4658
	.byte	7,2,148,16,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	22615
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_ACT_TB',0,2,153,16,3
	.word	30395
	.byte	7,2,156,16,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	22769
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_CTRL',0,2,161,16,3
	.word	30468
	.byte	7,2,164,16,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	23059
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_ENDIS_STAT',0,2,169,16,3
	.word	30545
	.byte	7,2,172,16,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	23349
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_FUPD_CTRL',0,2,177,16,3
	.word	30622
	.byte	7,2,180,16,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	23782
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_GLB_CTRL',0,2,185,16,3
	.word	30698
	.byte	7,2,188,16,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	24232
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_INT_TRIG',0,2,193,16,3
	.word	30773
	.byte	7,2,196,16,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	24502
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_CTRL',0,2,201,16,3
	.word	30848
	.byte	7,2,204,16,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	24792
	.byte	2,35,0,0,14
	.byte	'Ifx_GTM_TOM_TGC1_OUTEN_STAT',0,2,209,16,3
	.word	30925
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK0_5',0,2,220,16,25,4,4
	.byte	'CTRL',0,4
	.word	25688
	.byte	2,35,0,0,12
	.word	31002
	.byte	14
	.byte	'Ifx_GTM_CMU_CLK0_5',0,2,223,16,3
	.word	31043
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK_6',0,2,226,16,25,4,4
	.byte	'CTRL',0,4
	.word	25761
	.byte	2,35,0,0,12
	.word	31076
	.byte	14
	.byte	'Ifx_GTM_CMU_CLK_6',0,2,229,16,3
	.word	31116
	.byte	8
	.byte	'_Ifx_GTM_CMU_CLK_7',0,2,232,16,25,4,4
	.byte	'CTRL',0,4
	.word	25833
	.byte	2,35,0,0,12
	.word	31148
	.byte	14
	.byte	'Ifx_GTM_CMU_CLK_7',0,2,235,16,3
	.word	31188
	.byte	8
	.byte	'_Ifx_GTM_CMU_ECLK',0,2,238,16,25,8,4
	.byte	'NUM',0,4
	.word	26043
	.byte	2,35,0,4
	.byte	'DEN',0,4
	.word	25973
	.byte	2,35,4,0,12
	.word	31220
	.byte	14
	.byte	'Ifx_GTM_CMU_ECLK',0,2,242,16,3
	.word	31271
	.byte	8
	.byte	'_Ifx_GTM_CMU_FXCLK',0,2,245,16,25,4,4
	.byte	'CTRL',0,4
	.word	26113
	.byte	2,35,0,0,12
	.word	31302
	.byte	14
	.byte	'Ifx_GTM_CMU_FXCLK',0,2,248,16,3
	.word	31342
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL_CAN',0,2,251,16,25,4,4
	.byte	'OUTSEL',0,4
	.word	27278
	.byte	2,35,0,0,12
	.word	31374
	.byte	14
	.byte	'Ifx_GTM_INOUTSEL_CAN',0,2,254,16,3
	.word	31419
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL_T',0,2,129,17,25,32,10,32
	.word	27355
	.byte	11,7,0,4
	.byte	'OUTSEL',0,32
	.word	31480
	.byte	2,35,0,0,12
	.word	31454
	.byte	14
	.byte	'Ifx_GTM_INOUTSEL_T',0,2,132,17,3
	.word	31506
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL_TIM',0,2,135,17,25,32,4
	.byte	'INSEL',0,4
	.word	27430
	.byte	2,35,0,10,28
	.word	202
	.byte	11,27,0,4
	.byte	'reserved_4',0,28
	.word	31582
	.byte	2,35,4,0,12
	.word	31539
	.byte	14
	.byte	'Ifx_GTM_INOUTSEL_TIM',0,2,139,17,3
	.word	31612
	.byte	8
	.byte	'_Ifx_GTM_TIM_CH',0,2,142,17,25,116,4
	.byte	'GPR0',0,4
	.word	5304
	.byte	2,35,0,4
	.byte	'GPR1',0,4
	.word	5426
	.byte	2,35,4,4
	.byte	'CNT',0,4
	.word	5548
	.byte	2,35,8,4
	.byte	'ECNT',0,4
	.word	5674
	.byte	2,35,12,4
	.byte	'CNTS',0,4
	.word	5803
	.byte	2,35,16,4
	.byte	'TDUC',0,4
	.word	5925
	.byte	2,35,20,4
	.byte	'TDUV',0,4
	.word	6055
	.byte	2,35,24,4
	.byte	'FLT_RE',0,4
	.word	6220
	.byte	2,35,28,4
	.byte	'FLT_FE',0,4
	.word	6355
	.byte	2,35,32,4
	.byte	'CTRL',0,4
	.word	6490
	.byte	2,35,36,4
	.byte	'ECTRL',0,4
	.word	7061
	.byte	2,35,40,4
	.byte	'IRQ_NOTIFY',0,4
	.word	7198
	.byte	2,35,44,4
	.byte	'IRQ_EN',0,4
	.word	7434
	.byte	2,35,48,4
	.byte	'IRQ_FORCINT',0,4
	.word	7704
	.byte	2,35,52,4
	.byte	'IRQ_MODE',0,4
	.word	7966
	.byte	2,35,56,4
	.byte	'EIRQ_EN',0,4
	.word	8106
	.byte	2,35,60,10,52
	.word	202
	.byte	11,51,0,4
	.byte	'reserved_40',0,52
	.word	31919
	.byte	2,35,64,0,12
	.word	31647
	.byte	14
	.byte	'Ifx_GTM_TIM_CH',0,2,161,17,3
	.word	31950
	.byte	8
	.byte	'_Ifx_GTM_TOM_CH',0,2,164,17,25,48,4
	.byte	'CTRL',0,4
	.word	519
	.byte	2,35,0,4
	.byte	'SR0',0,4
	.word	906
	.byte	2,35,4,4
	.byte	'SR1',0,4
	.word	1029
	.byte	2,35,8,4
	.byte	'CM0',0,4
	.word	1152
	.byte	2,35,12,4
	.byte	'CM1',0,4
	.word	1275
	.byte	2,35,16,4
	.byte	'CN0',0,4
	.word	1398
	.byte	2,35,20,4
	.byte	'STAT',0,4
	.word	1521
	.byte	2,35,24,4
	.byte	'IRQ_NOTIFY',0,4
	.word	1644
	.byte	2,35,28,4
	.byte	'IRQ_EN',0,4
	.word	1801
	.byte	2,35,32,4
	.byte	'IRQ_FORCINT',0,4
	.word	1964
	.byte	2,35,36,4
	.byte	'IRQ_MODE',0,4
	.word	2133
	.byte	2,35,40,10,4
	.word	202
	.byte	11,3,0,4
	.byte	'reserved_2C',0,4
	.word	32169
	.byte	2,35,44,0,12
	.word	31979
	.byte	14
	.byte	'Ifx_GTM_TOM_CH',0,2,178,17,3
	.word	32200
	.byte	8
	.byte	'_Ifx_GTM_BRIDGE',0,2,191,17,25,12,4
	.byte	'MODE',0,4
	.word	25420
	.byte	2,35,0,4
	.byte	'PTR1',0,4
	.word	25489
	.byte	2,35,4,4
	.byte	'PTR2',0,4
	.word	25558
	.byte	2,35,8,0,12
	.word	32229
	.byte	14
	.byte	'Ifx_GTM_BRIDGE',0,2,196,17,3
	.word	32294
	.byte	8
	.byte	'_Ifx_GTM_CMU',0,2,199,17,25,72,4
	.byte	'CLK_EN',0,4
	.word	25905
	.byte	2,35,0,4
	.byte	'GCLK_NUM',0,4
	.word	26255
	.byte	2,35,4,4
	.byte	'GCLK_DEN',0,4
	.word	26185
	.byte	2,35,8,10,24
	.word	31002
	.byte	11,5,0,12
	.word	32394
	.byte	4
	.byte	'CLK0_5',0,24
	.word	32403
	.byte	2,35,12,12
	.word	31076
	.byte	4
	.byte	'CLK_6',0,4
	.word	32424
	.byte	2,35,36,12
	.word	31148
	.byte	4
	.byte	'CLK_7',0,4
	.word	32444
	.byte	2,35,40,10,24
	.word	31220
	.byte	11,2,0,12
	.word	32464
	.byte	4
	.byte	'ECLK',0,24
	.word	32473
	.byte	2,35,44,12
	.word	31302
	.byte	4
	.byte	'FXCLK',0,4
	.word	32492
	.byte	2,35,68,0,12
	.word	32323
	.byte	14
	.byte	'Ifx_GTM_CMU',0,2,209,17,3
	.word	32513
	.byte	8
	.byte	'_Ifx_GTM_DTM',0,2,212,17,25,36,4
	.byte	'CTRL',0,4
	.word	26600
	.byte	2,35,0,4
	.byte	'CH_CTRL1',0,4
	.word	26387
	.byte	2,35,4,4
	.byte	'CH_CTRL2',0,4
	.word	26457
	.byte	2,35,8,4
	.byte	'CH_CTRL2_SR',0,4
	.word	26527
	.byte	2,35,12,4
	.byte	'PS_CTRL',0,4
	.word	26734
	.byte	2,35,16,10,16
	.word	26666
	.byte	11,3,0,4
	.byte	'DTV_CH',0,16
	.word	32646
	.byte	2,35,20,0,12
	.word	32539
	.byte	14
	.byte	'Ifx_GTM_DTM',0,2,220,17,3
	.word	32672
	.byte	8
	.byte	'_Ifx_GTM_ICM',0,2,223,17,25,60,4
	.byte	'IRQG_0',0,4
	.word	26933
	.byte	2,35,0,4
	.byte	'reserved_4',0,4
	.word	32169
	.byte	2,35,4,4
	.byte	'IRQG_2',0,4
	.word	27001
	.byte	2,35,8,10,12
	.word	202
	.byte	11,11,0,4
	.byte	'reserved_C',0,12
	.word	32769
	.byte	2,35,12,4
	.byte	'IRQG_6',0,4
	.word	27069
	.byte	2,35,24,10,20
	.word	202
	.byte	11,19,0,4
	.byte	'reserved_1C',0,20
	.word	32814
	.byte	2,35,28,4
	.byte	'IRQG_MEI',0,4
	.word	27208
	.byte	2,35,48,4
	.byte	'reserved_34',0,4
	.word	32169
	.byte	2,35,52,4
	.byte	'IRQG_CEI1',0,4
	.word	27137
	.byte	2,35,56,0,12
	.word	32698
	.byte	14
	.byte	'Ifx_GTM_ICM',0,2,234,17,3
	.word	32903
	.byte	8
	.byte	'_Ifx_GTM_INOUTSEL',0,2,237,17,25,148,1,10,32
	.word	31539
	.byte	11,0,0,12
	.word	32954
	.byte	4
	.byte	'TIM',0,32
	.word	32963
	.byte	2,35,0,12
	.word	31454
	.byte	4
	.byte	'T',0,32
	.word	32981
	.byte	2,35,32,10,80
	.word	202
	.byte	11,79,0,4
	.byte	'reserved_40',0,80
	.word	32997
	.byte	2,35,64,12
	.word	31374
	.byte	4
	.byte	'CAN',0,4
	.word	33027
	.byte	3,35,144,1,0,12
	.word	32929
	.byte	14
	.byte	'Ifx_GTM_INOUTSEL',0,2,243,17,3
	.word	33047
	.byte	8
	.byte	'_Ifx_GTM_TBU',0,2,246,17,25,28,4
	.byte	'CHEN',0,4
	.word	28945
	.byte	2,35,0,4
	.byte	'CH0_CTRL',0,4
	.word	28595
	.byte	2,35,4,4
	.byte	'CH0_BASE',0,4
	.word	28525
	.byte	2,35,8,4
	.byte	'CH1_CTRL',0,4
	.word	28735
	.byte	2,35,12,4
	.byte	'CH1_BASE',0,4
	.word	28665
	.byte	2,35,16,4
	.byte	'CH2_CTRL',0,4
	.word	28875
	.byte	2,35,20,4
	.byte	'CH2_BASE',0,4
	.word	28805
	.byte	2,35,24,0,12
	.word	33078
	.byte	14
	.byte	'Ifx_GTM_TBU',0,2,255,17,3
	.word	33220
	.byte	8
	.byte	'_Ifx_GTM_TIM',0,2,130,18,25,128,8,12
	.word	31647
	.byte	4
	.byte	'CH0',0,116
	.word	33266
	.byte	2,35,0,4
	.byte	'INP_VAL',0,4
	.word	29641
	.byte	2,35,116,4
	.byte	'IN_SRC',0,4
	.word	29573
	.byte	2,35,120,4
	.byte	'RST',0,4
	.word	29710
	.byte	2,35,124,12
	.word	31647
	.byte	4
	.byte	'CH1',0,116
	.word	33330
	.byte	3,35,128,1,4
	.byte	'reserved_F4',0,12
	.word	32769
	.byte	3,35,244,1,12
	.word	31647
	.byte	4
	.byte	'CH2',0,116
	.word	33371
	.byte	3,35,128,2,4
	.byte	'reserved_174',0,12
	.word	32769
	.byte	3,35,244,2,12
	.word	31647
	.byte	4
	.byte	'CH3',0,116
	.word	33413
	.byte	3,35,128,3,4
	.byte	'reserved_1F4',0,12
	.word	32769
	.byte	3,35,244,3,12
	.word	31647
	.byte	4
	.byte	'CH4',0,116
	.word	33455
	.byte	3,35,128,4,4
	.byte	'reserved_274',0,12
	.word	32769
	.byte	3,35,244,4,12
	.word	31647
	.byte	4
	.byte	'CH5',0,116
	.word	33497
	.byte	3,35,128,5,4
	.byte	'reserved_2F4',0,12
	.word	32769
	.byte	3,35,244,5,12
	.word	31647
	.byte	4
	.byte	'CH6',0,116
	.word	33539
	.byte	3,35,128,6,4
	.byte	'reserved_374',0,12
	.word	32769
	.byte	3,35,244,6,12
	.word	31647
	.byte	4
	.byte	'CH7',0,116
	.word	33581
	.byte	3,35,128,7,4
	.byte	'reserved_3F4',0,12
	.word	32769
	.byte	3,35,244,7,0,12
	.word	33246
	.byte	14
	.byte	'Ifx_GTM_TIM',0,2,150,18,3
	.word	33624
	.byte	8
	.byte	'_Ifx_GTM_TOM',0,2,153,18,25,128,16,12
	.word	31979
	.byte	4
	.byte	'CH0',0,48
	.word	33670
	.byte	2,35,0,4
	.byte	'TGC0_GLB_CTRL',0,4
	.word	2323
	.byte	2,35,48,4
	.byte	'TGC0_ACT_TB',0,4
	.word	2791
	.byte	2,35,52,4
	.byte	'TGC0_FUPD_CTRL',0,4
	.word	2963
	.byte	2,35,56,4
	.byte	'TGC0_INT_TRIG',0,4
	.word	3414
	.byte	2,35,60,12
	.word	31979
	.byte	4
	.byte	'CH1',0,48
	.word	33779
	.byte	2,35,64,4
	.byte	'TGC0_ENDIS_CTRL',0,4
	.word	3734
	.byte	2,35,112,4
	.byte	'TGC0_ENDIS_STAT',0,4
	.word	4042
	.byte	2,35,116,4
	.byte	'TGC0_OUTEN_CTRL',0,4
	.word	4350
	.byte	2,35,120,4
	.byte	'TGC0_OUTEN_STAT',0,4
	.word	4658
	.byte	2,35,124,12
	.word	31979
	.byte	4
	.byte	'CH2',0,48
	.word	33897
	.byte	3,35,128,1,10,16
	.word	202
	.byte	11,15,0,4
	.byte	'reserved_B0',0,16
	.word	33916
	.byte	3,35,176,1,12
	.word	31979
	.byte	4
	.byte	'CH3',0,48
	.word	33947
	.byte	3,35,192,1,4
	.byte	'reserved_F0',0,16
	.word	33916
	.byte	3,35,240,1,12
	.word	31979
	.byte	4
	.byte	'CH4',0,48
	.word	33988
	.byte	3,35,128,2,4
	.byte	'reserved_130',0,16
	.word	33916
	.byte	3,35,176,2,12
	.word	31979
	.byte	4
	.byte	'CH5',0,48
	.word	34030
	.byte	3,35,192,2,4
	.byte	'reserved_170',0,16
	.word	33916
	.byte	3,35,240,2,12
	.word	31979
	.byte	4
	.byte	'CH6',0,48
	.word	34072
	.byte	3,35,128,3,4
	.byte	'reserved_1B0',0,16
	.word	33916
	.byte	3,35,176,3,12
	.word	31979
	.byte	4
	.byte	'CH7',0,48
	.word	34114
	.byte	3,35,192,3,4
	.byte	'reserved_1F0',0,16
	.word	33916
	.byte	3,35,240,3,12
	.word	31979
	.byte	4
	.byte	'CH8',0,48
	.word	34156
	.byte	3,35,128,4,4
	.byte	'TGC1_GLB_CTRL',0,4
	.word	30698
	.byte	3,35,176,4,4
	.byte	'TGC1_ACT_TB',0,4
	.word	30395
	.byte	3,35,180,4,4
	.byte	'TGC1_FUPD_CTRL',0,4
	.word	30622
	.byte	3,35,184,4,4
	.byte	'TGC1_INT_TRIG',0,4
	.word	30773
	.byte	3,35,188,4,12
	.word	31979
	.byte	4
	.byte	'CH9',0,48
	.word	34270
	.byte	3,35,192,4,4
	.byte	'TGC1_ENDIS_CTRL',0,4
	.word	30468
	.byte	3,35,240,4,4
	.byte	'TGC1_ENDIS_STAT',0,4
	.word	30545
	.byte	3,35,244,4,4
	.byte	'TGC1_OUTEN_CTRL',0,4
	.word	30848
	.byte	3,35,248,4,4
	.byte	'TGC1_OUTEN_STAT',0,4
	.word	30925
	.byte	3,35,252,4,12
	.word	31979
	.byte	4
	.byte	'CH10',0,48
	.word	34393
	.byte	3,35,128,5,4
	.byte	'reserved_2B0',0,16
	.word	33916
	.byte	3,35,176,5,12
	.word	31979
	.byte	4
	.byte	'CH11',0,48
	.word	34436
	.byte	3,35,192,5,4
	.byte	'reserved_2F0',0,16
	.word	33916
	.byte	3,35,240,5,12
	.word	31979
	.byte	4
	.byte	'CH12',0,48
	.word	34479
	.byte	3,35,128,6,4
	.byte	'reserved_330',0,16
	.word	33916
	.byte	3,35,176,6,12
	.word	31979
	.byte	4
	.byte	'CH13',0,48
	.word	34522
	.byte	3,35,192,6,4
	.byte	'reserved_370',0,16
	.word	33916
	.byte	3,35,240,6,12
	.word	31979
	.byte	4
	.byte	'CH14',0,48
	.word	34565
	.byte	3,35,128,7,4
	.byte	'reserved_3B0',0,16
	.word	33916
	.byte	3,35,176,7,12
	.word	31979
	.byte	4
	.byte	'CH15',0,48
	.word	34608
	.byte	3,35,192,7,10,144,8
	.word	202
	.byte	11,143,8,0,4
	.byte	'reserved_3F0',0,144,8
	.word	34628
	.byte	3,35,240,7,0,12
	.word	33650
	.byte	14
	.byte	'Ifx_GTM_TOM',0,2,199,18,3
	.word	34664
	.byte	12
	.word	513
	.byte	14
	.byte	'Ifx_GTM_TOM_CH_TYPE',0,1,155,4,4
	.word	34690
	.byte	12
	.word	2316
	.byte	14
	.byte	'Ifx_GTM_TOM_TGC_TYPE',0,1,177,4,5
	.word	34724
	.byte	3,1,179,4,20,128,16,4
	.byte	'reserved_tom0',0,48
	.word	3702
	.byte	2,35,0,10,128,8
	.word	2316
	.byte	11,1,0,12
	.word	34789
	.byte	4
	.byte	'TGC',0,128,8
	.word	34799
	.byte	2,35,48,10,208,7
	.word	202
	.byte	11,207,7,0,4
	.byte	'reserved_tgc2',0,208,7
	.word	34818
	.byte	3,35,176,8,0,12
	.word	34759
	.byte	14
	.byte	'Ifx_GTM_TOM_TGCx',0,1,184,4,5
	.word	34855
	.byte	3,1,187,4,20,128,16,10,128,8
	.word	513
	.byte	11,15,0,12
	.word	34893
	.byte	4
	.byte	'CH',0,128,8
	.word	34903
	.byte	2,35,0,10,128,8
	.word	202
	.byte	11,255,7,0,4
	.byte	'reserved_tom1',0,128,8
	.word	34921
	.byte	3,35,128,8,0,12
	.word	34886
	.byte	14
	.byte	'Ifx_GTM_TOM_CHx',0,1,191,4,5
	.word	34958
	.byte	12
	.word	5297
	.byte	14
	.byte	'Ifx_GTM_TIM_CH_TYPE',0,1,248,4,4
	.word	34988
	.byte	3,1,250,4,20,8,4
	.byte	'IN_SRC',0,4
	.word	29573
	.byte	2,35,0,4
	.byte	'RST',0,4
	.word	29710
	.byte	2,35,4,0,12
	.word	35022
	.byte	14
	.byte	'Ifx_GTM_TIM_IN_SRC_RESET_TYPE',0,1,255,4,4
	.word	35058
	.byte	3,1,129,5,21,128,16,10,128,8
	.word	5297
	.byte	11,7,0,12
	.word	35109
	.byte	4
	.byte	'CH',0,128,8
	.word	35119
	.byte	2,35,0,4
	.byte	'reserved_tim1',0,128,8
	.word	34921
	.byte	3,35,128,8,0,12
	.word	35102
	.byte	14
	.byte	'Ifx_GTM_TIM_CHx',0,1,133,5,4
	.word	35163
	.byte	3,1,135,5,20,128,16,10,120
	.word	202
	.byte	11,119,0,4
	.byte	'reserved_tim2',0,120
	.word	35200
	.byte	2,35,0,12
	.word	35022
	.byte	4
	.byte	'IN_SRC_RESET',0,8
	.word	35232
	.byte	2,35,120,10,128,15
	.word	202
	.byte	11,255,14,0,4
	.byte	'reserved_tim3',0,128,15
	.word	35259
	.byte	3,35,128,1,0,12
	.word	35193
	.byte	14
	.byte	'Ifx_GTM_TIM_IN_SRC_RSTx',0,1,140,5,4
	.word	35296
	.byte	16,1,174,5,11,1,17
	.byte	'GTM_CONFIGURABLE_CLK0',0,0,17
	.byte	'GTM_CONFIGURABLE_CLK1',0,1,17
	.byte	'GTM_CONFIGURABLE_CLK2',0,2,17
	.byte	'GTM_CONFIGURABLE_CLK3',0,3,17
	.byte	'GTM_CONFIGURABLE_CLK4',0,4,17
	.byte	'GTM_CONFIGURABLE_CLK5',0,5,17
	.byte	'GTM_CONFIGURABLE_CLK6',0,6,17
	.byte	'GTM_CONFIGURABLE_CLK7',0,7,0,14
	.byte	'Gtm_ConfigurableClockType',0,1,184,5,4
	.word	35334
	.byte	16,1,188,5,11,1,17
	.byte	'GTM_LOW',0,0,17
	.byte	'GTM_HIGH',0,1,0,14
	.byte	'Gtm_OutputLevelType',0,1,192,5,4
	.word	35568
	.byte	16,1,195,5,11,1,17
	.byte	'TOM_GLB_CTRL',0,0,17
	.byte	'TOM_ACT_TB',0,1,17
	.byte	'TOM_FUPD_CTRL',0,2,17
	.byte	'TOM_INT_TRIG',0,3,17
	.byte	'TOM_RESERVED_0',0,4,17
	.byte	'TOM_RESERVED_1',0,5,17
	.byte	'TOM_RESERVED_2',0,6,17
	.byte	'TOM_RESERVED_3',0,7,17
	.byte	'TOM_RESERVED_4',0,8,17
	.byte	'TOM_RESERVED_5',0,9,17
	.byte	'TOM_RESERVED_6',0,10,17
	.byte	'TOM_RESERVED_7',0,11,17
	.byte	'TOM_RESERVED_8',0,12,17
	.byte	'TOM_RESERVED_9',0,13,17
	.byte	'TOM_RESERVED_10',0,14,17
	.byte	'TOM_RESERVED_11',0,15,17
	.byte	'TOM_ENDIS_CTRL',0,16,17
	.byte	'TOM_ENDIS_STAT',0,17,17
	.byte	'TOM_OUTEN_CTRL',0,18,17
	.byte	'TOM_OUTEN_STAT',0,19,0,14
	.byte	'Gtm_TomTimerRegistersType',0,1,217,5,4
	.word	35625
	.byte	3,1,221,5,11,8,4
	.byte	'FltRisingEdge',0,4
	.word	181
	.byte	2,35,0,4
	.byte	'FltFallingEdge',0,4
	.word	181
	.byte	2,35,4,0,14
	.byte	'Gtm_TimFilterType',0,1,225,5,4
	.word	36000
	.byte	14
	.byte	'Gtm_TbuChCtrlType',0,1,230,5,32
	.word	28595
	.byte	14
	.byte	'Gtm_TbuChBaseType',0,1,231,5,32
	.word	28525
	.byte	3,1,233,5,11,8,4
	.byte	'CH_CTRL',0,4
	.word	28595
	.byte	2,35,0,4
	.byte	'CH_BASE',0,4
	.word	28525
	.byte	2,35,4,0,14
	.byte	'Gtm_TbuChType',0,1,237,5,4
	.word	36135
	.byte	3,1,249,5,9,36,10,4
	.word	181
	.byte	11,0,0,4
	.byte	'TimInSel',0,4
	.word	36205
	.byte	2,35,0,10,32
	.word	181
	.byte	11,7,0,4
	.byte	'ToutSel',0,32
	.word	36232
	.byte	2,35,4,0,14
	.byte	'Gtm_PortConfigType',0,1,253,5,2
	.word	36199
	.byte	14
	.byte	'Gtm_TimFltType',0,1,134,6,2
	.word	5124
	.byte	14
	.byte	'Gtm_TimConfigType',0,1,151,6,4
	.word	5017
	.byte	3,1,154,6,11,40,10,8
	.word	202
	.byte	11,7,0,4
	.byte	'Gtm_TimUsage',0,8
	.word	36344
	.byte	2,35,0,10,16
	.word	202
	.byte	11,15,0,10,32
	.word	36375
	.byte	11,1,0,4
	.byte	'Gtm_TomUsage',0,32
	.word	36384
	.byte	2,35,8,0,14
	.byte	'Gtm_ModUsageConfigType',0,1,163,6,4
	.word	36338
	.byte	3,1,177,6,9,16,4
	.byte	'GtmTomUpdateEn',0,2
	.word	320
	.byte	2,35,0,4
	.byte	'GtmTomEndisCtrl',0,2
	.word	320
	.byte	2,35,2,4
	.byte	'GtmTomEndisStat',0,2
	.word	320
	.byte	2,35,4,4
	.byte	'GtmTomOutenCtrl',0,2
	.word	320
	.byte	2,35,6,4
	.byte	'GtmTomOutenStat',0,2
	.word	320
	.byte	2,35,8,4
	.byte	'GtmTomFupd',0,4
	.word	181
	.byte	2,35,10,0,14
	.byte	'Gtm_TomTgcConfigGroupType',0,1,185,6,2
	.word	36448
	.byte	3,1,189,6,9,12,4
	.byte	'GtmTomIntTrig',0,2
	.word	320
	.byte	2,35,0,4
	.byte	'GtmTomActTb',0,4
	.word	181
	.byte	2,35,2,5
	.word	36448
	.byte	6
	.word	36684
	.byte	4
	.byte	'GtmTomTgcConfigGrpPtr',0,4
	.word	36689
	.byte	2,35,8,0,14
	.byte	'Gtm_TomTgcConfigType',0,1,196,6,2
	.word	36634
	.byte	14
	.byte	'Gtm_TomChannelConfigType',0,1,207,6,2
	.word	293
	.byte	14
	.byte	'Gtm_TomConfigType',0,1,219,6,2
	.word	219
	.byte	3,1,223,6,9,8,4
	.byte	'CmuEclkNum',0,4
	.word	181
	.byte	2,35,0,4
	.byte	'CmuEclkDen',0,4
	.word	181
	.byte	2,35,4,0,14
	.byte	'Gtm_ExtClkType',0,1,227,6,2
	.word	36817
	.byte	3,1,230,6,9,64,4
	.byte	'GtmClockEnable',0,4
	.word	181
	.byte	2,35,0,4
	.byte	'GtmCmuClkCnt',0,32
	.word	36232
	.byte	2,35,4,4
	.byte	'GtmFxdClkControl',0,4
	.word	181
	.byte	2,35,36,10,24
	.word	36817
	.byte	11,2,0,4
	.byte	'GtmEclk',0,24
	.word	36966
	.byte	2,35,40,0,14
	.byte	'Gtm_ClockSettingType',0,1,236,6,2
	.word	36888
	.byte	3,1,240,6,9,4,4
	.byte	'GtmCtrlValue',0,2
	.word	320
	.byte	2,35,0,4
	.byte	'GtmIrqEnable',0,2
	.word	320
	.byte	2,35,2,0,14
	.byte	'Gtm_GeneralConfigType',0,1,245,6,2
	.word	37023
	.byte	3,1,249,6,9,6,4
	.byte	'TbuChannelCtrl',0,1
	.word	202
	.byte	2,35,0,4
	.byte	'TbuBaseValue',0,4
	.word	181
	.byte	2,35,2,0,14
	.byte	'Gtm_TbuConfigType',0,1,253,6,2
	.word	37105
	.byte	3,1,129,7,9,72,4
	.byte	'GtmModuleSleepEnable',0,1
	.word	202
	.byte	2,35,0,4
	.byte	'GtmGclkNum',0,4
	.word	181
	.byte	2,35,2,4
	.byte	'GtmGclkDen',0,4
	.word	181
	.byte	2,35,6,4
	.byte	'GtmAccessEnable0',0,4
	.word	181
	.byte	2,35,10,4
	.byte	'GtmAccessEnable1',0,4
	.word	181
	.byte	2,35,14,10,2
	.word	320
	.byte	11,0,0,4
	.byte	'GtmTimModuleUsage',0,2
	.word	37313
	.byte	2,35,18,10,1
	.word	202
	.byte	11,0,0,4
	.byte	'GtmTimUsage',0,1
	.word	37349
	.byte	2,35,20,4
	.byte	'GtmTimConfigPtr',0,4
	.word	5292
	.byte	2,35,24,4
	.byte	'GtmTomTgcUsage',0,1
	.word	37349
	.byte	2,35,28,5
	.word	36634
	.byte	6
	.word	37428
	.byte	4
	.byte	'GtmTomTgcConfigPtr',0,4
	.word	37433
	.byte	2,35,32,10,8
	.word	181
	.byte	11,1,0,4
	.byte	'GtmTomModuleUsage',0,8
	.word	37466
	.byte	2,35,36,4
	.byte	'GtmTomUsage',0,4
	.word	36205
	.byte	2,35,44,4
	.byte	'GtmTomConfigPtr',0,4
	.word	508
	.byte	2,35,48,5
	.word	36338
	.byte	6
	.word	37548
	.byte	4
	.byte	'GtmModUsageConfigPtr',0,4
	.word	37553
	.byte	2,35,52,5
	.word	37023
	.byte	6
	.word	37588
	.byte	4
	.byte	'GtmGeneralConfigPtr',0,4
	.word	37593
	.byte	2,35,56,5
	.word	37105
	.byte	6
	.word	37627
	.byte	4
	.byte	'GtmTbuConfigPtr',0,4
	.word	37632
	.byte	2,35,60,5
	.word	202
	.byte	6
	.word	37662
	.byte	4
	.byte	'GtmAdcConnectionsPtr',0,4
	.word	37667
	.byte	2,35,64,4
	.byte	'GtmTtcanTriggers',0,2
	.word	37313
	.byte	2,35,68,0,14
	.byte	'Gtm_ModuleConfigType',0,1,163,7,2
	.word	37185
	.byte	18,1,1,19
	.word	202
	.byte	19
	.word	202
	.byte	19
	.word	202
	.byte	19
	.word	320
	.byte	0,6
	.word	37759
	.byte	14
	.byte	'Gtm_NotificationPtrType',0,1,172,7,16
	.word	37783
	.byte	8
	.byte	'Gtm_ConfigType',0,1,192,7,16,12,5
	.word	36888
	.byte	6
	.word	37842
	.byte	4
	.byte	'GtmClockSettingPtr',0,4
	.word	37847
	.byte	2,35,0,5
	.word	36199
	.byte	6
	.word	37880
	.byte	4
	.byte	'GtmPortConfigPtr',0,4
	.word	37885
	.byte	2,35,4,5
	.word	37185
	.byte	6
	.word	37916
	.byte	4
	.byte	'GtmModuleConfigPtr',0,4
	.word	37921
	.byte	2,35,8,0,14
	.byte	'Gtm_ConfigType',0,1,197,7,2
	.word	37821
	.byte	5
	.word	37821
	.byte	6
	.word	37979
	.byte	20
	.byte	'Gtm_kConfigPtr',0,1,216,7,30
	.word	37984
	.byte	1,1,8
	.byte	'_Ifx_SRC_SRCR_Bits',0,5,45,16,4,9
	.byte	'SRPN',0,1
	.word	202
	.byte	8,0,2,35,0,9
	.byte	'reserved_8',0,1
	.word	202
	.byte	2,6,2,35,1,9
	.byte	'SRE',0,1
	.word	202
	.byte	1,5,2,35,1,9
	.byte	'TOS',0,1
	.word	202
	.byte	1,4,2,35,1,9
	.byte	'reserved_12',0,1
	.word	202
	.byte	4,0,2,35,1,9
	.byte	'ECC',0,1
	.word	202
	.byte	5,3,2,35,2,9
	.byte	'reserved_21',0,1
	.word	202
	.byte	3,0,2,35,2,9
	.byte	'SRR',0,1
	.word	202
	.byte	1,7,2,35,3,9
	.byte	'CLRR',0,1
	.word	202
	.byte	1,6,2,35,3,9
	.byte	'SETR',0,1
	.word	202
	.byte	1,5,2,35,3,9
	.byte	'IOV',0,1
	.word	202
	.byte	1,4,2,35,3,9
	.byte	'IOVCLR',0,1
	.word	202
	.byte	1,3,2,35,3,9
	.byte	'SWS',0,1
	.word	202
	.byte	1,2,2,35,3,9
	.byte	'SWSCLR',0,1
	.word	202
	.byte	1,1,2,35,3,9
	.byte	'reserved_31',0,1
	.word	202
	.byte	1,0,2,35,3,0,14
	.byte	'Ifx_SRC_SRCR_Bits',0,5,62,3
	.word	38015
	.byte	7,5,70,9,4,4
	.byte	'U',0,4
	.word	525
	.byte	2,35,0,4
	.byte	'I',0,4
	.word	552
	.byte	2,35,0,4
	.byte	'B',0,4
	.word	38015
	.byte	2,35,0,0,14
	.byte	'Ifx_SRC_SRCR',0,5,75,3
	.word	38331
	.byte	8
	.byte	'_Ifx_SRC_ASCLIN',0,5,86,25,12,4
	.byte	'TX',0,4
	.word	38331
	.byte	2,35,0,4
	.byte	'RX',0,4
	.word	38331
	.byte	2,35,4,4
	.byte	'ERR',0,4
	.word	38331
	.byte	2,35,8,0,12
	.word	38391
	.byte	14
	.byte	'Ifx_SRC_ASCLIN',0,5,91,3
	.word	38450
	.byte	8
	.byte	'_Ifx_SRC_BCUSPB',0,5,94,25,4,4
	.byte	'SBSRC',0,4
	.word	38331
	.byte	2,35,0,0,12
	.word	38478
	.byte	14
	.byte	'Ifx_SRC_BCUSPB',0,5,97,3
	.word	38515
	.byte	8
	.byte	'_Ifx_SRC_CAN',0,5,100,25,64,10,64
	.word	38331
	.byte	11,15,0,4
	.byte	'INT',0,64
	.word	38561
	.byte	2,35,0,0,12
	.word	38543
	.byte	14
	.byte	'Ifx_SRC_CAN',0,5,103,3
	.word	38584
	.byte	8
	.byte	'_Ifx_SRC_CAN1',0,5,106,25,32,10,32
	.word	38331
	.byte	11,7,0,4
	.byte	'INT',0,32
	.word	38628
	.byte	2,35,0,0,12
	.word	38609
	.byte	14
	.byte	'Ifx_SRC_CAN1',0,5,109,3
	.word	38651
	.byte	8
	.byte	'_Ifx_SRC_CCU6',0,5,112,25,16,4
	.byte	'SR0',0,4
	.word	38331
	.byte	2,35,0,4
	.byte	'SR1',0,4
	.word	38331
	.byte	2,35,4,4
	.byte	'SR2',0,4
	.word	38331
	.byte	2,35,8,4
	.byte	'SR3',0,4
	.word	38331
	.byte	2,35,12,0,12
	.word	38677
	.byte	14
	.byte	'Ifx_SRC_CCU6',0,5,118,3
	.word	38749
	.byte	8
	.byte	'_Ifx_SRC_CERBERUS',0,5,121,25,8,10,8
	.word	38331
	.byte	11,1,0,4
	.byte	'SR',0,8
	.word	38798
	.byte	2,35,0,0,12
	.word	38775
	.byte	14
	.byte	'Ifx_SRC_CERBERUS',0,5,124,3
	.word	38820
	.byte	8
	.byte	'_Ifx_SRC_CPU',0,5,127,25,32,4
	.byte	'SBSRC',0,4
	.word	38331
	.byte	2,35,0,4
	.byte	'reserved_4',0,28
	.word	31582
	.byte	2,35,4,0,12
	.word	38850
	.byte	14
	.byte	'Ifx_SRC_CPU',0,5,131,1,3
	.word	38904
	.byte	8
	.byte	'_Ifx_SRC_DMA',0,5,134,1,25,80,4
	.byte	'ERR',0,4
	.word	38331
	.byte	2,35,0,4
	.byte	'reserved_4',0,12
	.word	32769
	.byte	2,35,4,4
	.byte	'CH',0,64
	.word	38561
	.byte	2,35,16,0,12
	.word	38930
	.byte	14
	.byte	'Ifx_SRC_DMA',0,5,139,1,3
	.word	38995
	.byte	8
	.byte	'_Ifx_SRC_EMEM',0,5,142,1,25,4,4
	.byte	'SR',0,4
	.word	38331
	.byte	2,35,0,0,12
	.word	39021
	.byte	14
	.byte	'Ifx_SRC_EMEM',0,5,145,1,3
	.word	39054
	.byte	8
	.byte	'_Ifx_SRC_ERAY',0,5,148,1,25,80,4
	.byte	'INT',0,8
	.word	38798
	.byte	2,35,0,4
	.byte	'TINT',0,8
	.word	38798
	.byte	2,35,8,4
	.byte	'NDAT',0,8
	.word	38798
	.byte	2,35,16,4
	.byte	'MBSC',0,8
	.word	38798
	.byte	2,35,24,4
	.byte	'OBUSY',0,4
	.word	38331
	.byte	2,35,32,4
	.byte	'IBUSY',0,4
	.word	38331
	.byte	2,35,36,10,40
	.word	202
	.byte	11,39,0,4
	.byte	'reserved_28',0,40
	.word	39186
	.byte	2,35,40,0,12
	.word	39081
	.byte	14
	.byte	'Ifx_SRC_ERAY',0,5,157,1,3
	.word	39217
	.byte	8
	.byte	'_Ifx_SRC_ETH',0,5,160,1,25,4,4
	.byte	'SR',0,4
	.word	38331
	.byte	2,35,0,0,12
	.word	39244
	.byte	14
	.byte	'Ifx_SRC_ETH',0,5,163,1,3
	.word	39276
	.byte	8
	.byte	'_Ifx_SRC_EVR',0,5,166,1,25,8,4
	.byte	'WUT',0,4
	.word	38331
	.byte	2,35,0,4
	.byte	'SCDC',0,4
	.word	38331
	.byte	2,35,4,0,12
	.word	39302
	.byte	14
	.byte	'Ifx_SRC_EVR',0,5,170,1,3
	.word	39349
	.byte	8
	.byte	'_Ifx_SRC_FFT',0,5,173,1,25,12,4
	.byte	'DONE',0,4
	.word	38331
	.byte	2,35,0,4
	.byte	'ERR',0,4
	.word	38331
	.byte	2,35,4,4
	.byte	'RFS',0,4
	.word	38331
	.byte	2,35,8,0,12
	.word	39375
	.byte	14
	.byte	'Ifx_SRC_FFT',0,5,178,1,3
	.word	39435
	.byte	8
	.byte	'_Ifx_SRC_GPSR',0,5,181,1,25,128,12,4
	.byte	'SR0',0,4
	.word	38331
	.byte	2,35,0,4
	.byte	'SR1',0,4
	.word	38331
	.byte	2,35,4,4
	.byte	'SR2',0,4
	.word	38331
	.byte	2,35,8,4
	.byte	'SR3',0,4
	.word	38331
	.byte	2,35,12,10,240,11
	.word	202
	.byte	11,239,11,0,4
	.byte	'reserved_10',0,240,11
	.word	39534
	.byte	2,35,16,0,12
	.word	39461
	.byte	14
	.byte	'Ifx_SRC_GPSR',0,5,188,1,3
	.word	39568
	.byte	8
	.byte	'_Ifx_SRC_GPT12',0,5,191,1,25,48,4
	.byte	'CIRQ',0,4
	.word	38331
	.byte	2,35,0,4
	.byte	'T2',0,4
	.word	38331
	.byte	2,35,4,4
	.byte	'T3',0,4
	.word	38331
	.byte	2,35,8,4
	.byte	'T4',0,4
	.word	38331
	.byte	2,35,12,4
	.byte	'T5',0,4
	.word	38331
	.byte	2,35,16,4
	.byte	'T6',0,4
	.word	38331
	.byte	2,35,20,10,24
	.word	202
	.byte	11,23,0,4
	.byte	'reserved_18',0,24
	.word	39690
	.byte	2,35,24,0,12
	.word	39595
	.byte	14
	.byte	'Ifx_SRC_GPT12',0,5,200,1,3
	.word	39721
	.byte	8
	.byte	'_Ifx_SRC_GTM',0,5,203,1,25,192,11,4
	.byte	'AEIIRQ',0,4
	.word	38331
	.byte	2,35,0,10,236,2
	.word	202
	.byte	11,235,2,0,4
	.byte	'reserved_4',0,236,2
	.word	39785
	.byte	2,35,4,4
	.byte	'ERR',0,4
	.word	38331
	.byte	3,35,240,2,4
	.byte	'reserved_174',0,12
	.word	32769
	.byte	3,35,244,2,10,32
	.word	38628
	.byte	11,0,0,4
	.byte	'TIM',0,32
	.word	39854
	.byte	3,35,128,3,10,224,7
	.word	202
	.byte	11,223,7,0,4
	.byte	'reserved_1A0',0,224,7
	.word	39877
	.byte	3,35,160,3,10,64
	.word	38628
	.byte	11,1,0,4
	.byte	'TOM',0,64
	.word	39912
	.byte	3,35,128,11,0,12
	.word	39749
	.byte	14
	.byte	'Ifx_SRC_GTM',0,5,212,1,3
	.word	39936
	.byte	8
	.byte	'_Ifx_SRC_HSM',0,5,215,1,25,8,4
	.byte	'HSM',0,8
	.word	38798
	.byte	2,35,0,0,12
	.word	39962
	.byte	14
	.byte	'Ifx_SRC_HSM',0,5,218,1,3
	.word	39995
	.byte	8
	.byte	'_Ifx_SRC_LMU',0,5,221,1,25,4,4
	.byte	'SR',0,4
	.word	38331
	.byte	2,35,0,0,12
	.word	40021
	.byte	14
	.byte	'Ifx_SRC_LMU',0,5,224,1,3
	.word	40053
	.byte	8
	.byte	'_Ifx_SRC_PMU',0,5,227,1,25,4,4
	.byte	'SR',0,4
	.word	38331
	.byte	2,35,0,0,12
	.word	40079
	.byte	14
	.byte	'Ifx_SRC_PMU',0,5,230,1,3
	.word	40111
	.byte	8
	.byte	'_Ifx_SRC_QSPI',0,5,233,1,25,24,4
	.byte	'TX',0,4
	.word	38331
	.byte	2,35,0,4
	.byte	'RX',0,4
	.word	38331
	.byte	2,35,4,4
	.byte	'ERR',0,4
	.word	38331
	.byte	2,35,8,4
	.byte	'PT',0,4
	.word	38331
	.byte	2,35,12,4
	.byte	'HC',0,4
	.word	38331
	.byte	2,35,16,4
	.byte	'U',0,4
	.word	38331
	.byte	2,35,20,0,12
	.word	40137
	.byte	14
	.byte	'Ifx_SRC_QSPI',0,5,241,1,3
	.word	40230
	.byte	8
	.byte	'_Ifx_SRC_SCU',0,5,244,1,25,20,4
	.byte	'DTS',0,4
	.word	38331
	.byte	2,35,0,10,16
	.word	38331
	.byte	11,3,0,4
	.byte	'ERU',0,16
	.word	40289
	.byte	2,35,4,0,12
	.word	40257
	.byte	14
	.byte	'Ifx_SRC_SCU',0,5,248,1,3
	.word	40312
	.byte	8
	.byte	'_Ifx_SRC_SENT',0,5,251,1,25,16,4
	.byte	'SR',0,16
	.word	40289
	.byte	2,35,0,0,12
	.word	40338
	.byte	14
	.byte	'Ifx_SRC_SENT',0,5,254,1,3
	.word	40371
	.byte	8
	.byte	'_Ifx_SRC_SMU',0,5,129,2,25,12,10,12
	.word	38331
	.byte	11,2,0,4
	.byte	'SR',0,12
	.word	40417
	.byte	2,35,0,0,12
	.word	40398
	.byte	14
	.byte	'Ifx_SRC_SMU',0,5,132,2,3
	.word	40439
	.byte	8
	.byte	'_Ifx_SRC_STM',0,5,135,2,25,96,4
	.byte	'SR0',0,4
	.word	38331
	.byte	2,35,0,4
	.byte	'SR1',0,4
	.word	38331
	.byte	2,35,4,10,88
	.word	202
	.byte	11,87,0,4
	.byte	'reserved_8',0,88
	.word	40510
	.byte	2,35,8,0,12
	.word	40465
	.byte	14
	.byte	'Ifx_SRC_STM',0,5,140,2,3
	.word	40540
	.byte	8
	.byte	'_Ifx_SRC_VADCCG',0,5,143,2,25,192,2,4
	.byte	'SR0',0,4
	.word	38331
	.byte	2,35,0,4
	.byte	'SR1',0,4
	.word	38331
	.byte	2,35,4,4
	.byte	'SR2',0,4
	.word	38331
	.byte	2,35,8,4
	.byte	'SR3',0,4
	.word	38331
	.byte	2,35,12,10,176,2
	.word	202
	.byte	11,175,2,0,4
	.byte	'reserved_10',0,176,2
	.word	40641
	.byte	2,35,16,0,12
	.word	40566
	.byte	14
	.byte	'Ifx_SRC_VADCCG',0,5,150,2,3
	.word	40675
	.byte	8
	.byte	'_Ifx_SRC_VADCG',0,5,153,2,25,16,4
	.byte	'SR0',0,4
	.word	38331
	.byte	2,35,0,4
	.byte	'SR1',0,4
	.word	38331
	.byte	2,35,4,4
	.byte	'SR2',0,4
	.word	38331
	.byte	2,35,8,4
	.byte	'SR3',0,4
	.word	38331
	.byte	2,35,12,0,12
	.word	40704
	.byte	14
	.byte	'Ifx_SRC_VADCG',0,5,159,2,3
	.word	40778
	.byte	8
	.byte	'_Ifx_SRC_XBAR',0,5,162,2,25,4,4
	.byte	'SRC',0,4
	.word	38331
	.byte	2,35,0,0,12
	.word	40806
	.byte	14
	.byte	'Ifx_SRC_XBAR',0,5,165,2,3
	.word	40840
	.byte	8
	.byte	'_Ifx_SRC_GASCLIN',0,5,178,2,25,24,10,24
	.word	38391
	.byte	11,1,0,12
	.word	40890
	.byte	4
	.byte	'ASCLIN',0,24
	.word	40899
	.byte	2,35,0,0,12
	.word	40867
	.byte	14
	.byte	'Ifx_SRC_GASCLIN',0,5,181,2,3
	.word	40921
	.byte	8
	.byte	'_Ifx_SRC_GBCU',0,5,184,2,25,4,12
	.word	38478
	.byte	4
	.byte	'SPB',0,4
	.word	40971
	.byte	2,35,0,0,12
	.word	40951
	.byte	14
	.byte	'Ifx_SRC_GBCU',0,5,187,2,3
	.word	40990
	.byte	8
	.byte	'_Ifx_SRC_GCAN',0,5,190,2,25,96,10,64
	.word	38543
	.byte	11,0,0,12
	.word	41037
	.byte	4
	.byte	'CAN',0,64
	.word	41046
	.byte	2,35,0,10,32
	.word	38609
	.byte	11,0,0,12
	.word	41064
	.byte	4
	.byte	'CAN1',0,32
	.word	41073
	.byte	2,35,64,0,12
	.word	41017
	.byte	14
	.byte	'Ifx_SRC_GCAN',0,5,194,2,3
	.word	41093
	.byte	8
	.byte	'_Ifx_SRC_GCCU6',0,5,197,2,25,32,10,32
	.word	38677
	.byte	11,1,0,12
	.word	41141
	.byte	4
	.byte	'CCU6',0,32
	.word	41150
	.byte	2,35,0,0,12
	.word	41120
	.byte	14
	.byte	'Ifx_SRC_GCCU6',0,5,200,2,3
	.word	41170
	.byte	8
	.byte	'_Ifx_SRC_GCERBERUS',0,5,203,2,25,8,12
	.word	38775
	.byte	4
	.byte	'CERBERUS',0,8
	.word	41223
	.byte	2,35,0,0,12
	.word	41198
	.byte	14
	.byte	'Ifx_SRC_GCERBERUS',0,5,206,2,3
	.word	41247
	.byte	8
	.byte	'_Ifx_SRC_GCPU',0,5,209,2,25,32,10,32
	.word	38850
	.byte	11,0,0,12
	.word	41299
	.byte	4
	.byte	'CPU',0,32
	.word	41308
	.byte	2,35,0,0,12
	.word	41279
	.byte	14
	.byte	'Ifx_SRC_GCPU',0,5,212,2,3
	.word	41327
	.byte	8
	.byte	'_Ifx_SRC_GDMA',0,5,215,2,25,80,10,80
	.word	38930
	.byte	11,0,0,12
	.word	41374
	.byte	4
	.byte	'DMA',0,80
	.word	41383
	.byte	2,35,0,0,12
	.word	41354
	.byte	14
	.byte	'Ifx_SRC_GDMA',0,5,218,2,3
	.word	41402
	.byte	8
	.byte	'_Ifx_SRC_GEMEM',0,5,221,2,25,4,10,4
	.word	39021
	.byte	11,0,0,12
	.word	41450
	.byte	4
	.byte	'EMEM',0,4
	.word	41459
	.byte	2,35,0,0,12
	.word	41429
	.byte	14
	.byte	'Ifx_SRC_GEMEM',0,5,224,2,3
	.word	41479
	.byte	8
	.byte	'_Ifx_SRC_GERAY',0,5,227,2,25,80,10,80
	.word	39081
	.byte	11,0,0,12
	.word	41528
	.byte	4
	.byte	'ERAY',0,80
	.word	41537
	.byte	2,35,0,0,12
	.word	41507
	.byte	14
	.byte	'Ifx_SRC_GERAY',0,5,230,2,3
	.word	41557
	.byte	8
	.byte	'_Ifx_SRC_GETH',0,5,233,2,25,4,10,4
	.word	39244
	.byte	11,0,0,12
	.word	41605
	.byte	4
	.byte	'ETH',0,4
	.word	41614
	.byte	2,35,0,0,12
	.word	41585
	.byte	14
	.byte	'Ifx_SRC_GETH',0,5,236,2,3
	.word	41633
	.byte	8
	.byte	'_Ifx_SRC_GEVR',0,5,239,2,25,8,10,8
	.word	39302
	.byte	11,0,0,12
	.word	41680
	.byte	4
	.byte	'EVR',0,8
	.word	41689
	.byte	2,35,0,0,12
	.word	41660
	.byte	14
	.byte	'Ifx_SRC_GEVR',0,5,242,2,3
	.word	41708
	.byte	8
	.byte	'_Ifx_SRC_GFFT',0,5,245,2,25,12,10,12
	.word	39375
	.byte	11,0,0,12
	.word	41755
	.byte	4
	.byte	'FFT',0,12
	.word	41764
	.byte	2,35,0,0,12
	.word	41735
	.byte	14
	.byte	'Ifx_SRC_GFFT',0,5,248,2,3
	.word	41783
	.byte	8
	.byte	'_Ifx_SRC_GGPSR',0,5,251,2,25,128,12,10,128,12
	.word	39461
	.byte	11,0,0,12
	.word	41832
	.byte	4
	.byte	'GPSR',0,128,12
	.word	41842
	.byte	2,35,0,0,12
	.word	41810
	.byte	14
	.byte	'Ifx_SRC_GGPSR',0,5,254,2,3
	.word	41863
	.byte	8
	.byte	'_Ifx_SRC_GGPT12',0,5,129,3,25,48,10,48
	.word	39595
	.byte	11,0,0,12
	.word	41913
	.byte	4
	.byte	'GPT12',0,48
	.word	41922
	.byte	2,35,0,0,12
	.word	41891
	.byte	14
	.byte	'Ifx_SRC_GGPT12',0,5,132,3,3
	.word	41943
	.byte	8
	.byte	'_Ifx_SRC_GGTM',0,5,135,3,25,192,11,10,192,11
	.word	39749
	.byte	11,0,0,12
	.word	41993
	.byte	4
	.byte	'GTM',0,192,11
	.word	42003
	.byte	2,35,0,0,12
	.word	41972
	.byte	14
	.byte	'Ifx_SRC_GGTM',0,5,138,3,3
	.word	42023
	.byte	8
	.byte	'_Ifx_SRC_GHSM',0,5,141,3,25,8,10,8
	.word	39962
	.byte	11,0,0,12
	.word	42070
	.byte	4
	.byte	'HSM',0,8
	.word	42079
	.byte	2,35,0,0,12
	.word	42050
	.byte	14
	.byte	'Ifx_SRC_GHSM',0,5,144,3,3
	.word	42098
	.byte	8
	.byte	'_Ifx_SRC_GLMU',0,5,147,3,25,4,10,4
	.word	40021
	.byte	11,0,0,12
	.word	42145
	.byte	4
	.byte	'LMU',0,4
	.word	42154
	.byte	2,35,0,0,12
	.word	42125
	.byte	14
	.byte	'Ifx_SRC_GLMU',0,5,150,3,3
	.word	42173
	.byte	8
	.byte	'_Ifx_SRC_GPMU',0,5,153,3,25,8,10,8
	.word	40079
	.byte	11,1,0,12
	.word	42220
	.byte	4
	.byte	'PMU',0,8
	.word	42229
	.byte	2,35,0,0,12
	.word	42200
	.byte	14
	.byte	'Ifx_SRC_GPMU',0,5,156,3,3
	.word	42248
	.byte	8
	.byte	'_Ifx_SRC_GQSPI',0,5,159,3,25,96,10,96
	.word	40137
	.byte	11,3,0,12
	.word	42296
	.byte	4
	.byte	'QSPI',0,96
	.word	42305
	.byte	2,35,0,0,12
	.word	42275
	.byte	14
	.byte	'Ifx_SRC_GQSPI',0,5,162,3,3
	.word	42325
	.byte	8
	.byte	'_Ifx_SRC_GSCU',0,5,165,3,25,20,12
	.word	40257
	.byte	4
	.byte	'SCU',0,20
	.word	42373
	.byte	2,35,0,0,12
	.word	42353
	.byte	14
	.byte	'Ifx_SRC_GSCU',0,5,168,3,3
	.word	42392
	.byte	8
	.byte	'_Ifx_SRC_GSENT',0,5,171,3,25,16,10,16
	.word	40338
	.byte	11,0,0,12
	.word	42440
	.byte	4
	.byte	'SENT',0,16
	.word	42449
	.byte	2,35,0,0,12
	.word	42419
	.byte	14
	.byte	'Ifx_SRC_GSENT',0,5,174,3,3
	.word	42469
	.byte	8
	.byte	'_Ifx_SRC_GSMU',0,5,177,3,25,12,10,12
	.word	40398
	.byte	11,0,0,12
	.word	42517
	.byte	4
	.byte	'SMU',0,12
	.word	42526
	.byte	2,35,0,0,12
	.word	42497
	.byte	14
	.byte	'Ifx_SRC_GSMU',0,5,180,3,3
	.word	42545
	.byte	8
	.byte	'_Ifx_SRC_GSTM',0,5,183,3,25,96,10,96
	.word	40465
	.byte	11,0,0,12
	.word	42592
	.byte	4
	.byte	'STM',0,96
	.word	42601
	.byte	2,35,0,0,12
	.word	42572
	.byte	14
	.byte	'Ifx_SRC_GSTM',0,5,186,3,3
	.word	42620
	.byte	8
	.byte	'_Ifx_SRC_GVADC',0,5,189,3,25,224,4,10,64
	.word	40704
	.byte	11,3,0,12
	.word	42669
	.byte	4
	.byte	'G',0,64
	.word	42678
	.byte	2,35,0,10,224,1
	.word	202
	.byte	11,223,1,0,4
	.byte	'reserved_40',0,224,1
	.word	42694
	.byte	2,35,64,10,192,2
	.word	40566
	.byte	11,0,0,12
	.word	42727
	.byte	4
	.byte	'CG',0,192,2
	.word	42737
	.byte	3,35,160,2,0,12
	.word	42647
	.byte	14
	.byte	'Ifx_SRC_GVADC',0,5,194,3,3
	.word	42757
	.byte	8
	.byte	'_Ifx_SRC_GXBAR',0,5,197,3,25,4,12
	.word	40806
	.byte	4
	.byte	'XBAR',0,4
	.word	42806
	.byte	2,35,0,0,12
	.word	42785
	.byte	14
	.byte	'Ifx_SRC_GXBAR',0,5,200,3,3
	.word	42826
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L47:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,36,0,3,8,11,15,62,15,0,0,3,19,1,58,15,59,15,57,15,11,15
	.byte	0,0,4,13,0,3,8,11,15,73,19,56,9,0,0,5,38,0,73,19,0,0,6,15,0,73,19,0,0,7,23,1,58,15,59,15,57,15,11,15,0
	.byte	0,8,19,1,3,8,58,15,59,15,57,15,11,15,0,0,9,13,0,3,8,11,15,73,19,13,15,12,15,56,9,0,0,10,1,1,11,15,73,19
	.byte	0,0,11,33,0,47,15,0,0,12,53,0,73,19,0,0,13,59,0,3,8,0,0,14,22,0,3,8,58,15,59,15,57,15,73,19,0,0,15,21
	.byte	0,54,15,0,0,16,4,1,58,15,59,15,57,15,11,15,0,0,17,40,0,3,8,28,13,0,0,18,21,1,54,15,39,12,0,0,19,5,0,73
	.byte	19,0,0,20,52,0,3,8,58,15,59,15,57,15,73,19,63,12,60,12,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L48:
	.word	.L247-.L246
.L246:
	.half	3
	.word	.L249-.L248
.L248:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxGtm_regdef.h',0,0,0,0
	.byte	'..\\mcal_src\\Gtm_Platform.c',0,0,0,0
	.byte	'..\\mcal_src\\Platform_Types.h',0,0,0,0
	.byte	'..\\mcal_src\\IfxSrc_regdef.h',0,0,0,0,0
.L249:
.L247:
	.sdecl	'.debug_info',debug,cluster('Gtm_lAdcConnectionsConfig')
	.sect	'.debug_info'
.L49:
	.word	307
	.half	3
	.word	.L50
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L52,.L51
	.byte	2
	.word	.L45
	.byte	3
	.byte	'Gtm_lAdcConnectionsConfig',0,1,241,1,7,1,1,1
	.word	.L30,.L99,.L29
	.byte	4
	.word	.L30,.L99
	.byte	5
	.byte	'RegTemp1',0,1,243,1,10
	.word	.L100,.L101
	.byte	5
	.byte	'RegTemp2',0,1,244,1,10
	.word	.L100,.L102
	.byte	5
	.byte	'Count',0,1,245,1,9
	.word	.L103,.L104
	.byte	0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_lAdcConnectionsConfig')
	.sect	'.debug_abbrev'
.L50:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Gtm_lAdcConnectionsConfig')
	.sect	'.debug_line'
.L51:
	.word	.L251-.L250
.L250:
	.half	3
	.word	.L253-.L252
.L252:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm_Platform.c',0,0,0,0,0
.L253:
	.byte	5,6,7,0,5,2
	.word	.L30
	.byte	3,247,1,1,5,40,9
	.half	.L254-.L30
	.byte	1,5,3,9
	.half	.L255-.L254
	.byte	1,5,14,7,9
	.half	.L256-.L255
	.byte	3,2,1,9
	.half	.L200-.L256
	.byte	3,1,1,9
	.half	.L201-.L200
	.byte	3,6,1,5,60,1,5,73,9
	.half	.L9-.L201
	.byte	3,5,1,5,68,9
	.half	.L257-.L9
	.byte	3,2,1,5,36,9
	.half	.L258-.L257
	.byte	3,127,1,5,13,9
	.half	.L259-.L258
	.byte	3,6,1,5,49,9
	.half	.L260-.L259
	.byte	1,5,72,9
	.half	.L261-.L260
	.byte	3,122,1,5,13,9
	.half	.L262-.L261
	.byte	3,7,1,5,16,9
	.half	.L263-.L262
	.byte	3,119,1,9
	.half	.L264-.L263
	.byte	3,6,1,5,67,9
	.half	.L265-.L264
	.byte	3,118,1,5,60,1,5,5,7,9
	.half	.L266-.L265
	.byte	3,16,1,9
	.half	.L267-.L266
	.byte	3,1,1,5,1,9
	.half	.L8-.L267
	.byte	3,2,1,7,9
	.half	.L53-.L8
	.byte	0,1,1
.L251:
	.sdecl	'.debug_ranges',debug,cluster('Gtm_lAdcConnectionsConfig')
	.sect	'.debug_ranges'
.L52:
	.word	-1,.L30,0,.L53-.L30,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_lTomComplexConfig')
	.sect	'.debug_info'
.L54:
	.word	464
	.half	3
	.word	.L55
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L57,.L56
	.byte	2
	.word	.L45
	.byte	3
	.byte	'Gtm_lTomComplexConfig',0,1,239,2,6,1,1,1
	.word	.L34,.L105,.L33
	.byte	4
	.word	.L106
	.byte	5
	.byte	'TomChannelConfigPtr',0,1,241,2,28
	.word	.L107,.L108
	.byte	5
	.byte	'TomChannelRegPtr',0,1,242,2,24
	.word	.L109,.L110
	.byte	5
	.byte	'TomCnt',0,1,243,2,9
	.word	.L103,.L111
	.byte	5
	.byte	'MajorCnt',0,1,244,2,9
	.word	.L103,.L112
	.byte	5
	.byte	'MinorCnt',0,1,245,2,9
	.word	.L103,.L113
	.byte	5
	.byte	'ModuleNo',0,1,246,2,9
	.word	.L103,.L114
	.byte	5
	.byte	'ChannelNo',0,1,247,2,9
	.word	.L103,.L115
	.byte	5
	.byte	'MajorCountLoopLimit',0,1,248,2,19
	.word	.L116,.L117
	.byte	4
	.word	.L118
	.byte	5
	.byte	'val',0,1,192,3,11
	.word	.L100,.L124
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_lTomComplexConfig')
	.sect	'.debug_abbrev'
.L55:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,85,6,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Gtm_lTomComplexConfig')
	.sect	'.debug_line'
.L56:
	.word	.L269-.L268
.L268:
	.half	3
	.word	.L271-.L270
.L270:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm_Platform.c',0,0,0,0,0
.L271:
	.byte	5,69,7,0,5,2
	.word	.L34
	.byte	3,248,2,1,5,6,3,118,1,5,39,9
	.half	.L209-.L34
	.byte	3,9,1,5,10,9
	.half	.L272-.L209
	.byte	3,3,1,5,41,9
	.half	.L211-.L272
	.byte	3,4,1,5,16,9
	.half	.L273-.L211
	.byte	1,5,60,9
	.half	.L212-.L273
	.byte	1,5,18,7,9
	.half	.L14-.L212
	.byte	3,2,1,5,11,9
	.half	.L213-.L14
	.byte	3,1,1,5,52,9
	.half	.L274-.L213
	.byte	1,5,73,9
	.half	.L275-.L274
	.byte	1,5,11,7,9
	.half	.L16-.L275
	.byte	3,4,1,5,7,9
	.half	.L210-.L16
	.byte	1,5,71,7,9
	.half	.L276-.L210
	.byte	3,11,1,5,9,9
	.half	.L218-.L276
	.byte	3,4,1,5,52,7,9
	.half	.L277-.L218
	.byte	3,3,1,5,76,3,13,1,5,52,9
	.half	.L278-.L277
	.byte	3,115,1,5,76,3,13,1,5,52,9
	.half	.L219-.L278
	.byte	3,1,1,5,11,3,3,1,5,51,9
	.half	.L279-.L219
	.byte	3,125,1,5,23,9
	.half	.L280-.L279
	.byte	3,116,1,5,67,9
	.half	.L220-.L280
	.byte	3,12,1,5,66,9
	.half	.L281-.L220
	.byte	1,5,11,9
	.half	.L221-.L281
	.byte	3,3,1,9
	.half	.L119-.L221
	.byte	3,23,1,9
	.half	.L120-.L119
	.byte	3,107,1,9
	.half	.L121-.L120
	.byte	3,21,1,9
	.half	.L122-.L121
	.byte	3,107,1,9
	.half	.L282-.L122
	.byte	3,2,1,9
	.half	.L283-.L282
	.byte	3,2,1,9
	.half	.L284-.L283
	.byte	3,2,1,9
	.half	.L285-.L284
	.byte	3,3,1,9
	.half	.L286-.L285
	.byte	3,6,1,9
	.half	.L287-.L286
	.byte	3,124,1,9
	.half	.L288-.L287
	.byte	3,4,1,9
	.half	.L289-.L288
	.byte	3,2,1,9
	.half	.L123-.L289
	.byte	3,4,1,5,16,9
	.half	.L17-.L123
	.byte	3,67,1,5,73,9
	.half	.L290-.L17
	.byte	3,127,1,5,70,7,9
	.half	.L15-.L290
	.byte	3,125,1,5,41,9
	.half	.L226-.L15
	.byte	1,5,60,9
	.half	.L227-.L226
	.byte	1,5,1,7,9
	.half	.L13-.L227
	.byte	3,202,0,1,7,9
	.half	.L58-.L13
	.byte	0,1,1
.L269:
	.sdecl	'.debug_ranges',debug,cluster('Gtm_lTomComplexConfig')
	.sect	'.debug_ranges'
.L57:
	.word	-1,.L34,0,.L58-.L34,0,0
.L106:
	.word	-1,.L34,0,.L105-.L34,-1,.L36,0,.L93-.L36,-1,.L38,0,.L88-.L38,-1,.L42,0,.L78-.L42,0,0
.L118:
	.word	-1,.L34,.L119-.L34,.L120-.L34,.L121-.L34,.L122-.L34,.L123-.L34,.L17-.L34,-1,.L40,0,.L83-.L40,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_lTomClockSetting')
	.sect	'.debug_info'
.L59:
	.word	590
	.half	3
	.word	.L60
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L62,.L61
	.byte	2
	.word	.L45
	.byte	3
	.byte	'Gtm_lTomClockSetting',0,1,219,3,6,1,1,1
	.word	.L44,.L125,.L43
	.byte	4
	.word	.L44,.L125
	.byte	5
	.byte	'TomChannelConfigPtr',0,1,221,3,28
	.word	.L107,.L126
	.byte	5
	.byte	'TomChannelRegPtr',0,1,222,3,24
	.word	.L109,.L127
	.byte	5
	.byte	'TomTgcRegPtr',0,1,223,3,25
	.word	.L128,.L129
	.byte	5
	.byte	'TomCnt',0,1,224,3,9
	.word	.L103,.L130
	.byte	5
	.byte	'MajorCnt',0,1,225,3,9
	.word	.L103,.L131
	.byte	5
	.byte	'MinorCnt',0,1,226,3,9
	.word	.L103,.L132
	.byte	5
	.byte	'ModuleNo',0,1,227,3,9
	.word	.L103,.L133
	.byte	5
	.byte	'ChannelNo',0,1,228,3,9
	.word	.L103,.L134
	.byte	5
	.byte	'TgcNumber',0,1,229,3,9
	.word	.L103,.L135
	.byte	5
	.byte	'RegVal',0,1,230,3,10
	.word	.L100,.L136
	.byte	5
	.byte	'MajorCountLoopLimit',0,1,231,3,19
	.word	.L137,.L138
	.byte	4
	.word	.L139,.L140
	.byte	5
	.byte	'val',0,1,159,4,11
	.word	.L100,.L141
	.byte	0,6
	.word	.L142
	.byte	5
	.byte	'val',0,1,180,4,11
	.word	.L100,.L148
	.byte	0,4
	.word	.L146,.L147
	.byte	5
	.byte	'val',0,1,177,4,11
	.word	.L100,.L149
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_lTomClockSetting')
	.sect	'.debug_abbrev'
.L60:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,11,1
	.byte	85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Gtm_lTomClockSetting')
	.sect	'.debug_line'
.L61:
	.word	.L292-.L291
.L291:
	.half	3
	.word	.L294-.L293
.L293:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm_Platform.c',0,0,0,0,0
.L294:
	.byte	5,68,7,0,5,2
	.word	.L44
	.byte	3,231,3,1,5,6,3,115,1,5,39,9
	.half	.L228-.L44
	.byte	3,12,1,5,10,9
	.half	.L295-.L228
	.byte	3,6,1,5,41,9
	.half	.L232-.L295
	.byte	3,3,1,5,16,9
	.half	.L296-.L232
	.byte	1,5,60,9
	.half	.L229-.L296
	.byte	1,5,18,7,9
	.half	.L20-.L229
	.byte	3,2,1,5,11,9
	.half	.L230-.L20
	.byte	3,1,1,5,52,9
	.half	.L297-.L230
	.byte	1,5,73,9
	.half	.L298-.L297
	.byte	1,5,11,7,9
	.half	.L22-.L298
	.byte	3,4,1,5,7,9
	.half	.L299-.L22
	.byte	1,5,71,7,9
	.half	.L300-.L299
	.byte	3,11,1,5,8,9
	.half	.L231-.L300
	.byte	3,4,1,5,52,7,9
	.half	.L301-.L231
	.byte	3,4,1,5,76,3,16,1,5,52,9
	.half	.L302-.L301
	.byte	3,112,1,5,76,3,16,1,5,53,9
	.half	.L233-.L302
	.byte	3,1,1,5,52,9
	.half	.L303-.L233
	.byte	1,5,19,9
	.half	.L304-.L303
	.byte	3,115,1,5,23,9
	.half	.L305-.L304
	.byte	3,126,1,5,19,9
	.half	.L234-.L305
	.byte	3,2,1,5,68,9
	.half	.L235-.L234
	.byte	3,13,1,5,67,9
	.half	.L306-.L235
	.byte	1,5,11,9
	.half	.L139-.L306
	.byte	3,4,1,5,29,9
	.half	.L140-.L139
	.byte	3,16,1,5,11,9
	.half	.L238-.L140
	.byte	3,120,1,5,50,9
	.half	.L307-.L238
	.byte	3,6,1,5,55,9
	.half	.L236-.L307
	.byte	1,5,50,1,5,11,9
	.half	.L143-.L236
	.byte	3,7,1,5,50,9
	.half	.L144-.L143
	.byte	3,121,1,5,27,9
	.half	.L239-.L144
	.byte	3,3,1,5,44,9
	.half	.L308-.L239
	.byte	3,127,1,5,11,3,2,1,9
	.half	.L145-.L308
	.byte	3,3,1,9
	.half	.L146-.L145
	.byte	3,125,1,9
	.half	.L147-.L146
	.byte	3,3,1,5,16,9
	.half	.L23-.L147
	.byte	3,64,1,5,73,9
	.half	.L243-.L23
	.byte	3,127,1,5,70,7,9
	.half	.L21-.L243
	.byte	3,125,1,5,41,9
	.half	.L244-.L21
	.byte	1,5,60,9
	.half	.L245-.L244
	.byte	1,5,1,7,9
	.half	.L19-.L245
	.byte	3,204,0,1,7,9
	.half	.L63-.L19
	.byte	0,1,1
.L292:
	.sdecl	'.debug_ranges',debug,cluster('Gtm_lTomClockSetting')
	.sect	'.debug_ranges'
.L62:
	.word	-1,.L44,0,.L63-.L44,0,0
.L142:
	.word	-1,.L44,.L143-.L44,.L144-.L44,.L145-.L44,.L146-.L44,.L147-.L44,.L23-.L44,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_lTimConfigure')
	.sect	'.debug_info'
.L64:
	.word	429
	.half	3
	.word	.L65
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L67,.L66
	.byte	2
	.word	.L45
	.byte	3
	.byte	'Gtm_lTimConfigure',0,1,104,6,1,1,1
	.word	.L26,.L150,.L25
	.byte	4
	.word	.L151
	.byte	5
	.byte	'TimConfigPtr',0,1,106,28
	.word	.L152,.L153
	.byte	5
	.byte	'TimChannelRegPtr',0,1,107,24
	.word	.L154,.L155
	.byte	5
	.byte	'TimCnt',0,1,111,9
	.word	.L103,.L156
	.byte	5
	.byte	'MinorCnt',0,1,112,9
	.word	.L103,.L157
	.byte	4
	.word	.L158
	.byte	5
	.byte	'val',0,1,205,1,9
	.word	.L100,.L166
	.byte	0,6
	.word	.L6,.L167
	.byte	5
	.byte	'val',0,1,194,1,9
	.word	.L100,.L168
	.byte	0,6
	.word	.L167,.L165
	.byte	5
	.byte	'val',0,1,199,1,9
	.word	.L100,.L169
	.byte	0,6
	.word	.L170,.L3
	.byte	5
	.byte	'val',0,1,217,1,9
	.word	.L100,.L171
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_lTimConfigure')
	.sect	'.debug_abbrev'
.L65:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,85,6,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,11,1,17,1
	.byte	18,1,0,0,0
	.sdecl	'.debug_line',debug,cluster('Gtm_lTimConfigure')
	.sect	'.debug_line'
.L66:
	.word	.L310-.L309
.L309:
	.half	3
	.word	.L312-.L311
.L311:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm_Platform.c',0,0,0,0,0
.L312:
	.byte	5,10,7,0,5,2
	.word	.L26
	.byte	3,244,0,1,5,16,9
	.half	.L193-.L26
	.byte	3,3,1,5,55,1,5,9,9
	.half	.L2-.L193
	.byte	3,8,1,9
	.half	.L194-.L2
	.byte	3,1,1,5,56,3,127,1,5,21,9
	.half	.L313-.L194
	.byte	3,1,1,5,61,9
	.half	.L314-.L313
	.byte	3,127,1,5,5,9
	.half	.L315-.L314
	.byte	1,5,69,7,9
	.half	.L316-.L315
	.byte	3,6,1,5,57,3,127,1,5,13,9
	.half	.L317-.L316
	.byte	3,3,1,5,9,3,197,0,1,5,69,9
	.half	.L160-.L317
	.byte	3,185,127,1,5,60,9
	.half	.L195-.L160
	.byte	3,8,1,5,56,1,5,9,9
	.half	.L161-.L195
	.byte	3,63,1,5,56,9
	.half	.L162-.L161
	.byte	3,65,1,5,59,9
	.half	.L318-.L162
	.byte	1,5,22,9
	.half	.L196-.L318
	.byte	3,7,1,5,13,9
	.half	.L319-.L196
	.byte	3,115,1,5,9,9
	.half	.L163-.L319
	.byte	3,197,0,1,5,78,9
	.half	.L164-.L163
	.byte	3,185,127,1,5,7,9
	.half	.L320-.L164
	.byte	3,15,1,5,9,7,9
	.half	.L321-.L320
	.byte	3,3,1,5,24,9
	.half	.L322-.L321
	.byte	3,5,1,5,9,9
	.half	.L323-.L322
	.byte	1,5,11,7,9
	.half	.L324-.L323
	.byte	3,2,1,9
	.half	.L325-.L324
	.byte	3,2,1,5,9,9
	.half	.L5-.L325
	.byte	3,3,1,9
	.half	.L326-.L5
	.byte	3,16,1,9
	.half	.L327-.L326
	.byte	3,114,1,9
	.half	.L328-.L327
	.byte	3,5,1,9
	.half	.L329-.L328
	.byte	3,2,1,9
	.half	.L330-.L329
	.byte	3,3,1,9
	.half	.L331-.L330
	.byte	3,4,1,5,30,9
	.half	.L332-.L331
	.byte	3,4,1,5,45,9
	.half	.L333-.L332
	.byte	1,5,9,9
	.half	.L334-.L333
	.byte	3,3,1,5,11,7,9
	.half	.L335-.L334
	.byte	3,2,1,5,9,9
	.half	.L6-.L335
	.byte	3,5,1,9
	.half	.L167-.L6
	.byte	3,5,1,9
	.half	.L165-.L167
	.byte	3,6,1,9
	.half	.L336-.L165
	.byte	3,117,1,9
	.half	.L4-.L336
	.byte	3,19,1,9
	.half	.L170-.L4
	.byte	3,4,1,5,65,9
	.half	.L3-.L170
	.byte	3,159,127,1,5,55,1,5,1,7,9
	.half	.L337-.L3
	.byte	3,231,0,1,7,9
	.half	.L68-.L337
	.byte	0,1,1
.L310:
	.sdecl	'.debug_ranges',debug,cluster('Gtm_lTimConfigure')
	.sect	'.debug_ranges'
.L67:
	.word	-1,.L26,0,.L68-.L26,0,0
.L151:
	.word	-1,.L26,0,.L150-.L26,-1,.L28,0,.L98-.L28,0,0
.L158:
	.word	-1,.L26,.L159-.L26,.L160-.L26,.L161-.L26,.L162-.L26,.L163-.L26,.L164-.L26,.L165-.L26,.L4-.L26,0,0
	.sdecl	'.debug_info',debug,cluster('Gtm_lResetGtmSRCReg')
	.sect	'.debug_info'
.L69:
	.word	437
	.half	3
	.word	.L70
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L72,.L71
	.byte	2
	.word	.L45
	.byte	3
	.byte	'Gtm_lResetGtmSRCReg',0,1,173,2,6,1,1,1
	.word	.L32,.L172,.L31
	.byte	4
	.word	.L32,.L172
	.byte	5
	.byte	'ModuleNo',0,1,175,2,9
	.word	.L103,.L173
	.byte	5
	.byte	'ChannelNo',0,1,176,2,9
	.word	.L103,.L174
	.byte	4
	.word	.L32,.L175
	.byte	5
	.byte	'val',0,1,179,2,3
	.word	.L100,.L176
	.byte	0,4
	.word	.L175,.L177
	.byte	5
	.byte	'val',0,1,183,2,3
	.word	.L100,.L178
	.byte	0,6
	.word	.L179
	.byte	5
	.byte	'val',0,1,191,2,5
	.word	.L100,.L183
	.byte	0,4
	.word	.L182,.L184
	.byte	5
	.byte	'val',0,1,195,2,5
	.word	.L100,.L185
	.byte	0,6
	.word	.L186
	.byte	5
	.byte	'val',0,1,207,2,6
	.word	.L100,.L190
	.byte	0,4
	.word	.L189,.L191
	.byte	5
	.byte	'val',0,1,212,2,6
	.word	.L100,.L192
	.byte	0,0,0,0
	.sdecl	'.debug_abbrev',debug,cluster('Gtm_lResetGtmSRCReg')
	.sect	'.debug_abbrev'
.L70:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,1,3,8,58,15,59,15,57,15,54,15
	.byte	39,12,63,12,17,1,18,1,64,6,0,0,4,11,1,17,1,18,1,0,0,5,52,0,3,8,58,15,59,15,57,15,73,16,2,6,0,0,6,11,1
	.byte	85,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('Gtm_lResetGtmSRCReg')
	.sect	'.debug_line'
.L71:
	.word	.L339-.L338
.L338:
	.half	3
	.word	.L341-.L340
.L340:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm_Platform.c',0,0,0,0,0
.L341:
	.byte	5,3,7,0,5,2
	.word	.L32
	.byte	3,178,2,1,9
	.half	.L175-.L32
	.byte	3,4,1,5,16,9
	.half	.L177-.L175
	.byte	3,4,1,5,68,1,5,5,9
	.half	.L10-.L177
	.byte	3,4,1,5,74,9
	.half	.L180-.L10
	.byte	3,125,1,5,5,9
	.half	.L181-.L180
	.byte	3,3,1,9
	.half	.L182-.L181
	.byte	3,4,1,5,68,9
	.half	.L206-.L182
	.byte	3,120,1,5,15,7,9
	.half	.L184-.L206
	.byte	3,14,1,5,60,1,5,18,9
	.half	.L11-.L184
	.byte	3,2,1,5,64,1,5,6,9
	.half	.L12-.L11
	.byte	3,4,1,5,75,9
	.half	.L187-.L12
	.byte	3,125,1,5,6,9
	.half	.L188-.L187
	.byte	3,3,1,9
	.half	.L189-.L188
	.byte	3,5,1,5,64,9
	.half	.L208-.L189
	.byte	3,119,1,5,70,7,9
	.half	.L191-.L208
	.byte	3,126,1,5,60,1,5,1,7,9
	.half	.L342-.L191
	.byte	3,17,1,7,9
	.half	.L73-.L342
	.byte	0,1,1
.L339:
	.sdecl	'.debug_ranges',debug,cluster('Gtm_lResetGtmSRCReg')
	.sect	'.debug_ranges'
.L72:
	.word	-1,.L32,0,.L73-.L32,0,0
.L179:
	.word	-1,.L32,.L10-.L32,.L180-.L32,.L181-.L32,.L182-.L32,0,0
.L186:
	.word	-1,.L32,.L12-.L32,.L187-.L32,.L188-.L32,.L189-.L32,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_1')
	.sect	'.debug_info'
.L74:
	.word	216
	.half	3
	.word	.L75
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L77,.L76
	.byte	2
	.word	.L45
	.byte	3
	.byte	'.cocofun_1',0,1,239,2,6,1
	.word	.L42,.L78,.L41
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_1')
	.sect	'.debug_abbrev'
.L75:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_1')
	.sect	'.debug_line'
.L76:
	.word	.L344-.L343
.L343:
	.half	3
	.word	.L346-.L345
.L345:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm_Platform.c',0,0,0,0,0
.L346:
	.byte	5,11,7,0,5,2
	.word	.L42
	.byte	3,133,3,1,5,58,9
	.half	.L214-.L42
	.byte	1,5,10,9
	.half	.L347-.L214
	.byte	3,1,1,5,58,9
	.half	.L348-.L347
	.byte	3,127,1,5,22,9
	.half	.L349-.L348
	.byte	3,1,1,5,69,9
	.half	.L350-.L349
	.byte	3,127,1,9
	.half	.L78-.L350
	.byte	0,1,1,5,11,0,5,2
	.word	.L42
	.byte	3,133,3,1,5,58,9
	.half	.L214-.L42
	.byte	3,241,0,1,5,41,9
	.half	.L347-.L214
	.byte	3,1,1,5,58,9
	.half	.L348-.L347
	.byte	3,127,1,5,53,9
	.half	.L349-.L348
	.byte	3,1,1,5,69,9
	.half	.L350-.L349
	.byte	3,127,1,3,143,127,1,7,9
	.half	.L78-.L350
	.byte	0,1,1
.L344:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_1')
	.sect	'.debug_ranges'
.L77:
	.word	-1,.L42,0,.L78-.L42,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_2')
	.sect	'.debug_info'
.L79:
	.word	216
	.half	3
	.word	.L80
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L82,.L81
	.byte	2
	.word	.L45
	.byte	3
	.byte	'.cocofun_2',0,1,239,2,6,1
	.word	.L40,.L83,.L39
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_2')
	.sect	'.debug_abbrev'
.L80:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_2')
	.sect	'.debug_line'
.L81:
	.word	.L352-.L351
.L351:
	.half	3
	.word	.L354-.L353
.L353:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm_Platform.c',0,0,0,0,0
.L354:
	.byte	5,11,7,0,5,2
	.word	.L40
	.byte	3,191,3,1,9
	.half	.L83-.L40
	.byte	0,1,1,5,11,0,5,2
	.word	.L40
	.byte	3,179,4,1,9
	.half	.L223-.L40
	.byte	3,140,127,1,7,9
	.half	.L83-.L223
	.byte	0,1,1
.L352:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_2')
	.sect	'.debug_ranges'
.L82:
	.word	-1,.L40,0,.L83-.L40,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_3')
	.sect	'.debug_info'
.L84:
	.word	216
	.half	3
	.word	.L85
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L87,.L86
	.byte	2
	.word	.L45
	.byte	3
	.byte	'.cocofun_3',0,1,239,2,6,1
	.word	.L38,.L88,.L37
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_3')
	.sect	'.debug_abbrev'
.L85:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_3')
	.sect	'.debug_line'
.L86:
	.word	.L356-.L355
.L355:
	.half	3
	.word	.L358-.L357
.L357:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm_Platform.c',0,0,0,0,0
.L358:
	.byte	5,71,7,0,5,2
	.word	.L38
	.byte	3,144,3,1,5,54,1,5,15,9
	.half	.L359-.L38
	.byte	3,2,1,5,71,9
	.half	.L216-.L359
	.byte	3,126,1,5,31,9
	.half	.L217-.L216
	.byte	3,4,1,9
	.half	.L88-.L217
	.byte	0,1,1,5,71,0,5,2
	.word	.L38
	.byte	3,129,4,1,5,54,1,5,15,9
	.half	.L359-.L38
	.byte	3,2,1,5,71,9
	.half	.L216-.L359
	.byte	3,126,1,5,30,9
	.half	.L217-.L216
	.byte	3,4,1,5,31,9
	.half	.L360-.L217
	.byte	3,143,127,1,7,9
	.half	.L88-.L360
	.byte	0,1,1
.L356:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_3')
	.sect	'.debug_ranges'
.L87:
	.word	-1,.L38,0,.L88-.L38,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_4')
	.sect	'.debug_info'
.L89:
	.word	216
	.half	3
	.word	.L90
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L92,.L91
	.byte	2
	.word	.L45
	.byte	3
	.byte	'.cocofun_4',0,1,239,2,6,1
	.word	.L36,.L93,.L35
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_4')
	.sect	'.debug_abbrev'
.L90:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_4')
	.sect	'.debug_line'
.L91:
	.word	.L362-.L361
.L361:
	.half	3
	.word	.L364-.L363
.L363:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm_Platform.c',0,0,0,0,0
.L364:
	.byte	5,16,7,0,5,2
	.word	.L36
	.byte	3,130,3,1,5,52,9
	.half	.L225-.L36
	.byte	3,127,1,5,38,9
	.half	.L365-.L225
	.byte	1,5,52,1,7,9
	.half	.L93-.L365
	.byte	0,1,1,5,16,0,5,2
	.word	.L36
	.byte	3,243,3,1,5,52,9
	.half	.L225-.L36
	.byte	3,127,1,5,38,9
	.half	.L365-.L225
	.byte	1,5,52,3,143,127,1,7,9
	.half	.L93-.L365
	.byte	0,1,1
.L362:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_4')
	.sect	'.debug_ranges'
.L92:
	.word	-1,.L36,0,.L93-.L36,0,0
	.sdecl	'.debug_info',debug,cluster('.cocofun_5')
	.sect	'.debug_info'
.L94:
	.word	215
	.half	3
	.word	.L95
	.byte	4,1
	.byte	'..\\mcal_src\\Gtm_Platform.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L97,.L96
	.byte	2
	.word	.L45
	.byte	3
	.byte	'.cocofun_5',0,1,104,6,1
	.word	.L28,.L98,.L27
	.byte	0
	.sdecl	'.debug_abbrev',debug,cluster('.cocofun_5')
	.sect	'.debug_abbrev'
.L95:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,85,6,16,6,0,0,2,61,0,24,16,0,0,3,46,0,3,8,58,15,59,15,57,15,54,15
	.byte	17,1,18,1,64,6,0,0,0
	.sdecl	'.debug_line',debug,cluster('.cocofun_5')
	.sect	'.debug_line'
.L96:
	.word	.L367-.L366
.L366:
	.half	3
	.word	.L369-.L368
.L368:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\mcal_src\\Gtm_Platform.c',0,0,0,0,0
.L369:
	.byte	5,9,7,0,5,2
	.word	.L28
	.byte	3,255,0,1,5,23,9
	.half	.L370-.L28
	.byte	1,9
	.half	.L98-.L370
	.byte	0,1,1,5,6,0,5,2
	.word	.L28
	.byte	3,247,1,1,5,20,9
	.half	.L370-.L28
	.byte	1,5,23,9
	.half	.L371-.L370
	.byte	3,136,127,1,7,9
	.half	.L98-.L371
	.byte	0,1,1,5,11,0,5,2
	.word	.L28
	.byte	3,133,3,1,5,25,9
	.half	.L370-.L28
	.byte	1,5,23,9
	.half	.L371-.L370
	.byte	3,250,125,1,7,9
	.half	.L98-.L371
	.byte	0,1,1,5,11,0,5,2
	.word	.L28
	.byte	3,246,3,1,5,25,9
	.half	.L370-.L28
	.byte	1,5,23,9
	.half	.L371-.L370
	.byte	3,137,125,1,7,9
	.half	.L98-.L371
	.byte	0,1,1
.L367:
	.sdecl	'.debug_ranges',debug,cluster('.cocofun_5')
	.sect	'.debug_ranges'
.L97:
	.word	-1,.L28,0,.L98-.L28,0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_1')
	.sect	'.debug_loc'
.L41:
	.word	-1,.L42,0,.L78-.L42
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_2')
	.sect	'.debug_loc'
.L39:
	.word	-1,.L40,0,.L83-.L40
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_3')
	.sect	'.debug_loc'
.L37:
	.word	-1,.L38,0,.L88-.L38
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_4')
	.sect	'.debug_loc'
.L35:
	.word	-1,.L36,0,.L93-.L36
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('.cocofun_5')
	.sect	'.debug_loc'
.L27:
	.word	-1,.L28,0,.L98-.L28
	.half	2
	.byte	138,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Gtm_lAdcConnectionsConfig')
	.sect	'.debug_loc'
.L104:
	.word	-1,.L30,.L9-.L30,.L8-.L30
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L29:
	.word	-1,.L30,0,.L99-.L30
	.half	2
	.byte	138,0
	.word	0,0
.L101:
	.word	-1,.L30,.L200-.L30,.L8-.L30
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L102:
	.word	-1,.L30,.L201-.L30,.L8-.L30
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Gtm_lResetGtmSRCReg')
	.sect	'.debug_loc'
.L174:
	.word	-1,.L32,.L10-.L32,.L11-.L32
	.half	5
	.byte	144,39,157,32,32
	.word	.L12-.L32,.L172-.L32
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L31:
	.word	-1,.L32,0,.L172-.L32
	.half	2
	.byte	138,0
	.word	0,0
.L173:
	.word	-1,.L32,.L11-.L32,.L172-.L32
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L183:
	.word	-1,.L32,.L205-.L32,.L206-.L32
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L192:
	.word	-1,.L32,.L208-.L32,.L172-.L32
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L190:
	.word	-1,.L32,.L207-.L32,.L208-.L32
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L176:
	.word	-1,.L32,.L202-.L32,.L203-.L32
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L185:
	.word	-1,.L32,.L206-.L32,.L11-.L32
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L178:
	.word	-1,.L32,.L204-.L32,.L10-.L32
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Gtm_lTimConfigure')
	.sect	'.debug_loc'
.L25:
	.word	-1,.L26,0,.L150-.L26
	.half	2
	.byte	138,0
	.word	0,0
.L157:
	.word	-1,.L26,.L28-.L26,.L98-.L26
	.half	5
	.byte	144,33,157,32,32
	.word	.L194-.L26,.L150-.L26
	.half	5
	.byte	144,33,157,32,32
	.word	0,0
.L155:
	.word	-1,.L26,.L196-.L26,.L3-.L26
	.half	1
	.byte	111
	.word	0,0
.L156:
	.word	-1,.L26,.L193-.L26,.L2-.L26
	.half	5
	.byte	144,33,157,32,0
	.word	.L28-.L26,.L98-.L26
	.half	5
	.byte	144,33,157,32,0
	.word	.L194-.L26,.L159-.L26
	.half	5
	.byte	144,33,157,32,0
	.word	.L163-.L26,.L150-.L26
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L153:
	.word	-1,.L26,.L195-.L26,.L3-.L26
	.half	1
	.byte	98
	.word	0,0
.L168:
	.word	-1,.L26,.L197-.L26,.L198-.L26
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L169:
	.word	-1,.L26,.L199-.L26,.L4-.L26
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L171:
	.word	-1,.L26,.L7-.L26,.L3-.L26
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L166:
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Gtm_lTomClockSetting')
	.sect	'.debug_loc'
.L134:
	.word	-1,.L44,.L234-.L44,.L146-.L44
	.half	5
	.byte	144,36,157,32,32
	.word	0,0
.L43:
	.word	-1,.L44,0,.L228-.L44
	.half	2
	.byte	138,0
	.word	.L228-.L44,.L125-.L44
	.half	2
	.byte	138,8
	.word	.L125-.L44,.L125-.L44
	.half	2
	.byte	138,0
	.word	0,0
.L131:
	.word	-1,.L44,.L28-.L44,.L98-.L44
	.half	5
	.byte	144,33,157,32,32
	.word	.L214-.L44,.L78-.L44
	.half	5
	.byte	144,33,157,32,32
	.word	.L38-.L44,.L88-.L44
	.half	5
	.byte	144,33,157,32,32
	.word	.L229-.L44,.L23-.L44
	.half	5
	.byte	144,33,157,32,32
	.word	.L40-.L44,.L83-.L44
	.half	5
	.byte	144,33,157,32,32
	.word	.L36-.L44,.L93-.L44
	.half	5
	.byte	144,33,157,32,32
	.word	.L243-.L44,.L244-.L44
	.half	5
	.byte	144,33,157,32,32
	.word	.L245-.L44,.L125-.L44
	.half	5
	.byte	144,33,157,32,32
	.word	0,0
.L138:
	.word	-1,.L44,.L28-.L44,.L98-.L44
	.half	2
	.byte	145,120
	.word	.L214-.L44,.L78-.L44
	.half	2
	.byte	145,120
	.word	.L38-.L44,.L88-.L44
	.half	2
	.byte	145,120
	.word	0,.L23-.L44
	.half	2
	.byte	145,120
	.word	.L40-.L44,.L83-.L44
	.half	2
	.byte	145,120
	.word	.L36-.L44,.L93-.L44
	.half	2
	.byte	145,120
	.word	.L243-.L44,.L125-.L44
	.half	2
	.byte	145,120
	.word	0,0
.L132:
	.word	-1,.L44,.L28-.L44,.L98-.L44
	.half	5
	.byte	144,33,157,32,0
	.word	.L214-.L44,.L78-.L44
	.half	5
	.byte	144,33,157,32,0
	.word	.L38-.L44,.L88-.L44
	.half	5
	.byte	144,33,157,32,0
	.word	.L230-.L44,.L23-.L44
	.half	5
	.byte	144,33,157,32,0
	.word	.L40-.L44,.L83-.L44
	.half	5
	.byte	144,33,157,32,0
	.word	.L36-.L44,.L224-.L44
	.half	5
	.byte	144,33,157,32,0
	.word	.L225-.L44,.L93-.L44
	.half	5
	.byte	144,33,157,32,0
	.word	.L243-.L44,.L19-.L44
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L133:
	.word	-1,.L44,.L233-.L44,.L144-.L44
	.half	5
	.byte	144,36,157,32,0
	.word	0,0
.L136:
	.word	-1,.L44,.L145-.L44,.L23-.L44
	.half	5
	.byte	144,32,157,32,0
	.word	.L40-.L44,.L40-.L44
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L135:
	.word	-1,.L44,.L235-.L44,.L23-.L44
	.half	5
	.byte	144,35,157,32,32
	.word	.L40-.L44,.L83-.L44
	.half	5
	.byte	144,35,157,32,32
	.word	0,0
.L126:
	.word	-1,.L44,.L217-.L44,.L88-.L44
	.half	1
	.byte	98
	.word	.L231-.L44,.L23-.L44
	.half	1
	.byte	98
	.word	.L40-.L44,.L83-.L44
	.half	1
	.byte	98
	.word	0,0
.L127:
	.word	-1,.L44,.L139-.L44,.L236-.L44
	.half	1
	.byte	111
	.word	0,0
.L130:
	.word	-1,.L44,.L28-.L44,.L98-.L44
	.half	5
	.byte	144,32,157,32,32
	.word	.L214-.L44,.L78-.L44
	.half	5
	.byte	144,32,157,32,32
	.word	.L38-.L44,.L215-.L44
	.half	5
	.byte	144,32,157,32,32
	.word	.L216-.L44,.L88-.L44
	.half	5
	.byte	144,32,157,32,32
	.word	.L232-.L44,.L23-.L44
	.half	5
	.byte	144,32,157,32,32
	.word	.L40-.L44,.L83-.L44
	.half	5
	.byte	144,32,157,32,32
	.word	.L36-.L44,.L93-.L44
	.half	5
	.byte	144,32,157,32,32
	.word	.L243-.L44,.L125-.L44
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L129:
	.word	-1,.L44,.L239-.L44,.L240-.L44
	.half	1
	.byte	111
	.word	0,0
.L148:
	.word	-1,.L44,.L242-.L44,.L83-.L44
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L149:
	.word	-1,.L44,.L146-.L44,.L23-.L44
	.half	5
	.byte	144,39,157,32,32
	.word	.L40-.L44,.L241-.L44
	.half	5
	.byte	144,39,157,32,32
	.word	0,0
.L141:
	.word	-1,.L44,.L237-.L44,.L238-.L44
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
	.sdecl	'.debug_loc',debug,cluster('Gtm_lTomComplexConfig')
	.sect	'.debug_loc'
.L115:
	.word	-1,.L34,.L220-.L34,.L122-.L34
	.half	5
	.byte	144,35,157,32,0
	.word	0,0
.L33:
	.word	-1,.L34,0,.L209-.L34
	.half	2
	.byte	138,0
	.word	.L209-.L34,.L105-.L34
	.half	2
	.byte	138,8
	.word	.L105-.L34,.L105-.L34
	.half	2
	.byte	138,0
	.word	0,0
.L112:
	.word	-1,.L34,.L212-.L34,.L210-.L34
	.half	5
	.byte	144,33,157,32,32
	.word	.L28-.L34,.L98-.L34
	.half	5
	.byte	144,33,157,32,32
	.word	.L214-.L34,.L78-.L34
	.half	5
	.byte	144,33,157,32,32
	.word	.L38-.L34,.L88-.L34
	.half	5
	.byte	144,33,157,32,32
	.word	.L218-.L34,.L17-.L34
	.half	5
	.byte	144,33,157,32,32
	.word	.L40-.L34,.L223-.L34
	.half	5
	.byte	144,33,157,32,32
	.word	.L36-.L34,.L93-.L34
	.half	5
	.byte	144,33,157,32,32
	.word	.L15-.L34,.L226-.L34
	.half	5
	.byte	144,33,157,32,32
	.word	.L227-.L34,.L105-.L34
	.half	5
	.byte	144,33,157,32,32
	.word	0,0
.L117:
	.word	-1,.L34,0,.L210-.L34
	.half	2
	.byte	145,120
	.word	.L28-.L34,.L98-.L34
	.half	2
	.byte	145,120
	.word	.L214-.L34,.L78-.L34
	.half	2
	.byte	145,120
	.word	.L38-.L34,.L88-.L34
	.half	2
	.byte	145,120
	.word	.L218-.L34,.L17-.L34
	.half	2
	.byte	145,120
	.word	.L40-.L34,.L223-.L34
	.half	2
	.byte	145,120
	.word	.L36-.L34,.L93-.L34
	.half	2
	.byte	145,120
	.word	.L15-.L34,.L105-.L34
	.half	2
	.byte	145,120
	.word	0,0
.L113:
	.word	-1,.L34,.L213-.L34,.L210-.L34
	.half	5
	.byte	144,33,157,32,0
	.word	.L28-.L34,.L98-.L34
	.half	5
	.byte	144,33,157,32,0
	.word	.L214-.L34,.L78-.L34
	.half	5
	.byte	144,33,157,32,0
	.word	.L38-.L34,.L88-.L34
	.half	5
	.byte	144,33,157,32,0
	.word	.L218-.L34,.L17-.L34
	.half	5
	.byte	144,33,157,32,0
	.word	.L40-.L34,.L223-.L34
	.half	5
	.byte	144,33,157,32,0
	.word	.L36-.L34,.L224-.L34
	.half	5
	.byte	144,33,157,32,0
	.word	.L225-.L34,.L93-.L34
	.half	5
	.byte	144,33,157,32,0
	.word	.L15-.L34,.L13-.L34
	.half	5
	.byte	144,33,157,32,0
	.word	0,0
.L114:
	.word	-1,.L34,.L219-.L34,.L120-.L34
	.half	5
	.byte	144,32,157,32,0
	.word	0,0
.L108:
	.word	-1,.L34,.L217-.L34,.L88-.L34
	.half	1
	.byte	98
	.word	.L218-.L34,.L17-.L34
	.half	1
	.byte	98
	.word	.L40-.L34,.L223-.L34
	.half	1
	.byte	98
	.word	0,0
.L110:
	.word	-1,.L34,.L221-.L34,.L222-.L34
	.half	1
	.byte	111
	.word	0,0
.L111:
	.word	-1,.L34,.L211-.L34,.L210-.L34
	.half	5
	.byte	144,32,157,32,32
	.word	.L28-.L34,.L98-.L34
	.half	5
	.byte	144,32,157,32,32
	.word	.L214-.L34,.L78-.L34
	.half	5
	.byte	144,32,157,32,32
	.word	.L38-.L34,.L215-.L34
	.half	5
	.byte	144,32,157,32,32
	.word	.L216-.L34,.L88-.L34
	.half	5
	.byte	144,32,157,32,32
	.word	.L218-.L34,.L17-.L34
	.half	5
	.byte	144,32,157,32,32
	.word	.L40-.L34,.L223-.L34
	.half	5
	.byte	144,32,157,32,32
	.word	.L36-.L34,.L93-.L34
	.half	5
	.byte	144,32,157,32,32
	.word	.L15-.L34,.L105-.L34
	.half	5
	.byte	144,32,157,32,32
	.word	0,0
.L124:
	.word	0,0
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L372:
	.word	48
	.word	-1
	.byte	3,0,2,1,27,12,26,0,8,26,8,27,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36,8,37,8,38
	.byte	8,39
	.sdecl	'.debug_frame',debug,cluster('Gtm_lTimConfigure')
	.sect	'.debug_frame'
	.word	16
	.word	.L372,.L26,.L150-.L26
	.byte	8,19,8,23
	.sdecl	'.debug_frame',debug,cluster('Gtm_lAdcConnectionsConfig')
	.sect	'.debug_frame'
	.word	24
	.word	.L372,.L30,.L99-.L30
	.byte	8,19,8,20,8,21,8,22,8,23,0,0
	.sdecl	'.debug_frame',debug,cluster('Gtm_lResetGtmSRCReg')
	.sect	'.debug_frame'
	.word	20
	.word	.L372,.L32,.L172-.L32
	.byte	8,19,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('Gtm_lTomComplexConfig')
	.sect	'.debug_frame'
	.word	44
	.word	.L372,.L34,.L105-.L34
	.byte	8,19,8,21,8,22,8,23,4
	.word	(.L209-.L34)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L105-.L209)/2
	.byte	19,0,8,26,0,0
	.sdecl	'.debug_frame',debug,cluster('Gtm_lTomClockSetting')
	.sect	'.debug_frame'
	.word	44
	.word	.L372,.L44,.L125-.L44
	.byte	8,19,8,20,8,21,8,22,8,23,4
	.word	(.L228-.L44)/2
	.byte	19,8,22,26,3,19,138,8,4
	.word	(.L125-.L228)/2
	.byte	19,0,8,26
	.sdecl	'.debug_frame',debug
	.sect	'.debug_frame'
.L373:
	.word	52
	.word	-1
	.byte	3,0,2,1,40,12,26,0,7,26,9,40,27,155,0,8,30,8,29,8,28,8,16,8,17,8,24,8,25,8,31,8,32,8,33,8,34,8,35,8,36
	.byte	8,37,8,38,8,39,0
	.sdecl	'.debug_frame',debug,cluster('.cocofun_5')
	.sect	'.debug_frame'
	.word	24
	.word	.L373,.L28,.L98-.L28
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_4')
	.sect	'.debug_frame'
	.word	24
	.word	.L373,.L36,.L93-.L36
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_3')
	.sect	'.debug_frame'
	.word	24
	.word	.L373,.L38,.L88-.L38
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_2')
	.sect	'.debug_frame'
	.word	24
	.word	.L373,.L40,.L83-.L40
	.byte	8,18,8,19,8,20,8,21,8,22,8,23
	.sdecl	'.debug_frame',debug,cluster('.cocofun_1')
	.sect	'.debug_frame'
	.word	24
	.word	.L373,.L42,.L78-.L42
	.byte	8,18,8,19,8,20,8,21,8,22,8,23

; ..\mcal_src\Gtm_Platform.c	   573  
; ..\mcal_src\Gtm_Platform.c	   574  #define GTM_STOP_SEC_CODE
; ..\mcal_src\Gtm_Platform.c	   575  /*IFX_MISRA_RULE_19_01_STATUS= File inclusion after pre-processor directives is 
; ..\mcal_src\Gtm_Platform.c	   576   allowed only for MemMap.h*/
; ..\mcal_src\Gtm_Platform.c	   577  #include "MemMap.h"

	; Module end
