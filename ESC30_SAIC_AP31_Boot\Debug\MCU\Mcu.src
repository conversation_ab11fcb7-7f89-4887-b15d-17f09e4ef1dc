	; Module start
	.compiler_version	"TASKING VX-toolset for TriCore: C compiler v4.3r3 Build 759.2.1"
	.compiler_invocation	"ctc -f cc13364a -c99 --dep-file=MCU\\.Mcu.o.d -D__CPU__=userdef16x -D__CPU_USERDEF16X__ --core=tc1.6.x -F -D_TASKING_C_TRICORE_=1 -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\MCU -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash\\flsloader -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Crc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fee -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\Fls -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\MemIf -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom\\NvM -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\eeprom -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\flash -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\uds -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\wdg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Secure -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Vss -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\vss_code -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_cfg -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\dma_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\integration_general\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\inc -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\mcal_src\\spi_infineon_tricore\\src -IE:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Src_file -I-I -I-I -I-I -g2 --make-target=MCU\\Mcu.o -t4 --language=-gcc,-volatile,+strings --default-near-size=0 -O2 --default-a1-size=0 --default-a0-size=0 --source --align=4 --compact-max-size=200 --switch=auto -o MCU\\Mcu.src ..\\MCU\\Mcu.c"
	.compiler_name		"ctc"
	.name	"Mcu"

	
$TC16X
	
	.sdecl	'.debug_info',debug
	.sect	'.debug_info'
.L3:
	.word	219
	.half	3
	.word	.L4
	.byte	4
.L2:
	.byte	1
	.byte	'..\\MCU\\Mcu.c',0
	.byte	'TASKING VX-toolset for TriCore: C compiler',0
	.byte	'E:\\Project\\ESC3.0_SAIC\\boot\\0621_boot\\ESC30_SAIC_AP31_Boot0621ok\\ESC30_SAIC_AP31_Boot\\Debug\\',0
	.byte	12,1
	.word	.L5
	.byte	2
	.byte	'void',0,3
	.word	167
	.byte	4
	.byte	'__prof_adm',0,1,1,1
	.word	173
	.byte	5,1,3
	.word	197
	.byte	4
	.byte	'__codeptr',0,1,1,1
	.word	199
	.byte	0
	.sdecl	'.debug_abbrev',debug
	.sect	'.debug_abbrev'
.L4:
	.byte	1,17,1,3,8,37,8,27,8,19,15,128,70,12,16,6,0,0,2,59,0,3,8,0,0,3,15,0,73,19,0,0,4,22,0,3,8,58,15,59,15,57
	.byte	15,73,19,0,0,5,21,0,54,15,0,0,0
	.sdecl	'.debug_line',debug
	.sect	'.debug_line'
.L5:
	.word	.L7-.L6
.L6:
	.half	3
	.word	.L9-.L8
.L8:
	.byte	2,1,-4,9,10,0,1,1,1,1,0,0,0,1,0
	.byte	'..\\MCU\\Mcu.c',0,0,0,0,0
.L9:
.L7:

; ..\MCU\Mcu.c	     1  ///*============================================================================*/
; ..\MCU\Mcu.c	     2  ///*  Copyright (C) 2009-2011, iSOFT INFRASTRUCTURE SOFTWARE CO.,LTD.
; ..\MCU\Mcu.c	     3  // *
; ..\MCU\Mcu.c	     4  // *  All rights reserved. This software is iSOFT property. Duplication
; ..\MCU\Mcu.c	     5  // *  or disclosure without iSOFT written authorization is prohibited.
; ..\MCU\Mcu.c	     6  // *
; ..\MCU\Mcu.c	     7  // *  @file       <Mcu.c>
; ..\MCU\Mcu.c	     8  // *  @brief      <This is Mcu C file>
; ..\MCU\Mcu.c	     9  // *
; ..\MCU\Mcu.c	    10  // *  <Compiler: TASKING3.5    MCU:TC1782>
; ..\MCU\Mcu.c	    11  // *
; ..\MCU\Mcu.c	    12  // *  <AUTHOR>
; ..\MCU\Mcu.c	    13  // *  @date       <2014-5-30>
; ..\MCU\Mcu.c	    14  // */
; ..\MCU\Mcu.c	    15  ///*============================================================================*/
; ..\MCU\Mcu.c	    16  //
; ..\MCU\Mcu.c	    17  ///*=======[R E V I S I O N   H I S T O R Y]====================================*/
; ..\MCU\Mcu.c	    18  ///*  <VERSION>    <DATE>    <AUTHOR>    <REVISION LOG>
; ..\MCU\Mcu.c	    19  // *  V1.0.0       20140407   jjq        Initial version
; ..\MCU\Mcu.c	    20  // *  V1.0.1       20140530   jianan.liu fix it for bootloader
; ..\MCU\Mcu.c	    21  // */
; ..\MCU\Mcu.c	    22  ///*============================================================================*/
; ..\MCU\Mcu.c	    23  //
; ..\MCU\Mcu.c	    24  ///*=======[I N C L U D E S]====================================================*/
; ..\MCU\Mcu.c	    25  //#include "Mcu.h"
; ..\MCU\Mcu.c	    26  //#include "wdg.h"
; ..\MCU\Mcu.c	    27  //#include "mcal_src/IfxScu_reg.h"
; ..\MCU\Mcu.c	    28  //#include "mcal_src/IfxScu_bf.h"
; ..\MCU\Mcu.c	    29  //#include "mcal_src/IfxCpu_reg.h"
; ..\MCU\Mcu.c	    30  //#include "mcal_src/IfxCpu_bf.h"
; ..\MCU\Mcu.c	    31  //
; ..\MCU\Mcu.c	    32  //
; ..\MCU\Mcu.c	    33  //
; ..\MCU\Mcu.c	    34  //
; ..\MCU\Mcu.c	    35  //#define EXTCLK		(20000000)	/* external oscillator clock (20MHz) */
; ..\MCU\Mcu.c	    36  //
; ..\MCU\Mcu.c	    37  //static unsigned long system_GetIntClock(void);
; ..\MCU\Mcu.c	    38  //static unsigned long system_GetPllClock(void);
; ..\MCU\Mcu.c	    39  ///*=======[M A C R O S]========================================================*/
; ..\MCU\Mcu.c	    40  //#define SCU_RSTCON   (*((volatile uint32*)0xf0036058))
; ..\MCU\Mcu.c	    41  ///* 200/100 MHz @ 20MHz ext. clock */
; ..\MCU\Mcu.c	    42  //const TPllInitValue g_PllInitValue_200_100 =
; ..\MCU\Mcu.c	    43  //{
; ..\MCU\Mcu.c	    44  //	/* OSCCON,	PLLCON0,	PLLCON1,	CCUCON0,	CCUCON1,	CCUCON2 */
; ..\MCU\Mcu.c	    45  //	/* 0x000F0118, 0x01017600, 0x00020002, 0x52250101, 0x50012211, 0x40000202 */
; ..\MCU\Mcu.c	    46  //	/* OSCCON,	PLLCON0,	PLLCON1,	CCUCON0,	CCUCON1,	CCUCON2 */
; ..\MCU\Mcu.c	    47  //	0x00070118, 0x00013A00, 0x00000502, 0x02220122U, 0x58212215U, 0x00000002U
; ..\MCU\Mcu.c	    48  //};
; ..\MCU\Mcu.c	    49  //static Ifx_SCU * const pSCU = (Ifx_SCU *)&MODULE_SCU;
; ..\MCU\Mcu.c	    50  //
; ..\MCU\Mcu.c	    51  ///*=======[F U N C T I O N   I M P L E M E N T A T I O N S]====================*/
; ..\MCU\Mcu.c	    52  //unsigned long SYSTEM_GetStmClock(void)
; ..\MCU\Mcu.c	    53  //{
; ..\MCU\Mcu.c	    54  //	unsigned long frequency = system_GetIntClock();
; ..\MCU\Mcu.c	    55  //	unsigned long divider = pSCU->CCUCON1.B.STMDIV;
; ..\MCU\Mcu.c	    56  //	if (0 == divider)
; ..\MCU\Mcu.c	    57  //		return 0;
; ..\MCU\Mcu.c	    58  //	return (frequency / divider);
; ..\MCU\Mcu.c	    59  //}
; ..\MCU\Mcu.c	    60  //static unsigned long system_GetIntClock(void)
; ..\MCU\Mcu.c	    61  //{
; ..\MCU\Mcu.c	    62  //	unsigned long frequency = 0;
; ..\MCU\Mcu.c	    63  //	switch (pSCU->CCUCON0.B.CLKSEL)
; ..\MCU\Mcu.c	    64  //	{
; ..\MCU\Mcu.c	    65  //		default:
; ..\MCU\Mcu.c	    66  //		case 0:  /* back-up clock (typ. 100 MHz) */
; ..\MCU\Mcu.c	    67  //			frequency = 100000000ul;
; ..\MCU\Mcu.c	    68  //			break;
; ..\MCU\Mcu.c	    69  //		case 1:	 /* fPLL */
; ..\MCU\Mcu.c	    70  //			frequency = system_GetPllClock();
; ..\MCU\Mcu.c	    71  //			break;
; ..\MCU\Mcu.c	    72  //	}
; ..\MCU\Mcu.c	    73  //	return frequency;
; ..\MCU\Mcu.c	    74  //}
; ..\MCU\Mcu.c	    75  //static unsigned long system_GetPllClock(void)
; ..\MCU\Mcu.c	    76  //{
; ..\MCU\Mcu.c	    77  //	unsigned int frequency = EXTCLK;	/* fOSC */
; ..\MCU\Mcu.c	    78  //
; ..\MCU\Mcu.c	    79  //	Ifx_SCU_PLLSTAT pllstat = pSCU->PLLSTAT;
; ..\MCU\Mcu.c	    80  //	Ifx_SCU_PLLCON0 pllcon0 = pSCU->PLLCON0;
; ..\MCU\Mcu.c	    81  //	Ifx_SCU_PLLCON1 pllcon1 = pSCU->PLLCON1;
; ..\MCU\Mcu.c	    82  //
; ..\MCU\Mcu.c	    83  //	if (0 == (pllstat.B.VCOBYST))
; ..\MCU\Mcu.c	    84  //	{
; ..\MCU\Mcu.c	    85  //		if (0 == (pllstat.B.FINDIS))
; ..\MCU\Mcu.c	    86  //		{
; ..\MCU\Mcu.c	    87  //			/* normal mode */
; ..\MCU\Mcu.c	    88  //			frequency *= (pllcon0.B.NDIV + 1);		/* fOSC*N */
; ..\MCU\Mcu.c	    89  //			frequency /= (pllcon0.B.PDIV + 1);		/* .../P  */
; ..\MCU\Mcu.c	    90  //			frequency /= (pllcon1.B.K2DIV + 1);		/* .../K2 */
; ..\MCU\Mcu.c	    91  //		}
; ..\MCU\Mcu.c	    92  //		else	/* freerunning mode */
; ..\MCU\Mcu.c	    93  //		{
; ..\MCU\Mcu.c	    94  //			frequency = 800000000;		/* fVCOBASE 800 MHz (???) */
; ..\MCU\Mcu.c	    95  //			frequency /= (pllcon1.B.K2DIV + 1);		/* .../K2 */
; ..\MCU\Mcu.c	    96  //		}
; ..\MCU\Mcu.c	    97  //	}
; ..\MCU\Mcu.c	    98  //	else	/* prescaler mode */
; ..\MCU\Mcu.c	    99  //	{
; ..\MCU\Mcu.c	   100  //		frequency /= (pllcon1.B.K1DIV + 1);		/* fOSC/K1 */
; ..\MCU\Mcu.c	   101  //	}
; ..\MCU\Mcu.c	   102  //
; ..\MCU\Mcu.c	   103  //	return (unsigned long)frequency;
; ..\MCU\Mcu.c	   104  //}
; ..\MCU\Mcu.c	   105  //
; ..\MCU\Mcu.c	   106  ///******************************************************************************/
; ..\MCU\Mcu.c	   107  ///*
; ..\MCU\Mcu.c	   108  // * Brief               <Mcu_Init>
; ..\MCU\Mcu.c	   109  // * Sync/Async          <Synchronous>
; ..\MCU\Mcu.c	   110  // * Reentrancy          <Non-Reentrant>
; ..\MCU\Mcu.c	   111  // * Param-Name[in]      <ConfigPtr>
; ..\MCU\Mcu.c	   112  // * Param-Name[out]     <None>
; ..\MCU\Mcu.c	   113  // * Param-Name[in/out]  <None>
; ..\MCU\Mcu.c	   114  // * Return              <None>
; ..\MCU\Mcu.c	   115  // * PreCondition        <None>
; ..\MCU\Mcu.c	   116  // * CallByAPI           <APIName>
; ..\MCU\Mcu.c	   117  // */
; ..\MCU\Mcu.c	   118  ///******************************************************************************/
; ..\MCU\Mcu.c	   119  //void Mcu_Init(void)
; ..\MCU\Mcu.c	   120  //{
; ..\MCU\Mcu.c	   121  //	const PPllInitValue pPllInitValue = &g_PllInitValue_200_100;
; ..\MCU\Mcu.c	   122  //
; ..\MCU\Mcu.c	   123  //	unlock_safety_wdtcon();
; ..\MCU\Mcu.c	   124  //
; ..\MCU\Mcu.c	   125  //	pSCU->OSCCON.U = pPllInitValue->uiOSCCON;//0x000F0118
; ..\MCU\Mcu.c	   126  //
; ..\MCU\Mcu.c	   127  //	while (pSCU->CCUCON1.B.LCK);
; ..\MCU\Mcu.c	   128  //	pSCU->CCUCON1.U = pPllInitValue->uiCCUCON1 | (1 << IFX_SCU_CCUCON1_UP_OFF);
; ..\MCU\Mcu.c	   129  //
; ..\MCU\Mcu.c	   130  //	pSCU->PLLCON0.U = pPllInitValue->uiPLLCON0 | (1 << IFX_SCU_PLLCON0_SETFINDIS_OFF);
; ..\MCU\Mcu.c	   131  //
; ..\MCU\Mcu.c	   132  //	pSCU->PLLCON1.U = pPllInitValue->uiPLLCON1;	//0x00020002			/* set K1,K2 divider */    //	K2 =2 k3=0 k1= 2
; ..\MCU\Mcu.c	   133  //	pSCU->PLLCON0.U = pPllInitValue->uiPLLCON0 | (1 << IFX_SCU_PLLCON0_CLRFINDIS_OFF);
; ..\MCU\Mcu.c	   134  //
; ..\MCU\Mcu.c	   135  //	lock_safety_wdtcon();
; ..\MCU\Mcu.c	   136  //
; ..\MCU\Mcu.c	   137  //	while (0 == pSCU->PLLSTAT.B.VCOLOCK);
; ..\MCU\Mcu.c	   138  //
; ..\MCU\Mcu.c	   139  //	unlock_safety_wdtcon();
; ..\MCU\Mcu.c	   140  //
; ..\MCU\Mcu.c	   141  //	pSCU->CCUCON0.U = pPllInitValue->uiCCUCON0;
; ..\MCU\Mcu.c	   142  //	pSCU->CCUCON1.U = pPllInitValue->uiCCUCON1;
; ..\MCU\Mcu.c	   143  //	pSCU->CCUCON1.U = pPllInitValue->uiCCUCON1 | (1 << IFX_SCU_CCUCON1_UP_OFF);//0x50012211  Fcan=Fsource  Fstm=Fsource/2 Fgtm=Fsource/2 OSC is clock source for PLL
; ..\MCU\Mcu.c	   144  //	while (pSCU->CCUCON1.B.LCK);
; ..\MCU\Mcu.c	   145  //
; ..\MCU\Mcu.c	   146  //	pSCU->CCUCON2.U = pPllInitValue->uiCCUCON2;
; ..\MCU\Mcu.c	   147  //	pSCU->CCUCON2.U = pPllInitValue->uiCCUCON2 | (1 << IFX_SCU_CCUCON2_UP_OFF);//0x40000202 Fbbb
; ..\MCU\Mcu.c	   148  //	while (pSCU->CCUCON2.B.LCK);
; ..\MCU\Mcu.c	   149  //
; ..\MCU\Mcu.c	   150  //	pSCU->CCUCON0.U = pPllInitValue->uiCCUCON0 | 0x10000000u | (1 << IFX_SCU_CCUCON0_UP_OFF);//0x52250101    1010010001001010000000100000001  pll is used as clock Fsource
; ..\MCU\Mcu.c	   151  //	while (pSCU->CCUCON0.B.LCK);
; ..\MCU\Mcu.c	   152  //
; ..\MCU\Mcu.c	   153  //	lock_safety_wdtcon();
; ..\MCU\Mcu.c	   154  //
; ..\MCU\Mcu.c	   155  //	unlock_safety_wdtcon();
; ..\MCU\Mcu.c	   156  //	SCU_RSTCON = 0x00000140;
; ..\MCU\Mcu.c	   157  //	lock_safety_wdtcon();
; ..\MCU\Mcu.c	   158  //}
; ..\MCU\Mcu.c	   159  //
; ..\MCU\Mcu.c	   160  ///******************************************************************************/
; ..\MCU\Mcu.c	   161  ///*
; ..\MCU\Mcu.c	   162  // * Brief               <Mcu_Deinit>
; ..\MCU\Mcu.c	   163  // * Sync/Async          <Synchronous>
; ..\MCU\Mcu.c	   164  // * Reentrancy          <Non-Reentrant>
; ..\MCU\Mcu.c	   165  // * Param-Name[in]      <ConfigPtr>
; ..\MCU\Mcu.c	   166  // * Param-Name[out]     <None>
; ..\MCU\Mcu.c	   167  // * Param-Name[in/out]  <None>
; ..\MCU\Mcu.c	   168  // * Return              <None>
; ..\MCU\Mcu.c	   169  // * PreCondition        <None>
; ..\MCU\Mcu.c	   170  // * CallByAPI           <APIName>
; ..\MCU\Mcu.c	   171  // */
; ..\MCU\Mcu.c	   172  ///******************************************************************************/
; ..\MCU\Mcu.c	   173  ////void Mcu_Deinit(void)
; ..\MCU\Mcu.c	   174  ////{
; ..\MCU\Mcu.c	   175  ////
; ..\MCU\Mcu.c	   176  ////}
; ..\MCU\Mcu.c	   177  ///*=======[E N D   O F   F I L E]==============================================*/
; ..\MCU\Mcu.c	   178  //

	; Module end
